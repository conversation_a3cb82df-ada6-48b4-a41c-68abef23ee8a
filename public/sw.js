// Service Worker for PagesLab PWA
const CACHE_NAME = 'pageslab-v1.0.0'
const STATIC_CACHE = 'pageslab-static-v1.0.0'
const DYNAMIC_CACHE = 'pageslab-dynamic-v1.0.0'

// Files to cache immediately
const STATIC_FILES = [
  '/',
  '/manifest.json',
  '/icons/icon-192x192.png',
  '/icons/icon-512x512.png',
  // Add other critical static files
]

// API endpoints to cache
const API_CACHE_PATTERNS = [
  /\/api\/generate-website/,
  /\/api\/analytics/,
]

// Image cache patterns
const IMAGE_CACHE_PATTERNS = [
  /\.(jpg|jpeg|png|gif|webp|svg)$/i,
  /unsplash\.com/,
  /pexels\.com/,
]

// Install event - cache static files
self.addEventListener('install', (event) => {
  console.log('Service Worker installing...')
  
  event.waitUntil(
    caches.open(STATIC_CACHE)
      .then((cache) => {
        console.log('Caching static files')
        return cache.addAll(STATIC_FILES)
      })
      .then(() => {
        console.log('Static files cached successfully')
        return self.skipWaiting()
      })
      .catch((error) => {
        console.error('Failed to cache static files:', error)
      })
  )
})

// Activate event - clean up old caches
self.addEventListener('activate', (event) => {
  console.log('Service Worker activating...')
  
  event.waitUntil(
    caches.keys()
      .then((cacheNames) => {
        return Promise.all(
          cacheNames.map((cacheName) => {
            if (cacheName !== STATIC_CACHE && cacheName !== DYNAMIC_CACHE) {
              console.log('Deleting old cache:', cacheName)
              return caches.delete(cacheName)
            }
          })
        )
      })
      .then(() => {
        console.log('Service Worker activated')
        return self.clients.claim()
      })
  )
})

// Fetch event - handle requests with caching strategies
self.addEventListener('fetch', (event) => {
  const { request } = event
  const url = new URL(request.url)

  // Skip non-GET requests
  if (request.method !== 'GET') {
    return
  }

  // Skip chrome-extension and other non-http requests
  if (!url.protocol.startsWith('http')) {
    return
  }

  event.respondWith(handleRequest(request))
})

// Main request handler with different caching strategies
async function handleRequest(request) {
  const url = new URL(request.url)
  
  try {
    // Strategy 1: Cache First for static files
    if (isStaticFile(url)) {
      return await cacheFirst(request, STATIC_CACHE)
    }
    
    // Strategy 2: Network First for API calls
    if (isAPICall(url)) {
      return await networkFirst(request, DYNAMIC_CACHE)
    }
    
    // Strategy 3: Cache First for images
    if (isImage(url)) {
      return await cacheFirst(request, DYNAMIC_CACHE)
    }
    
    // Strategy 4: Stale While Revalidate for HTML pages
    if (isHTMLPage(request)) {
      return await staleWhileRevalidate(request, DYNAMIC_CACHE)
    }
    
    // Default: Network First
    return await networkFirst(request, DYNAMIC_CACHE)
    
  } catch (error) {
    console.error('Request handling error:', error)
    return await handleOffline(request)
  }
}

// Cache First strategy
async function cacheFirst(request, cacheName) {
  const cache = await caches.open(cacheName)
  const cachedResponse = await cache.match(request)
  
  if (cachedResponse) {
    return cachedResponse
  }
  
  try {
    const networkResponse = await fetch(request)
    if (networkResponse.ok) {
      cache.put(request, networkResponse.clone())
    }
    return networkResponse
  } catch (error) {
    console.error('Network request failed:', error)
    throw error
  }
}

// Network First strategy
async function networkFirst(request, cacheName) {
  const cache = await caches.open(cacheName)
  
  try {
    const networkResponse = await fetch(request)
    if (networkResponse.ok) {
      cache.put(request, networkResponse.clone())
    }
    return networkResponse
  } catch (error) {
    console.log('Network failed, trying cache:', error)
    const cachedResponse = await cache.match(request)
    if (cachedResponse) {
      return cachedResponse
    }
    throw error
  }
}

// Stale While Revalidate strategy
async function staleWhileRevalidate(request, cacheName) {
  const cache = await caches.open(cacheName)
  const cachedResponse = await cache.match(request)
  
  // Fetch in background to update cache
  const fetchPromise = fetch(request).then((networkResponse) => {
    if (networkResponse.ok) {
      cache.put(request, networkResponse.clone())
    }
    return networkResponse
  }).catch((error) => {
    console.error('Background fetch failed:', error)
  })
  
  // Return cached version immediately if available
  if (cachedResponse) {
    return cachedResponse
  }
  
  // Otherwise wait for network
  return await fetchPromise
}

// Handle offline scenarios
async function handleOffline(request) {
  const url = new URL(request.url)
  
  // Return offline page for HTML requests
  if (isHTMLPage(request)) {
    const cache = await caches.open(STATIC_CACHE)
    const offlinePage = await cache.match('/offline.html')
    if (offlinePage) {
      return offlinePage
    }
  }
  
  // Return placeholder for images
  if (isImage(url)) {
    const cache = await caches.open(STATIC_CACHE)
    const placeholder = await cache.match('/images/offline-placeholder.png')
    if (placeholder) {
      return placeholder
    }
  }
  
  // Return generic offline response
  return new Response(
    JSON.stringify({
      error: 'Offline',
      message: 'This feature requires an internet connection'
    }),
    {
      status: 503,
      statusText: 'Service Unavailable',
      headers: { 'Content-Type': 'application/json' }
    }
  )
}

// Helper functions to identify request types
function isStaticFile(url) {
  return url.pathname.includes('/static/') || 
         url.pathname.includes('/_next/static/') ||
         url.pathname.endsWith('.js') ||
         url.pathname.endsWith('.css') ||
         url.pathname.endsWith('.woff2') ||
         url.pathname.endsWith('.woff')
}

function isAPICall(url) {
  return url.pathname.startsWith('/api/') ||
         API_CACHE_PATTERNS.some(pattern => pattern.test(url.href))
}

function isImage(url) {
  return IMAGE_CACHE_PATTERNS.some(pattern => pattern.test(url.href))
}

function isHTMLPage(request) {
  return request.headers.get('accept')?.includes('text/html')
}

// Background sync for offline actions
self.addEventListener('sync', (event) => {
  console.log('Background sync triggered:', event.tag)
  
  if (event.tag === 'website-generation') {
    event.waitUntil(syncWebsiteGeneration())
  }
})

// Sync website generation when back online
async function syncWebsiteGeneration() {
  try {
    // Get pending website generations from IndexedDB
    const pendingGenerations = await getPendingGenerations()
    
    for (const generation of pendingGenerations) {
      try {
        const response = await fetch('/api/generate-website', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify(generation.data)
        })
        
        if (response.ok) {
          await removePendingGeneration(generation.id)
          await notifyUser('Website generated successfully!')
        }
      } catch (error) {
        console.error('Failed to sync generation:', error)
      }
    }
  } catch (error) {
    console.error('Background sync failed:', error)
  }
}

// Push notification handler
self.addEventListener('push', (event) => {
  console.log('Push notification received')
  
  const options = {
    body: event.data ? event.data.text() : 'New update available!',
    icon: '/icons/icon-192x192.png',
    badge: '/icons/badge-72x72.png',
    vibrate: [200, 100, 200],
    data: {
      url: '/'
    },
    actions: [
      {
        action: 'open',
        title: 'Open PagesLab'
      },
      {
        action: 'dismiss',
        title: 'Dismiss'
      }
    ]
  }
  
  event.waitUntil(
    self.registration.showNotification('PagesLab', options)
  )
})

// Notification click handler
self.addEventListener('notificationclick', (event) => {
  event.notification.close()
  
  if (event.action === 'open' || !event.action) {
    event.waitUntil(
      clients.openWindow(event.notification.data?.url || '/')
    )
  }
})

// Utility functions for IndexedDB operations
async function getPendingGenerations() {
  // Implementation would use IndexedDB to store offline data
  return []
}

async function removePendingGeneration(id) {
  // Implementation would remove from IndexedDB
  console.log('Removing pending generation:', id)
}

async function notifyUser(message) {
  // Show notification to user
  console.log('Notifying user:', message)
}
