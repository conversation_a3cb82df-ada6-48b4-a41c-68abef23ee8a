// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model User {
  id               String   @id @default(cuid())
  email            String   @unique
  name             String?
  password         String
  subscriptionTier String   @default("starter") @map("subscription_tier")
  createdAt        DateTime @default(now()) @map("created_at")
  updatedAt        DateTime @updatedAt @map("updated_at")

  websites Website[]

  @@index([email])
  @@index([createdAt])
  @@map("users")
}

model Website {
  id               String   @id @default(cuid())
  userId           String   @map("user_id")
  name             String
  businessProfile  Json     @map("business_profile")
  generatedContent Json     @map("generated_content")
  designSpec       Json     @map("design_spec")
  htmlOutput       String   @map("html_output") @db.Text
  cssOutput        String   @map("css_output") @db.Text
  subdomain        String   @unique
  isPublished      Boolean  @default(false) @map("is_published")
  createdAt        DateTime @default(now()) @map("created_at")
  updatedAt        DateTime @updatedAt @map("updated_at")

  user      User                    @relation(fields: [userId], references: [id], onDelete: Cascade)
  analytics GenerationAnalytics[]

  @@index([userId])
  @@index([subdomain])
  @@index([isPublished])
  @@index([createdAt])
  @@map("websites")
}

model CulturalTemplate {
  id           String   @id @default(cuid())
  region       String
  businessType String   @map("business_type")
  templateData Json     @map("template_data")
  createdAt    DateTime @default(now()) @map("created_at")

  @@index([region])
  @@index([businessType])
  @@index([region, businessType])
  @@map("cultural_templates")
}

model GenerationAnalytics {
  id                     String   @id @default(cuid())
  websiteId              String?  @map("website_id")
  generationTimeMs       Int      @map("generation_time_ms")
  userSatisfactionScore  Int?     @map("user_satisfaction_score")
  createdAt              DateTime @default(now()) @map("created_at")

  website Website? @relation(fields: [websiteId], references: [id])

  @@index([websiteId])
  @@index([createdAt])
  @@index([generationTimeMs])
  @@map("generation_analytics")
}