import { PrismaClient } from '@prisma/client'
import type { KenyanRegion, BusinessType } from '../src/types'

const prisma = new PrismaClient()

// Cultural templates data for different regions and business types
const culturalTemplatesData = [
  // Coastal Region Templates
  {
    region: 'COASTAL' as KenyanRegion,
    businessType: 'RESTAURANT' as BusinessType,
    templateData: {
      colorScheme: {
        primary: '#0891b2', // Ocean blue
        secondary: '#f97316', // Sunset orange
        accent: '#06b6d4', // Cyan
        background: '#f0f9ff',
        text: '#0c4a6e',
        muted: '#64748b'
      },
      culturalElements: [
        {
          type: 'COLOR',
          value: '#0891b2',
          description: 'Ocean blue representing the coastal waters'
        },
        {
          type: 'LANGUAGE',
          value: '<PERSON><PERSON><PERSON> chakula cha bahari',
          description: 'Welcome to seafood in Swahili'
        }
      ],
      businessPractices: {
        paymentMethods: [
          { type: 'MPESA', displayName: 'M-Pesa', isActive: true },
          { type: 'CASH', displayName: 'Cash', isActive: true },
          { type: 'AIRTEL_MONEY', displayName: 'Airtel Money', isActive: true }
        ],
        communicationPreferences: ['WhatsApp', 'Phone calls'],
        businessHours: 'Mon-Sun: 7AM-10PM',
        culturalConsiderations: ['Halal options available', 'Fresh seafood daily']
      }
    }
  },
  {
    region: 'COASTAL' as KenyanRegion,
    businessType: 'SALON' as BusinessType,
    templateData: {
      colorScheme: {
        primary: '#ec4899', // Pink
        secondary: '#f97316', // Orange
        accent: '#06b6d4', // Cyan
        background: '#fef7ff',
        text: '#831843',
        muted: '#64748b'
      },
      culturalElements: [
        {
          type: 'LANGUAGE',
          value: 'Salon ya urembo',
          description: 'Beauty salon in Swahili'
        }
      ],
      businessPractices: {
        paymentMethods: [
          { type: 'MPESA', displayName: 'M-Pesa', isActive: true },
          { type: 'CASH', displayName: 'Cash', isActive: true }
        ],
        communicationPreferences: ['WhatsApp', 'Phone calls'],
        businessHours: 'Mon-Sat: 8AM-7PM, Sun: 10AM-5PM',
        culturalConsiderations: ['Bridal packages available', 'Traditional hairstyles']
      }
    }
  },

  // Highland Region Templates
  {
    region: 'HIGHLAND' as KenyanRegion,
    businessType: 'RESTAURANT' as BusinessType,
    templateData: {
      colorScheme: {
        primary: '#16a34a', // Green
        secondary: '#dc2626', // Red
        accent: '#3b82f6', // Blue
        background: '#f7fee7',
        text: '#14532d',
        muted: '#64748b'
      },
      culturalElements: [
        {
          type: 'COLOR',
          value: '#16a34a',
          description: 'Highland green representing the fertile highlands'
        },
        {
          type: 'LANGUAGE',
          value: 'Nyama choma na ugali',
          description: 'Grilled meat and ugali - highland favorites'
        }
      ],
      businessPractices: {
        paymentMethods: [
          { type: 'MPESA', displayName: 'M-Pesa', isActive: true },
          { type: 'CASH', displayName: 'Cash', isActive: true },
          { type: 'CARD', displayName: 'Card Payment', isActive: true }
        ],
        communicationPreferences: ['WhatsApp', 'Phone calls', 'Email'],
        businessHours: 'Mon-Sun: 6AM-11PM',
        culturalConsiderations: ['Local vegetables', 'Traditional cooking methods']
      }
    }
  },
  {
    region: 'HIGHLAND' as KenyanRegion,
    businessType: 'TECH_SERVICES' as BusinessType,
    templateData: {
      colorScheme: {
        primary: '#6366f1', // Indigo
        secondary: '#8b5cf6', // Violet
        accent: '#06b6d4', // Cyan
        background: '#f8fafc',
        text: '#1e293b',
        muted: '#64748b'
      },
      culturalElements: [
        {
          type: 'LANGUAGE',
          value: 'Huduma za teknolojia',
          description: 'Technology services in Swahili'
        }
      ],
      businessPractices: {
        paymentMethods: [
          { type: 'MPESA', displayName: 'M-Pesa', isActive: true },
          { type: 'BANK_TRANSFER', displayName: 'Bank Transfer', isActive: true },
          { type: 'CARD', displayName: 'Card Payment', isActive: true }
        ],
        communicationPreferences: ['Email', 'WhatsApp', 'Phone calls'],
        businessHours: 'Mon-Fri: 8AM-6PM, Sat: 9AM-2PM',
        culturalConsiderations: ['Remote support available', 'Local language support']
      }
    }
  },

  // Western Region Templates
  {
    region: 'WESTERN' as KenyanRegion,
    businessType: 'AGRICULTURE' as BusinessType,
    templateData: {
      colorScheme: {
        primary: '#65a30d', // Lime green
        secondary: '#f59e0b', // Amber
        accent: '#16a34a', // Green
        background: '#f7fee7',
        text: '#365314',
        muted: '#64748b'
      },
      culturalElements: [
        {
          type: 'COLOR',
          value: '#65a30d',
          description: 'Agricultural green representing fertile farmlands'
        },
        {
          type: 'LANGUAGE',
          value: 'Kilimo na mifugo',
          description: 'Agriculture and livestock in Swahili'
        }
      ],
      businessPractices: {
        paymentMethods: [
          { type: 'MPESA', displayName: 'M-Pesa', isActive: true },
          { type: 'CASH', displayName: 'Cash', isActive: true },
          { type: 'BANK_TRANSFER', displayName: 'Bank Transfer', isActive: true }
        ],
        communicationPreferences: ['Phone calls', 'WhatsApp'],
        businessHours: 'Mon-Sat: 6AM-6PM',
        culturalConsiderations: ['Seasonal availability', 'Organic farming methods']
      }
    }
  },
  {
    region: 'WESTERN' as KenyanRegion,
    businessType: 'SHOP' as BusinessType,
    templateData: {
      colorScheme: {
        primary: '#dc2626', // Red
        secondary: '#16a34a', // Green
        accent: '#f59e0b', // Amber
        background: '#fef2f2',
        text: '#7f1d1d',
        muted: '#64748b'
      },
      culturalElements: [
        {
          type: 'LANGUAGE',
          value: 'Duka la kila kitu',
          description: 'Shop for everything in Swahili'
        }
      ],
      businessPractices: {
        paymentMethods: [
          { type: 'MPESA', displayName: 'M-Pesa', isActive: true },
          { type: 'CASH', displayName: 'Cash', isActive: true },
          { type: 'AIRTEL_MONEY', displayName: 'Airtel Money', isActive: true }
        ],
        communicationPreferences: ['WhatsApp', 'Phone calls'],
        businessHours: 'Mon-Sun: 7AM-9PM',
        culturalConsiderations: ['Credit available for regulars', 'Local products']
      }
    }
  },

  // Northern Region Templates
  {
    region: 'NORTHERN' as KenyanRegion,
    businessType: 'TRANSPORT' as BusinessType,
    templateData: {
      colorScheme: {
        primary: '#f97316', // Orange
        secondary: '#dc2626', // Red
        accent: '#eab308', // Yellow
        background: '#fff7ed',
        text: '#9a3412',
        muted: '#64748b'
      },
      culturalElements: [
        {
          type: 'COLOR',
          value: '#f97316',
          description: 'Desert orange representing the northern landscape'
        },
        {
          type: 'LANGUAGE',
          value: 'Usafiri wa haraka',
          description: 'Fast transport in Swahili'
        }
      ],
      businessPractices: {
        paymentMethods: [
          { type: 'MPESA', displayName: 'M-Pesa', isActive: true },
          { type: 'CASH', displayName: 'Cash', isActive: true }
        ],
        communicationPreferences: ['Phone calls', 'WhatsApp'],
        businessHours: 'Mon-Sun: 5AM-10PM',
        culturalConsiderations: ['Long distance routes', 'Reliable service']
      }
    }
  },
  {
    region: 'NORTHERN' as KenyanRegion,
    businessType: 'HOTEL' as BusinessType,
    templateData: {
      colorScheme: {
        primary: '#dc2626', // Red
        secondary: '#f97316', // Orange
        accent: '#eab308', // Yellow
        background: '#fef2f2',
        text: '#7f1d1d',
        muted: '#64748b'
      },
      culturalElements: [
        {
          type: 'LANGUAGE',
          value: 'Hoteli ya utalii',
          description: 'Tourism hotel in Swahili'
        }
      ],
      businessPractices: {
        paymentMethods: [
          { type: 'MPESA', displayName: 'M-Pesa', isActive: true },
          { type: 'CASH', displayName: 'Cash', isActive: true },
          { type: 'CARD', displayName: 'Card Payment', isActive: true },
          { type: 'BANK_TRANSFER', displayName: 'Bank Transfer', isActive: true }
        ],
        communicationPreferences: ['Phone calls', 'WhatsApp', 'Email'],
        businessHours: '24/7 Service',
        culturalConsiderations: ['Cultural tours available', 'Local cuisine']
      }
    }
  }
]

async function main() {
  console.log('🌱 Starting database seed...')

  // Clear existing cultural templates
  await prisma.culturalTemplate.deleteMany()
  console.log('🗑️  Cleared existing cultural templates')

  // Insert cultural templates
  for (const template of culturalTemplatesData) {
    await prisma.culturalTemplate.create({
      data: template
    })
  }

  console.log(`✅ Created ${culturalTemplatesData.length} cultural templates`)

  // Create a test user for development
  const testUser = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      email: '<EMAIL>',
      subscriptionTier: 'premium'
    }
  })

  console.log('👤 Created test user:', testUser.email)

  console.log('🎉 Database seeded successfully!')
}

main()
  .catch((e) => {
    console.error('❌ Error seeding database:', e)
    process.exit(1)
  })
  .finally(async () => {
    await prisma.$disconnect()
  })