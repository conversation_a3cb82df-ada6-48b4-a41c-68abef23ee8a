{"name": "pageslab", "version": "0.1.0", "private": true, "engines": {"node": ">=18.17.0"}, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:unit": "jest --testPathPattern=__tests__", "test:integration": "jest --testPathPattern=integration", "test:e2e": "jest --testPathPattern=e2e", "test:ci": "jest --ci --coverage --watchAll=false", "db:generate": "prisma generate", "db:push": "prisma db push", "db:migrate": "prisma migrate dev", "db:studio": "prisma studio"}, "dependencies": {"@headlessui/react": "^2.2.0", "@prisma/client": "^5.22.0", "autoprefixer": "^10.4.21", "bcryptjs": "^2.4.3", "clsx": "^2.1.1", "framer-motion": "^11.11.17", "jsonwebtoken": "^9.0.2", "lucide-react": "^0.525.0", "next": "^15.0.3", "openai": "^4.67.3", "prisma": "^5.22.0", "react": "^18", "react-dom": "^18", "redis": "^4.7.0", "sharp": "^0.33.5", "tailwind-merge": "^3.3.1", "zod": "^3.25.76", "zustand": "^5.0.2"}, "devDependencies": {"@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.0.1", "@types/bcryptjs": "^2.4.6", "@types/jest": "^29.5.14", "@types/jsonwebtoken": "^9.0.10", "@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "eslint": "^8", "eslint-config-next": "^15.0.3", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "postcss": "^8", "tailwindcss": "^3.4.1", "typescript": "^5"}}