#!/usr/bin/env tsx

/**
 * Migration script to add existing users to Prisma database with proper passwords
 * Run with: npx tsx scripts/migrate-users.ts
 */

import bcrypt from 'bcryptjs'
import { prisma } from '../src/lib/db'

const existingUsers = [
  {
    email: '<EMAIL>',
    password: 'password',
    name: 'Demo User'
  },
  {
    email: '<EMAIL>', 
    password: 'capt4ain',
    name: 'Captain <PERSON><PERSON>'
  },
  {
    email: '<EMAIL>',
    password: 'capt4ain', 
    name: '<PERSON> <PERSON>'
  }
]

async function migrateUsers() {
  console.log('🔄 Starting user migration...')
  
  for (const userData of existingUsers) {
    try {
      // Check if user already exists
      const existingUser = await prisma.user.findUnique({
        where: { email: userData.email }
      })
      
      if (existingUser) {
        // Update existing user with password and name if missing
        const hashedPassword = await bcrypt.hash(userData.password, 10)
        
        await prisma.user.update({
          where: { email: userData.email },
          data: {
            password: hashedPassword,
            name: userData.name
          }
        })
        
        console.log(`✅ Updated user: ${userData.email}`)
      } else {
        // Create new user
        const hashedPassword = await bcrypt.hash(userData.password, 10)
        
        await prisma.user.create({
          data: {
            email: userData.email,
            password: hashedPassword,
            name: userData.name,
            subscriptionTier: 'starter'
          }
        })
        
        console.log(`✅ Created user: ${userData.email}`)
      }
    } catch (error) {
      console.error(`❌ Failed to migrate user ${userData.email}:`, error)
    }
  }
  
  console.log('🎉 User migration completed!')
}

// Run migration
migrateUsers()
  .catch(console.error)
  .finally(() => prisma.$disconnect())
