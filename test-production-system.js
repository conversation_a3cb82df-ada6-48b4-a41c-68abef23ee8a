#!/usr/bin/env node

/**
 * Test script to verify the production system is working correctly
 * This will test different business types with production mode enabled
 */

const testBusinesses = [
  {
    name: "Modern Restaurant",
    description: "A modern Kenyan restaurant in Nairobi serving traditional and fusion cuisine with outdoor seating and live music",
    expectedSectors: ["restaurant", "hospitality"]
  },
  {
    name: "Beauty Salon",
    description: "Professional hair and beauty salon in Mombasa offering braiding, styling, and beauty treatments for women",
    expectedSectors: ["beauty", "salon"]
  },
  {
    name: "Tech Startup",
    description: "Software development company in Nairobi creating mobile apps and web solutions for African businesses",
    expectedSectors: ["technology", "software"]
  }
]

async function testProductionSystem() {
  console.log('🧪 Testing Production System...\n')
  
  for (const business of testBusinesses) {
    console.log(`\n📋 Testing: ${business.name}`)
    console.log(`📝 Description: ${business.description}`)
    
    try {
      const response = await fetch('http://localhost:3002/api/generate-website', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          businessDescription: business.description,
          includeImages: true,
          integrations: {
            whatsapp: true,
            googleMaps: true,
            contactForm: true
          },
          useProductionMode: true
        })
      })
      
      const result = await response.json()
      
      if (result.success) {
        console.log('✅ Generation successful!')
        console.log(`📊 Business Type: ${result.data.businessProfile?.type || 'Unknown'}`)
        console.log(`📏 HTML Length: ${result.data.website?.html?.length || 0} characters`)
        console.log(`🎨 CSS Length: ${result.data.website?.css?.length || 0} characters`)
        console.log(`🏭 Production Mode: ${result.data.website?.isProduction ? 'YES' : 'NO'}`)
        
        // Check for production quality indicators
        const html = result.data.website?.html || ''
        const css = result.data.website?.css || ''
        
        const qualityChecks = {
          'Has Navigation': html.includes('<nav') || html.includes('navigation'),
          'Has Hero Section': html.includes('hero') || html.includes('banner'),
          'Has Services': html.includes('service') || html.includes('offering'),
          'Has Contact': html.includes('contact') || html.includes('phone'),
          'Has Responsive CSS': css.includes('@media') || css.includes('responsive'),
          'Has Modern CSS': css.includes('grid') || css.includes('flexbox') || css.includes('flex'),
          'Has Animations': css.includes('transition') || css.includes('animation'),
          'Has Custom Properties': css.includes('--') || css.includes('var(')
        }
        
        console.log('\n🔍 Quality Checks:')
        Object.entries(qualityChecks).forEach(([check, passed]) => {
          console.log(`  ${passed ? '✅' : '❌'} ${check}`)
        })
        
        const passedChecks = Object.values(qualityChecks).filter(Boolean).length
        const totalChecks = Object.keys(qualityChecks).length
        const qualityScore = Math.round((passedChecks / totalChecks) * 100)
        
        console.log(`\n📊 Quality Score: ${qualityScore}% (${passedChecks}/${totalChecks} checks passed)`)
        
        if (qualityScore >= 80) {
          console.log('🎉 HIGH QUALITY - Production ready!')
        } else if (qualityScore >= 60) {
          console.log('⚠️ MEDIUM QUALITY - Needs improvement')
        } else {
          console.log('❌ LOW QUALITY - Major issues detected')
        }
        
      } else {
        console.log('❌ Generation failed:', result.error)
      }
      
    } catch (error) {
      console.log('❌ Network error:', error.message)
    }
    
    console.log('\n' + '='.repeat(80))
  }
}

// Run the test
testProductionSystem().catch(console.error)
