# PagesLab - AI Website Generator for Kenyan Businesses

PagesLab is a revolutionary AI-powered website generator specifically designed for Kenyan businesses. Unlike traditional template-based builders, PagesLab generates unique HTML/CSS websites tailored to each business, incorporating Kenyan cultural elements, local business practices, and regional design aesthetics.

## Features

- 🤖 **AI-Powered Generation**: Create unique websites in under 60 seconds
- 🇰🇪 **Cultural Intelligence**: Deep understanding of Kenyan business culture
- 📱 **Mobile-First**: Optimized for Kenyan mobile users and 3G networks
- 💰 **M-Pesa Integration**: Built-in support for local payment methods
- 🌍 **Multi-Language**: English and Swahili support
- 🎨 **Regional Styles**: Coastal, Highland, Western, and Northern design themes

## Tech Stack

- **Frontend**: Next.js 14, TypeScript, Tailwind CSS
- **Backend**: Next.js API Routes, Prisma ORM
- **Database**: PostgreSQL
- **AI**: OpenAI GPT-4
- **Styling**: Tailwind CSS with custom Kenyan design system
- **Testing**: Jest, React Testing Library

## Getting Started

### Prerequisites

- Node.js 18+ 
- PostgreSQL database
- OpenAI API key

### Installation

1. Clone the repository
2. Install dependencies:
   ```bash
   npm install
   ```

3. Set up environment variables:
   ```bash
   cp .env.example .env.local
   ```
   Fill in your database URL, OpenAI API key, and other required variables.

4. Set up the database:
   ```bash
   npm run db:generate
   npm run db:push
   ```

5. Run the development server:
   ```bash
   npm run dev
   ```

6. Open [http://localhost:3000](http://localhost:3000) in your browser.

## Development

### Available Scripts

- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm run start` - Start production server
- `npm run lint` - Run ESLint
- `npm run test` - Run tests
- `npm run test:watch` - Run tests in watch mode
- `npm run db:generate` - Generate Prisma client
- `npm run db:push` - Push schema to database
- `npm run db:migrate` - Run database migrations
- `npm run db:studio` - Open Prisma Studio

### Project Structure

```
src/
├── app/                 # Next.js app directory
├── components/          # React components
├── lib/                 # Utility functions and configurations
├── types/               # TypeScript type definitions
└── styles/              # Global styles

prisma/
├── schema.prisma        # Database schema
└── migrations/          # Database migrations
```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Run tests and ensure they pass
6. Submit a pull request

## License

This project is licensed under the MIT License.