#!/bin/bash

echo "Setting up PostgreSQL with Docker..."

# Remove existing container if it exists
docker rm -f pageslab-postgres 2>/dev/null || true

# Start PostgreSQL container
docker run --name pageslab-postgres \
  -e POSTGRES_DB=pageslab \
  -e POSTGRES_USER=pageslab_user \
  -e POSTGRES_PASSWORD=pageslab_password \
  -p 5435:5432 \
  -d postgres:15

echo "Waiting for PostgreSQL to start..."
sleep 10

# Check if container is running
if docker ps | grep -q pageslab-postgres; then
    echo "✅ PostgreSQL container is running"
    
    # Test connection
    docker exec pageslab-postgres pg_isready -U pageslab_user -d pageslab
    
    if [ $? -eq 0 ]; then
        echo "✅ Database is ready"
        echo "Now run: npm run db:push"
    else
        echo "❌ Database is not ready yet, wait a few more seconds"
    fi
else
    echo "❌ Failed to start PostgreSQL container"
    echo "Container logs:"
    docker logs pageslab-postgres
fi