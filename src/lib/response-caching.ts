/**
 * Response Caching System for PagesLab API
 * Implements intelligent caching for API responses
 */

import { NextRequest, NextResponse } from 'next/server'

export interface CacheConfig {
  ttl: number // Time to live in seconds
  maxSize?: number // Maximum cache size in MB
  keyGenerator?: (request: NextRequest) => string
  shouldCache?: (request: NextRequest, response: NextResponse) => boolean
  tags?: string[] // Cache tags for invalidation
}

export interface CacheEntry {
  data: any
  headers: Record<string, string>
  status: number
  timestamp: number
  ttl: number
  tags: string[]
  size: number // Size in bytes
}

export interface CacheStats {
  hits: number
  misses: number
  size: number // Current cache size in bytes
  entries: number
  hitRate: number
}

class ResponseCache {
  private cache = new Map<string, CacheEntry>()
  private stats: CacheStats = {
    hits: 0,
    misses: 0,
    size: 0,
    entries: 0,
    hitRate: 0
  }
  private maxSize: number
  private cleanupInterval: NodeJS.Timeout | null = null

  constructor(maxSizeMB: number = 100) {
    this.maxSize = maxSizeMB * 1024 * 1024 // Convert to bytes
    
    // Start cleanup interval
    this.cleanupInterval = setInterval(() => {
      this.cleanup()
    }, 5 * 60 * 1000) // Cleanup every 5 minutes
  }

  /**
   * Generate cache key from request
   */
  private generateKey(request: NextRequest, keyGenerator?: (req: NextRequest) => string): string {
    if (keyGenerator) {
      return keyGenerator(request)
    }

    const url = new URL(request.url)
    const method = request.method
    const searchParams = url.searchParams.toString()
    
    // Include relevant headers in cache key
    const relevantHeaders = [
      'accept',
      'accept-language',
      'x-api-version',
      'authorization'
    ]
    
    const headerString = relevantHeaders
      .map(header => `${header}:${request.headers.get(header) || ''}`)
      .join('|')

    return `${method}:${url.pathname}:${searchParams}:${headerString}`
  }

  /**
   * Calculate response size
   */
  private calculateSize(data: any, headers: Record<string, string>): number {
    const dataSize = JSON.stringify(data).length * 2 // UTF-16 encoding
    const headersSize = JSON.stringify(headers).length * 2
    return dataSize + headersSize
  }

  /**
   * Check if entry is expired
   */
  private isExpired(entry: CacheEntry): boolean {
    return Date.now() - entry.timestamp > entry.ttl * 1000
  }

  /**
   * Remove expired entries and enforce size limits
   */
  private cleanup(): void {
    const now = Date.now()
    let removedSize = 0

    // Remove expired entries
    for (const [key, entry] of this.cache.entries()) {
      if (this.isExpired(entry)) {
        removedSize += entry.size
        this.cache.delete(key)
      }
    }

    // If still over size limit, remove oldest entries
    if (this.stats.size - removedSize > this.maxSize) {
      const entries = Array.from(this.cache.entries())
        .sort(([, a], [, b]) => a.timestamp - b.timestamp)

      for (const [key, entry] of entries) {
        if (this.stats.size - removedSize <= this.maxSize) break
        
        removedSize += entry.size
        this.cache.delete(key)
      }
    }

    // Update stats
    this.stats.size -= removedSize
    this.stats.entries = this.cache.size
    this.updateHitRate()
  }

  /**
   * Update hit rate calculation
   */
  private updateHitRate(): void {
    const total = this.stats.hits + this.stats.misses
    this.stats.hitRate = total > 0 ? this.stats.hits / total : 0
  }

  /**
   * Get cached response
   */
  get(request: NextRequest, config: CacheConfig): CacheEntry | null {
    const key = this.generateKey(request, config.keyGenerator)
    const entry = this.cache.get(key)

    if (!entry) {
      this.stats.misses++
      this.updateHitRate()
      return null
    }

    if (this.isExpired(entry)) {
      this.cache.delete(key)
      this.stats.size -= entry.size
      this.stats.entries--
      this.stats.misses++
      this.updateHitRate()
      return null
    }

    this.stats.hits++
    this.updateHitRate()
    return entry
  }

  /**
   * Set cached response
   */
  set(request: NextRequest, response: NextResponse, config: CacheConfig): void {
    const key = this.generateKey(request, config.keyGenerator)
    
    // Extract response data
    const data = response.body
    const headers: Record<string, string> = {}
    response.headers.forEach((value, key) => {
      headers[key] = value
    })

    const size = this.calculateSize(data, headers)
    
    // Check if we should cache this response
    if (config.shouldCache && !config.shouldCache(request, response)) {
      return
    }

    // Don't cache if response is too large
    if (size > this.maxSize * 0.1) { // Don't cache responses larger than 10% of max size
      return
    }

    const entry: CacheEntry = {
      data,
      headers,
      status: response.status,
      timestamp: Date.now(),
      ttl: config.ttl,
      tags: config.tags || [],
      size
    }

    // Remove old entry if exists
    const oldEntry = this.cache.get(key)
    if (oldEntry) {
      this.stats.size -= oldEntry.size
    }

    // Add new entry
    this.cache.set(key, entry)
    this.stats.size += size
    this.stats.entries = this.cache.size

    // Cleanup if necessary
    if (this.stats.size > this.maxSize) {
      this.cleanup()
    }
  }

  /**
   * Invalidate cache by tags
   */
  invalidateByTags(tags: string[]): number {
    let removedCount = 0
    let removedSize = 0

    for (const [key, entry] of this.cache.entries()) {
      if (entry.tags.some(tag => tags.includes(tag))) {
        this.cache.delete(key)
        removedSize += entry.size
        removedCount++
      }
    }

    this.stats.size -= removedSize
    this.stats.entries = this.cache.size

    return removedCount
  }

  /**
   * Clear all cache
   */
  clear(): void {
    this.cache.clear()
    this.stats = {
      hits: 0,
      misses: 0,
      size: 0,
      entries: 0,
      hitRate: 0
    }
  }

  /**
   * Get cache statistics
   */
  getStats(): CacheStats {
    return { ...this.stats }
  }

  /**
   * Destroy cache instance
   */
  destroy(): void {
    if (this.cleanupInterval) {
      clearInterval(this.cleanupInterval)
    }
    this.clear()
  }
}

// Singleton cache instance
const responseCache = new ResponseCache(100) // 100MB cache

// Default cache configurations
export const CACHE_CONFIGS = {
  // Static content - cache for 1 hour
  STATIC: {
    ttl: 3600,
    tags: ['static'] as string[],
    shouldCache: (req: NextRequest, res: NextResponse) => res.status === 200
  },

  // Business analysis - cache for 30 minutes
  BUSINESS_ANALYSIS: {
    ttl: 1800,
    tags: ['business', 'analysis'] as string[],
    shouldCache: (req: NextRequest, res: NextResponse) => res.status === 200
  },

  // Design generation - cache for 15 minutes
  DESIGN_GENERATION: {
    ttl: 900,
    tags: ['design', 'generation'] as string[],
    shouldCache: (req: NextRequest, res: NextResponse) => res.status === 200
  },

  // Content generation - cache for 10 minutes
  CONTENT_GENERATION: {
    ttl: 600,
    tags: ['content', 'generation'] as string[],
    shouldCache: (req: NextRequest, res: NextResponse) => res.status === 200
  },

  // Analytics - cache for 5 minutes
  ANALYTICS: {
    ttl: 300,
    tags: ['analytics'] as string[],
    shouldCache: (req: NextRequest, res: NextResponse) => res.status === 200
  },

  // User data - cache for 2 minutes
  USER_DATA: {
    ttl: 120,
    tags: ['user'] as string[],
    shouldCache: (req: NextRequest, res: NextResponse) => res.status === 200
  }
} as const

/**
 * Cache middleware
 */
export function createCacheMiddleware(config: CacheConfig) {
  return async (request: NextRequest): Promise<NextResponse | null> => {
    // Only cache GET requests
    if (request.method !== 'GET') {
      return null
    }

    // Check for cache hit
    const cachedEntry = responseCache.get(request, config)
    if (cachedEntry) {
      const response = new NextResponse(JSON.stringify(cachedEntry.data), {
        status: cachedEntry.status,
        headers: cachedEntry.headers
      })
      
      // Add cache headers
      response.headers.set('X-Cache', 'HIT')
      response.headers.set('X-Cache-Timestamp', new Date(cachedEntry.timestamp).toISOString())
      
      return response
    }

    // Cache miss - continue to handler
    return null
  }
}

/**
 * Cache response after handler execution
 */
export function cacheResponse(request: NextRequest, response: NextResponse, config: CacheConfig): NextResponse {
  // Only cache successful GET requests
  if (request.method === 'GET' && response.status === 200) {
    responseCache.set(request, response, config)
  }

  // Add cache headers
  response.headers.set('X-Cache', 'MISS')
  response.headers.set('X-Cache-TTL', config.ttl.toString())
  
  return response
}

/**
 * Invalidate cache by tags
 */
export function invalidateCache(tags: string[]): number {
  return responseCache.invalidateByTags(tags)
}

/**
 * Get cache statistics
 */
export function getCacheStats(): CacheStats {
  return responseCache.getStats()
}

/**
 * Clear all cache
 */
export function clearCache(): void {
  responseCache.clear()
}

export { responseCache }
