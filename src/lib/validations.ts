import { z } from 'zod'
import type { 
  BusinessType, 
  KenyanRegion, 
  Language, 
  SubscriptionTier 
} from '@/types'

// Enum validations
export const BusinessTypeSchema = z.enum([
  'SALON',
  'RESTAURANT', 
  'SHOP',
  'CLIN<PERSON>',
  'GARAGE',
  'HOTEL',
  '<PERSON>HO<PERSON>',
  'CHURCH',
  'CONSULTANCY',
  'TECH_SERVICES',
  'AGRICULTURE',
  'TRANSPORT',
  'CONSTRUCTION',
  'ENTERTAINMENT',
  'OTHER'
])

export const KenyanRegionSchema = z.enum([
  'COASTAL',
  'HIGHLAND', 
  'WESTERN',
  'NORTHERN',
  'CENTRAL',
  'EASTERN'
])

export const LanguageSchema = z.enum([
  'ENGLISH',
  'SWAHILI',
  'KIKUYU',
  'LUO',
  'LUHYA',
  'KAMBA'
])

export const SubscriptionTierSchema = z.enum(['starter', 'premium'])

// Location validation
export const KenyanLocationSchema = z.object({
  county: z.string().min(1, 'County is required'),
  area: z.string().min(1, 'Area is required'),
  region: KenyanRegionSchema,
  coordinates: z.object({
    lat: z.number().min(-90).max(90),
    lng: z.number().min(-180).max(180)
  }).optional()
})

// Contact information validation
export const ContactInfoSchema = z.object({
  phone: z.string()
    .regex(/^\+254[0-9]{9}$/, 'Phone must be in format +254XXXXXXXXX')
    .or(z.string().regex(/^0[0-9]{9}$/, 'Phone must be in format 0XXXXXXXXX')),
  whatsapp: z.string()
    .regex(/^\+254[0-9]{9}$/, 'WhatsApp must be in format +254XXXXXXXXX')
    .optional(),
  email: z.string().email('Invalid email format').optional(),
  address: z.string().min(5, 'Address must be at least 5 characters'),
  businessHours: z.string().optional(),
  website: z.string().url('Invalid website URL').optional()
})

// Payment method validation
export const PaymentMethodSchema = z.object({
  type: z.enum(['MPESA', 'AIRTEL_MONEY', 'TKASH', 'BANK_TRANSFER', 'CASH', 'CARD']),
  displayName: z.string().min(1, 'Display name is required'),
  icon: z.string().optional(),
  isActive: z.boolean().default(true)
})

// Cultural element validation
export const CulturalElementSchema = z.object({
  type: z.enum(['COLOR', 'PATTERN', 'IMAGERY', 'LANGUAGE', 'SYMBOL']),
  value: z.string().min(1, 'Value is required'),
  description: z.string().min(1, 'Description is required')
})

// Business practices validation
export const BusinessPracticesSchema = z.object({
  paymentMethods: z.array(PaymentMethodSchema).min(1, 'At least one payment method required'),
  communicationPreferences: z.array(z.string()).default([]),
  businessHours: z.string().default('Mon-Fri: 8AM-6PM, Sat: 9AM-4PM'),
  culturalConsiderations: z.array(z.string()).default([])
})

// Cultural context validation
export const CulturalContextSchema = z.object({
  region: KenyanRegionSchema,
  language: LanguageSchema.default('ENGLISH'),
  businessPractices: BusinessPracticesSchema,
  culturalElements: z.array(CulturalElementSchema).default([])
})

// Business profile validation
export const BusinessProfileSchema = z.object({
  name: z.string().min(1, 'Business name is required').max(100, 'Business name too long'),
  type: BusinessTypeSchema,
  location: KenyanLocationSchema,
  services: z.array(z.string().min(1)).min(1, 'At least one service required').max(20, 'Too many services'),
  description: z.string().min(10, 'Description must be at least 10 characters').max(500, 'Description too long'),
  targetAudience: z.string().min(5, 'Target audience must be at least 5 characters').max(200, 'Target audience too long'),
  contactInfo: ContactInfoSchema,
  culturalContext: CulturalContextSchema
})

// Service validation
export const ServiceSchema = z.object({
  name: z.string().min(1, 'Service name is required').max(100, 'Service name too long'),
  description: z.string().min(5, 'Service description must be at least 5 characters').max(300, 'Service description too long'),
  price: z.string().optional(),
  duration: z.string().optional()
})

// Call to action validation
export const CallToActionSchema = z.object({
  text: z.string().min(1, 'CTA text is required').max(50, 'CTA text too long'),
  type: z.enum(['PRIMARY', 'SECONDARY', 'CONTACT', 'WHATSAPP']),
  action: z.string().min(1, 'CTA action is required')
})

// Contact method validation
export const ContactMethodSchema = z.object({
  type: z.enum(['PHONE', 'WHATSAPP', 'EMAIL', 'SMS']),
  value: z.string().min(1, 'Contact value is required'),
  label: z.string().min(1, 'Contact label is required'),
  icon: z.string().min(1, 'Contact icon is required')
})

// Location info validation
export const LocationInfoSchema = z.object({
  address: z.string().min(5, 'Address must be at least 5 characters'),
  googleMapsUrl: z.string().url('Invalid Google Maps URL').optional(),
  directions: z.string().optional(),
  landmarks: z.array(z.string()).default([])
})

// Contact section validation
export const ContactSectionSchema = z.object({
  title: z.string().min(1, 'Contact section title is required'),
  description: z.string().min(5, 'Contact section description must be at least 5 characters'),
  methods: z.array(ContactMethodSchema).min(1, 'At least one contact method required'),
  location: LocationInfoSchema.optional()
})

// Generated content validation
export const GeneratedContentSchema = z.object({
  headline: z.string().min(5, 'Headline must be at least 5 characters').max(100, 'Headline too long'),
  subheadline: z.string().min(5, 'Subheadline must be at least 5 characters').max(200, 'Subheadline too long'),
  aboutSection: z.string().min(20, 'About section must be at least 20 characters').max(1000, 'About section too long'),
  services: z.array(ServiceSchema).min(1, 'At least one service required').max(20, 'Too many services'),
  callToActions: z.array(CallToActionSchema).min(1, 'At least one CTA required').max(5, 'Too many CTAs'),
  contactSection: ContactSectionSchema
})

// Color scheme validation
export const ColorSchemeSchema = z.object({
  primary: z.string().regex(/^#[0-9A-Fa-f]{6}$/, 'Primary color must be a valid hex color'),
  secondary: z.string().regex(/^#[0-9A-Fa-f]{6}$/, 'Secondary color must be a valid hex color'),
  accent: z.string().regex(/^#[0-9A-Fa-f]{6}$/, 'Accent color must be a valid hex color'),
  background: z.string().regex(/^#[0-9A-Fa-f]{6}$/, 'Background color must be a valid hex color'),
  text: z.string().regex(/^#[0-9A-Fa-f]{6}$/, 'Text color must be a valid hex color'),
  muted: z.string().regex(/^#[0-9A-Fa-f]{6}$/, 'Muted color must be a valid hex color')
})

// Typography validation
export const TypographySpecSchema = z.object({
  headingFont: z.string().min(1, 'Heading font is required'),
  bodyFont: z.string().min(1, 'Body font is required'),
  headingSizes: z.object({
    h1: z.string().min(1, 'H1 size is required'),
    h2: z.string().min(1, 'H2 size is required'),
    h3: z.string().min(1, 'H3 size is required')
  }),
  bodySize: z.string().min(1, 'Body size is required'),
  lineHeight: z.string().min(1, 'Line height is required')
})

// Responsive breakpoints validation
export const ResponsiveBreakpointsSchema = z.object({
  mobile: z.string().min(1, 'Mobile breakpoint is required'),
  tablet: z.string().min(1, 'Tablet breakpoint is required'),
  desktop: z.string().min(1, 'Desktop breakpoint is required'),
  largeDesktop: z.string().min(1, 'Large desktop breakpoint is required')
})

// Section styling validation
export const SectionStylingSchema = z.object({
  backgroundColor: z.string().regex(/^#[0-9A-Fa-f]{6}$/, 'Background color must be a valid hex color'),
  textColor: z.string().regex(/^#[0-9A-Fa-f]{6}$/, 'Text color must be a valid hex color'),
  padding: z.string().min(1, 'Padding is required'),
  margin: z.string().min(1, 'Margin is required'),
  alignment: z.enum(['LEFT', 'CENTER', 'RIGHT'])
})

// Layout section validation
export const LayoutSectionSchema = z.object({
  id: z.string().min(1, 'Section ID is required'),
  type: z.enum(['HERO', 'ABOUT', 'SERVICES', 'CONTACT', 'GALLERY', 'TESTIMONIALS']),
  order: z.number().min(0, 'Order must be non-negative'),
  content: z.any(), // This will be validated based on section type
  styling: SectionStylingSchema
})

// Navigation validation
export const NavItemSchema = z.object({
  label: z.string().min(1, 'Nav item label is required'),
  href: z.string().min(1, 'Nav item href is required'),
  isActive: z.boolean().default(false)
})

export const NavigationStylingSchema = z.object({
  backgroundColor: z.string().regex(/^#[0-9A-Fa-f]{6}$/, 'Background color must be a valid hex color'),
  textColor: z.string().regex(/^#[0-9A-Fa-f]{6}$/, 'Text color must be a valid hex color'),
  hoverColor: z.string().regex(/^#[0-9A-Fa-f]{6}$/, 'Hover color must be a valid hex color'),
  position: z.enum(['FIXED', 'STATIC', 'STICKY'])
})

export const NavigationSpecSchema = z.object({
  type: z.enum(['HORIZONTAL', 'VERTICAL', 'HAMBURGER']),
  items: z.array(NavItemSchema).min(1, 'At least one navigation item required'),
  styling: NavigationStylingSchema
})

// Footer validation
export const SocialLinkSchema = z.object({
  platform: z.string().min(1, 'Social platform is required'),
  url: z.string().url('Invalid social URL'),
  icon: z.string().min(1, 'Social icon is required')
})

export const FooterContentSchema = z.object({
  businessInfo: z.string().min(5, 'Footer business info must be at least 5 characters'),
  contactInfo: ContactInfoSchema,
  socialLinks: z.array(SocialLinkSchema).default([]),
  copyright: z.string().min(1, 'Copyright text is required')
})

export const FooterStylingSchema = z.object({
  backgroundColor: z.string().regex(/^#[0-9A-Fa-f]{6}$/, 'Background color must be a valid hex color'),
  textColor: z.string().regex(/^#[0-9A-Fa-f]{6}$/, 'Text color must be a valid hex color'),
  layout: z.enum(['SIMPLE', 'MULTI_COLUMN'])
})

export const FooterSpecSchema = z.object({
  content: FooterContentSchema,
  styling: FooterStylingSchema
})

// Layout structure validation
export const LayoutStructureSchema = z.object({
  sections: z.array(LayoutSectionSchema).min(1, 'At least one section required'),
  navigation: NavigationSpecSchema,
  footer: FooterSpecSchema
})

// Design spec validation
export const DesignSpecSchema = z.object({
  colorScheme: ColorSchemeSchema,
  typography: TypographySpecSchema,
  layout: LayoutStructureSchema,
  culturalElements: z.array(CulturalElementSchema).default([]),
  responsiveBreakpoints: ResponsiveBreakpointsSchema
})

// Website validation
export const WebsiteSchema = z.object({
  id: z.string().min(1, 'Website ID is required'),
  userId: z.string().min(1, 'User ID is required'),
  name: z.string().min(1, 'Website name is required').max(100, 'Website name too long'),
  businessProfile: BusinessProfileSchema,
  generatedContent: GeneratedContentSchema,
  designSpec: DesignSpecSchema,
  htmlOutput: z.string().min(1, 'HTML output is required'),
  cssOutput: z.string().min(1, 'CSS output is required'),
  subdomain: z.string().min(1, 'Subdomain is required').max(50, 'Subdomain too long'),
  isPublished: z.boolean().default(false),
  createdAt: z.date(),
  updatedAt: z.date()
})

// User validation
export const UserSchema = z.object({
  id: z.string().min(1, 'User ID is required'),
  email: z.string().email('Invalid email format'),
  subscription: SubscriptionTierSchema.default('starter'),
  websites: z.array(WebsiteSchema).default([]),
  createdAt: z.date(),
  updatedAt: z.date()
})

// API input validations
export const CreateWebsiteInputSchema = z.object({
  businessDescription: z.string().min(10, 'Business description must be at least 10 characters').max(1000, 'Business description too long'),
  contactPhone: z.string().optional(),
  contactEmail: z.string().email('Invalid email format').optional(),
  businessLocation: z.string().optional()
})

export const UpdateWebsiteInputSchema = z.object({
  name: z.string().min(1, 'Website name is required').max(100, 'Website name too long').optional(),
  businessProfile: BusinessProfileSchema.partial().optional(),
  generatedContent: GeneratedContentSchema.partial().optional(),
  designSpec: DesignSpecSchema.partial().optional(),
  isPublished: z.boolean().optional()
})

// Validation helper functions
export function validateBusinessProfile(data: unknown) {
  return BusinessProfileSchema.safeParse(data)
}

export function validateGeneratedContent(data: unknown) {
  return GeneratedContentSchema.safeParse(data)
}

export function validateDesignSpec(data: unknown) {
  return DesignSpecSchema.safeParse(data)
}

export function validateWebsite(data: unknown) {
  return WebsiteSchema.safeParse(data)
}

export function validateUser(data: unknown) {
  return UserSchema.safeParse(data)
}

export function validateCreateWebsiteInput(data: unknown) {
  return CreateWebsiteInputSchema.safeParse(data)
}

export function validateUpdateWebsiteInput(data: unknown) {
  return UpdateWebsiteInputSchema.safeParse(data)
}