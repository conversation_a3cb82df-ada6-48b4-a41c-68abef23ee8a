/**
 * Analytics Service for PagesLab
 * Tracks website generation metrics, user behavior, and performance data
 */

export interface AnalyticsEvent {
  id: string
  timestamp: number
  type: 'website_generation' | 'user_action' | 'performance' | 'error' | 'business_metric'
  category: string
  action: string
  label?: string
  value?: number
  userId?: string
  sessionId: string
  metadata: Record<string, any>
}

export interface WebsiteGenerationMetrics {
  businessType: string
  region: string
  includeImages: boolean
  integrations: string[]
  generationTime: number
  success: boolean
  errorCode?: string
  contentLength: number
  imageCount: number
  sectionCount: number
}

export interface UserBehaviorMetrics {
  action: 'page_view' | 'button_click' | 'form_submit' | 'download' | 'share'
  page: string
  element?: string
  duration?: number
  scrollDepth?: number
}

export interface PerformanceMetrics {
  metric: 'load_time' | 'api_response_time' | 'image_optimization' | 'cache_hit_rate'
  value: number
  endpoint?: string
  cacheType?: string
}

export interface BusinessMetrics {
  metric: 'conversion' | 'retention' | 'engagement' | 'revenue'
  value: number
  period: 'daily' | 'weekly' | 'monthly'
}

class AnalyticsService {
  private events: AnalyticsEvent[] = []
  private sessionId: string
  private userId?: string
  private maxEvents = 10000 // Keep last 10k events in memory

  constructor() {
    this.sessionId = this.generateSessionId()
    this.startPeriodicFlush()
  }

  private generateSessionId(): string {
    return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }

  private generateEventId(): string {
    return `event_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }

  setUserId(userId: string): void {
    this.userId = userId
  }

  /**
   * Track website generation event
   */
  trackWebsiteGeneration(metrics: WebsiteGenerationMetrics): void {
    this.track({
      type: 'website_generation',
      category: 'generation',
      action: 'create_website',
      label: metrics.businessType,
      value: metrics.generationTime,
      metadata: {
        ...metrics,
        timestamp: Date.now()
      }
    })
  }

  /**
   * Track user behavior event
   */
  trackUserBehavior(metrics: UserBehaviorMetrics): void {
    this.track({
      type: 'user_action',
      category: 'behavior',
      action: metrics.action,
      label: metrics.page,
      value: metrics.duration,
      metadata: {
        ...metrics,
        timestamp: Date.now()
      }
    })
  }

  /**
   * Track performance metrics
   */
  trackPerformance(metrics: PerformanceMetrics): void {
    this.track({
      type: 'performance',
      category: 'performance',
      action: metrics.metric,
      label: metrics.endpoint,
      value: metrics.value,
      metadata: {
        ...metrics,
        timestamp: Date.now()
      }
    })
  }

  /**
   * Track business metrics
   */
  trackBusinessMetric(metrics: BusinessMetrics): void {
    this.track({
      type: 'business_metric',
      category: 'business',
      action: metrics.metric,
      label: metrics.period,
      value: metrics.value,
      metadata: {
        ...metrics,
        timestamp: Date.now()
      }
    })
  }

  /**
   * Track error events
   */
  trackError(error: {
    code: string
    message: string
    category: string
    severity: 'low' | 'medium' | 'high' | 'critical'
    context?: Record<string, any>
  }): void {
    this.track({
      type: 'error',
      category: 'error',
      action: error.code,
      label: error.category,
      metadata: {
        ...error,
        timestamp: Date.now()
      }
    })
  }

  /**
   * Generic event tracking
   */
  private track(eventData: Omit<AnalyticsEvent, 'id' | 'timestamp' | 'sessionId' | 'userId'>): void {
    const event: AnalyticsEvent = {
      id: this.generateEventId(),
      timestamp: Date.now(),
      sessionId: this.sessionId,
      userId: this.userId,
      ...eventData
    }

    this.events.push(event)

    // Keep only the most recent events
    if (this.events.length > this.maxEvents) {
      this.events = this.events.slice(-this.maxEvents)
    }

    // Log important events to console in development
    if (process.env.NODE_ENV === 'development') {
      console.log('📊 Analytics Event:', {
        type: event.type,
        action: event.action,
        label: event.label,
        value: event.value
      })
    }
  }

  /**
   * Get analytics summary
   */
  getSummary(timeRange: { start: number; end: number } = { 
    start: Date.now() - 24 * 60 * 60 * 1000, 
    end: Date.now() 
  }): {
    totalEvents: number
    websiteGenerations: number
    averageGenerationTime: number
    topBusinessTypes: Array<{ type: string; count: number }>
    errorRate: number
    performanceMetrics: {
      averageLoadTime: number
      cacheHitRate: number
    }
  } {
    const filteredEvents = this.events.filter(
      event => event.timestamp >= timeRange.start && event.timestamp <= timeRange.end
    )

    const websiteGenerations = filteredEvents.filter(
      event => event.type === 'website_generation'
    )

    const errors = filteredEvents.filter(
      event => event.type === 'error'
    )

    const performanceEvents = filteredEvents.filter(
      event => event.type === 'performance'
    )

    // Calculate business type distribution
    const businessTypeCounts = new Map<string, number>()
    websiteGenerations.forEach(event => {
      const businessType = event.metadata.businessType || 'unknown'
      businessTypeCounts.set(businessType, (businessTypeCounts.get(businessType) || 0) + 1)
    })

    const topBusinessTypes = Array.from(businessTypeCounts.entries())
      .map(([type, count]) => ({ type, count }))
      .sort((a, b) => b.count - a.count)
      .slice(0, 5)

    // Calculate average generation time
    const generationTimes = websiteGenerations
      .map(event => event.value || 0)
      .filter(time => time > 0)
    
    const averageGenerationTime = generationTimes.length > 0
      ? generationTimes.reduce((sum, time) => sum + time, 0) / generationTimes.length
      : 0

    // Calculate performance metrics
    const loadTimeEvents = performanceEvents.filter(event => event.action === 'load_time')
    const averageLoadTime = loadTimeEvents.length > 0
      ? loadTimeEvents.reduce((sum, event) => sum + (event.value || 0), 0) / loadTimeEvents.length
      : 0

    const cacheEvents = performanceEvents.filter(event => event.action === 'cache_hit_rate')
    const cacheHitRate = cacheEvents.length > 0
      ? cacheEvents.reduce((sum, event) => sum + (event.value || 0), 0) / cacheEvents.length
      : 0

    return {
      totalEvents: filteredEvents.length,
      websiteGenerations: websiteGenerations.length,
      averageGenerationTime,
      topBusinessTypes,
      errorRate: filteredEvents.length > 0 ? (errors.length / filteredEvents.length) * 100 : 0,
      performanceMetrics: {
        averageLoadTime,
        cacheHitRate
      }
    }
  }

  /**
   * Get events by type
   */
  getEventsByType(type: AnalyticsEvent['type'], limit = 100): AnalyticsEvent[] {
    return this.events
      .filter(event => event.type === type)
      .slice(-limit)
      .reverse()
  }

  /**
   * Export analytics data
   */
  exportData(format: 'json' | 'csv' = 'json'): string {
    if (format === 'csv') {
      const headers = ['id', 'timestamp', 'type', 'category', 'action', 'label', 'value', 'userId', 'sessionId']
      const rows = this.events.map(event => [
        event.id,
        new Date(event.timestamp).toISOString(),
        event.type,
        event.category,
        event.action,
        event.label || '',
        event.value || '',
        event.userId || '',
        event.sessionId
      ])
      
      return [headers, ...rows].map(row => row.join(',')).join('\n')
    }

    return JSON.stringify(this.events, null, 2)
  }

  /**
   * Clear analytics data
   */
  clear(): void {
    this.events = []
  }

  /**
   * Periodic flush to external analytics service (placeholder)
   */
  private startPeriodicFlush(): void {
    // In production, you would send data to external analytics services
    // like Google Analytics, Mixpanel, or your own analytics backend
    setInterval(() => {
      if (this.events.length > 0) {
        console.log(`📊 Analytics: ${this.events.length} events tracked`)
        
        // Here you would send data to external service
        // this.flushToExternalService()
      }
    }, 5 * 60 * 1000) // Every 5 minutes
  }

  /**
   * Flush to external analytics service (placeholder)
   */
  private async flushToExternalService(): Promise<void> {
    // Implementation would depend on your analytics provider
    // Example: Google Analytics, Mixpanel, PostHog, etc.
    
    try {
      // const response = await fetch('/api/analytics/flush', {
      //   method: 'POST',
      //   headers: { 'Content-Type': 'application/json' },
      //   body: JSON.stringify(this.events)
      // })
      
      console.log('📊 Analytics data would be flushed to external service')
    } catch (error) {
      console.error('Failed to flush analytics data:', error)
    }
  }
}

// Singleton instance
export const analyticsService = new AnalyticsService()

// Convenience functions
export const trackWebsiteGeneration = (metrics: WebsiteGenerationMetrics) => 
  analyticsService.trackWebsiteGeneration(metrics)

export const trackUserBehavior = (metrics: UserBehaviorMetrics) => 
  analyticsService.trackUserBehavior(metrics)

export const trackPerformance = (metrics: PerformanceMetrics) => 
  analyticsService.trackPerformance(metrics)

export const trackError = (error: Parameters<typeof analyticsService.trackError>[0]) => 
  analyticsService.trackError(error)
