/**
 * Rate Limiting Service for PagesLab API Endpoints
 * Implements sliding window rate limiting with Redis-like in-memory storage
 */

import { NextRequest, NextResponse } from 'next/server'

export interface RateLimitConfig {
  windowMs: number // Time window in milliseconds
  maxRequests: number // Maximum requests per window
  keyGenerator?: (request: NextRequest) => string
  skipSuccessfulRequests?: boolean
  skipFailedRequests?: boolean
  message?: string
  headers?: boolean
}

export interface RateLimitInfo {
  limit: number
  remaining: number
  reset: number
  retryAfter?: number
}

interface RateLimitEntry {
  requests: number[]
  windowStart: number
}

class RateLimiter {
  private store = new Map<string, RateLimitEntry>()
  private cleanupInterval: NodeJS.Timeout

  constructor() {
    // Clean up expired entries every 5 minutes
    this.cleanupInterval = setInterval(() => {
      this.cleanup()
    }, 5 * 60 * 1000)
  }

  private cleanup(): void {
    const now = Date.now()
    for (const [key, entry] of this.store.entries()) {
      // Remove entries older than 1 hour
      if (now - entry.windowStart > 60 * 60 * 1000) {
        this.store.delete(key)
      }
    }
  }

  private getClientKey(request: NextRequest, keyGenerator?: (req: NextRequest) => string): string {
    if (keyGenerator) {
      return keyGenerator(request)
    }

    // Try to get IP from various headers (for production deployment)
    const forwarded = request.headers.get('x-forwarded-for')
    const realIp = request.headers.get('x-real-ip')
    const ip = forwarded?.split(',')[0] || realIp || 'unknown'
    
    return `rate_limit:${ip}`
  }

  private removeExpiredRequests(requests: number[], windowMs: number): number[] {
    const now = Date.now()
    return requests.filter(timestamp => now - timestamp < windowMs)
  }

  checkRateLimit(request: NextRequest, config: RateLimitConfig): RateLimitInfo {
    const key = this.getClientKey(request, config.keyGenerator)
    const now = Date.now()
    
    let entry = this.store.get(key)
    if (!entry) {
      entry = { requests: [], windowStart: now }
      this.store.set(key, entry)
    }

    // Remove expired requests from the sliding window
    entry.requests = this.removeExpiredRequests(entry.requests, config.windowMs)

    const remaining = Math.max(0, config.maxRequests - entry.requests.length)
    const reset = now + config.windowMs

    return {
      limit: config.maxRequests,
      remaining,
      reset,
      retryAfter: remaining === 0 ? Math.ceil(config.windowMs / 1000) : undefined
    }
  }

  recordRequest(request: NextRequest, config: RateLimitConfig): boolean {
    const key = this.getClientKey(request, config.keyGenerator)
    const now = Date.now()
    
    let entry = this.store.get(key)
    if (!entry) {
      entry = { requests: [], windowStart: now }
      this.store.set(key, entry)
    }

    // Remove expired requests
    entry.requests = this.removeExpiredRequests(entry.requests, config.windowMs)

    // Check if limit exceeded
    if (entry.requests.length >= config.maxRequests) {
      return false // Rate limit exceeded
    }

    // Record the request
    entry.requests.push(now)
    this.store.set(key, entry)
    
    return true // Request allowed
  }

  destroy(): void {
    if (this.cleanupInterval) {
      clearInterval(this.cleanupInterval)
    }
    this.store.clear()
  }
}

// Singleton rate limiter instance
const rateLimiter = new RateLimiter()

// Default configurations for different endpoint types
export const RATE_LIMIT_CONFIGS = {
  // Website generation - most resource intensive
  WEBSITE_GENERATION: {
    windowMs: 15 * 60 * 1000, // 15 minutes
    maxRequests: 5, // 5 websites per 15 minutes
    message: 'Too many website generation requests. Please wait before generating another website.',
    headers: true
  },

  // Content generation - moderate usage
  CONTENT_GENERATION: {
    windowMs: 5 * 60 * 1000, // 5 minutes
    maxRequests: 20, // 20 requests per 5 minutes
    message: 'Too many content generation requests. Please slow down.',
    headers: true
  },

  // Authentication - strict for security
  AUTH: {
    windowMs: 15 * 60 * 1000, // 15 minutes
    maxRequests: 10, // 10 login attempts per 15 minutes
    message: 'Too many authentication attempts. Please wait before trying again.',
    headers: true
  },

  // General API - lenient for regular operations
  GENERAL: {
    windowMs: 1 * 60 * 1000, // 1 minute
    maxRequests: 60, // 60 requests per minute
    message: 'Too many requests. Please slow down.',
    headers: true
  },

  // Image processing - moderate limits
  IMAGE_PROCESSING: {
    windowMs: 5 * 60 * 1000, // 5 minutes
    maxRequests: 30, // 30 image requests per 5 minutes
    message: 'Too many image processing requests. Please wait.',
    headers: true
  }
} as const

/**
 * Rate limiting middleware function
 */
export function createRateLimit(config: RateLimitConfig) {
  return async (request: NextRequest): Promise<NextResponse | null> => {
    try {
      const info = rateLimiter.checkRateLimit(request, config)
      
      // Check if rate limit exceeded
      if (info.remaining === 0) {
        const response = NextResponse.json(
          {
            success: false,
            error: config.message || 'Rate limit exceeded',
            code: 'RATE_LIMIT_EXCEEDED',
            retryAfter: info.retryAfter
          },
          { status: 429 }
        )

        // Add rate limit headers
        if (config.headers) {
          response.headers.set('X-RateLimit-Limit', info.limit.toString())
          response.headers.set('X-RateLimit-Remaining', info.remaining.toString())
          response.headers.set('X-RateLimit-Reset', info.reset.toString())
          if (info.retryAfter) {
            response.headers.set('Retry-After', info.retryAfter.toString())
          }
        }

        return response
      }

      // Record the request
      const allowed = rateLimiter.recordRequest(request, config)
      if (!allowed) {
        return NextResponse.json(
          {
            success: false,
            error: config.message || 'Rate limit exceeded',
            code: 'RATE_LIMIT_EXCEEDED'
          },
          { status: 429 }
        )
      }

      // Request allowed - return null to continue processing
      return null

    } catch (error) {
      console.error('Rate limiting error:', error)
      // On error, allow the request to proceed
      return null
    }
  }
}

/**
 * Utility function to add rate limit headers to successful responses
 */
export function addRateLimitHeaders(
  response: NextResponse, 
  request: NextRequest, 
  config: RateLimitConfig
): NextResponse {
  if (!config.headers) return response

  try {
    const info = rateLimiter.checkRateLimit(request, config)
    
    response.headers.set('X-RateLimit-Limit', info.limit.toString())
    response.headers.set('X-RateLimit-Remaining', info.remaining.toString())
    response.headers.set('X-RateLimit-Reset', info.reset.toString())
    
    return response
  } catch (error) {
    console.error('Error adding rate limit headers:', error)
    return response
  }
}

/**
 * Custom key generators for specific use cases
 */
export const keyGenerators = {
  // Rate limit by IP + User Agent (more specific)
  ipAndUserAgent: (request: NextRequest): string => {
    const forwarded = request.headers.get('x-forwarded-for')
    const realIp = request.headers.get('x-real-ip')
    const ip = forwarded?.split(',')[0] || realIp || 'unknown'
    const userAgent = request.headers.get('user-agent') || 'unknown'
    return `rate_limit:${ip}:${userAgent.substring(0, 50)}`
  },

  // Rate limit by authenticated user ID
  userId: (request: NextRequest): string => {
    const token = request.cookies.get('auth-token')?.value || 
                  request.headers.get('Authorization')?.replace('Bearer ', '')
    
    if (token) {
      try {
        const jwt = require('jsonwebtoken')
        const decoded = jwt.verify(token, process.env.JWT_SECRET || 'fallback-secret-key') as { userId: string }
        return `rate_limit:user:${decoded.userId}`
      } catch {
        // Fall back to IP if token is invalid
      }
    }
    
    const forwarded = request.headers.get('x-forwarded-for')
    const realIp = request.headers.get('x-real-ip')
    const ip = forwarded?.split(',')[0] || realIp || 'unknown'
    return `rate_limit:${ip}`
  }
}

export { rateLimiter }
