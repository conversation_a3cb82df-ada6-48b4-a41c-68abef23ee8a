/**
 * Image Service with Unsplash and Pexels support
 * Provides high-quality images for website generation
 */
import { websiteCache } from './cache-service'
import { withErrorHandling, createError, errorHandler } from './error-handling'

interface WebsiteImage {
  id: string
  url: string
  alt: string
  width: number
  height: number
  photographer: string
  photographerUrl: string
  source: 'unsplash' | 'pexels' | 'fallback'
  // Performance optimization URLs
  optimized?: {
    webp: string
    jpeg: string
    placeholder: string
    srcSet: string
  }
}

export class ImageService {
  private unsplashBaseUrl = 'https://api.unsplash.com'
  private pexelsBaseUrl = 'https://api.pexels.com/v1'
  private unsplashAccessKey: string
  private pexelsApiKey: string

  constructor() {
    this.unsplashAccessKey = process.env.UNSPLASH_ACCESS_KEY || ''
    this.pexelsApiKey = process.env.PEXELS_API_KEY || ''

    console.log('🔑 Image Service initialized:')
    console.log(`  - Unsplash: ${this.unsplashAccessKey ? '✅ Available' : '❌ No API key'}`)
    console.log(`  - Pexels: ${this.pexelsApiKey ? '✅ Available' : '❌ No API key'}`)
  }

  /**
   * Generate optimized image URLs for performance
   */
  private generateOptimizedUrls(baseUrl: string, source: 'unsplash' | 'pexels'): {
    webp: string
    jpeg: string
    placeholder: string
    srcSet: string
  } {
    if (source === 'unsplash') {
      return {
        webp: `${baseUrl}&fm=webp&q=85&w=1200`,
        jpeg: `${baseUrl}&fm=jpg&q=85&w=1200`,
        placeholder: `${baseUrl}&fm=jpg&q=20&w=20&blur=200`,
        srcSet: [640, 768, 1024, 1280, 1920].map(w =>
          `${baseUrl}&fm=webp&q=85&w=${w} ${w}w`
        ).join(', ')
      }
    } else {
      // Pexels doesn't support URL parameters, return original
      return {
        webp: baseUrl,
        jpeg: baseUrl,
        placeholder: baseUrl,
        srcSet: `${baseUrl} 1920w`
      }
    }
  }

  /**
   * Generate AI-powered image keywords from business description
   */
  private async generateImageKeywords(businessDescription: string): Promise<{
    primary: string[];
    hero: string[];
    gallery: string[];
  }> {
    try {
      const prompt = `
        Based on this Kenyan business description: "${businessDescription}"

        Generate relevant image search keywords that would find:
        1. Professional business images (NO people unless absolutely necessary)
        2. African/Kenyan context when possible
        3. Industry-specific imagery

        Return ONLY a JSON object with this structure:
        {
          "primary": ["keyword1", "keyword2", "keyword3"],
          "hero": ["hero-keyword1", "hero-keyword2"],
          "gallery": ["gallery-keyword1", "gallery-keyword2", "gallery-keyword3"]
        }

        Focus on:
        - Business equipment, products, or services
        - African/Kenyan settings when relevant
        - Professional environments
        - Avoid people in images unless it's a service that requires human interaction
      `

      const response = await fetch('/api/generate-content', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ prompt, type: 'image-keywords' })
      })

      if (response.ok) {
        const result = await response.json()
        const keywords = JSON.parse(result.content)

        // Add African/Kenyan context to keywords
        const enhancedKeywords = {
          primary: keywords.primary.map((k: string) => `${k} Kenya Africa`),
          hero: keywords.hero.map((k: string) => `${k} Kenya professional`),
          gallery: keywords.gallery.map((k: string) => `${k} African business`)
        }

        return enhancedKeywords
      }
    } catch (error) {
      console.log('AI keyword generation failed, using fallback')
    }

    // Fallback to industry-specific keywords based on business description
    const businessType = this.inferBusinessType(businessDescription)
    const fallbackKeywords = this.getFallbackKeywords(businessType)

    return {
      primary: fallbackKeywords.primary,
      hero: fallbackKeywords.hero,
      gallery: fallbackKeywords.gallery
    }
  }

  /**
   * Infer business type from description
   */
  private inferBusinessType(businessDescription: string): string {
    const description = businessDescription.toLowerCase()

    if (description.includes('restaurant') || description.includes('food') || description.includes('kitchen') || description.includes('cafe') || description.includes('hotel') || description.includes('catering')) {
      return 'RESTAURANT'
    }
    if (description.includes('shop') || description.includes('store') || description.includes('retail') || description.includes('boutique') || description.includes('market')) {
      return 'RETAIL'
    }
    if (description.includes('tech') || description.includes('software') || description.includes('digital') || description.includes('app') || description.includes('website') || description.includes('IT')) {
      return 'TECHNOLOGY'
    }
    if (description.includes('health') || description.includes('medical') || description.includes('clinic') || description.includes('hospital') || description.includes('doctor') || description.includes('pharmacy')) {
      return 'HEALTHCARE'
    }
    if (description.includes('education') || description.includes('school') || description.includes('training') || description.includes('course') || description.includes('academy') || description.includes('university')) {
      return 'EDUCATION'
    }
    if (description.includes('finance') || description.includes('bank') || description.includes('investment') || description.includes('insurance') || description.includes('loan') || description.includes('accounting')) {
      return 'FINANCE'
    }
    if (description.includes('construction') || description.includes('building') || description.includes('contractor') || description.includes('architecture') || description.includes('engineering')) {
      return 'CONSTRUCTION'
    }
    if (description.includes('consulting') || description.includes('advisory') || description.includes('professional service') || description.includes('business service')) {
      return 'CONSULTING'
    }
    if (description.includes('non-profit') || description.includes('charity') || description.includes('ngo') || description.includes('community') || description.includes('volunteer')) {
      return 'NON_PROFIT'
    }

    return 'OTHER'
  }

  /**
   * Get fallback keywords based on business type
   */
  private getFallbackKeywords(businessType: string): {
    primary: string[];
    hero: string[];
    gallery: string[];
  } {
    const keywordMap: Record<string, any> = {
      'RESTAURANT': {
        primary: ['restaurant Kenya', 'African cuisine', 'food service Kenya'],
        hero: ['restaurant interior Kenya', 'African dining', 'food preparation Kenya'],
        gallery: ['African dishes', 'restaurant kitchen Kenya', 'food presentation Africa']
      },
      'RETAIL': {
        primary: ['shop Kenya', 'retail store Africa', 'market Kenya'],
        hero: ['shop interior Kenya', 'retail space Africa', 'store front Kenya'],
        gallery: ['products Kenya', 'shopping Africa', 'retail display Kenya']
      },
      'TECHNOLOGY': {
        primary: ['technology Kenya', 'digital innovation Africa', 'tech startup Kenya'],
        hero: ['tech office Kenya', 'digital workspace Africa', 'innovation hub Kenya'],
        gallery: ['technology equipment Kenya', 'digital tools Africa', 'tech solutions Kenya']
      },
      'HEALTHCARE': {
        primary: ['healthcare Kenya', 'medical service Africa', 'health clinic Kenya'],
        hero: ['medical facility Kenya', 'healthcare interior Africa', 'clinic Kenya'],
        gallery: ['medical equipment Kenya', 'healthcare tools Africa', 'health service Kenya']
      },
      'EDUCATION': {
        primary: ['education Kenya', 'learning Africa', 'school Kenya'],
        hero: ['classroom Kenya', 'educational facility Africa', 'learning space Kenya'],
        gallery: ['educational materials Kenya', 'learning tools Africa', 'school equipment Kenya']
      },
      'FINANCE': {
        primary: ['finance Kenya', 'banking Africa', 'financial service Kenya'],
        hero: ['bank interior Kenya', 'financial office Africa', 'business meeting Kenya'],
        gallery: ['financial documents Kenya', 'banking tools Africa', 'business planning Kenya']
      },
      'CONSTRUCTION': {
        primary: ['construction Kenya', 'building Africa', 'development Kenya'],
        hero: ['construction site Kenya', 'building project Africa', 'development Kenya'],
        gallery: ['construction equipment Kenya', 'building materials Africa', 'construction tools Kenya']
      },
      'CONSULTING': {
        primary: ['consulting Kenya', 'business service Africa', 'professional advice Kenya'],
        hero: ['office meeting Kenya', 'business consultation Africa', 'professional workspace Kenya'],
        gallery: ['business planning Kenya', 'consultation tools Africa', 'professional service Kenya']
      },
      'NON_PROFIT': {
        primary: ['community Kenya', 'social impact Africa', 'volunteer Kenya'],
        hero: ['community center Kenya', 'social gathering Africa', 'volunteer work Kenya'],
        gallery: ['community service Kenya', 'social impact Africa', 'volunteer activities Kenya']
      }
    }

    return keywordMap[businessType] || {
      primary: ['business Kenya', 'professional service Africa', 'modern office Kenya'],
      hero: ['business interior Kenya', 'professional workspace Africa'],
      gallery: ['business equipment Kenya', 'service tools Africa', 'professional setup Kenya']
    }
  }

  /**
   * Get AI-powered business images with African context
   */
  async getAIBusinessImages(businessDescription: string, count: number = 6): Promise<{
    heroImage: WebsiteImage | null;
    businessImages: WebsiteImage[];
  }> {
    console.log('🤖 Generating AI-powered image keywords for business...')

    // Infer business type from description
    const businessType = this.inferBusinessType(businessDescription)
    console.log(`📊 Inferred business type: ${businessType}`)

    // Generate AI keywords
    const aiKeywords = await this.generateImageKeywords(businessDescription)

    // Get hero image with proper business type
    const heroImages = await this.getBusinessImages(businessType, aiKeywords.hero, 1)
    const heroImage = heroImages.length > 0 ? heroImages[0] : null

    // Get gallery images with proper business type
    const businessImages = await this.getBusinessImages(businessType, aiKeywords.gallery, count)

    return {
      heroImage,
      businessImages
    }
  }

  /**
   * Get images for a specific business type and context
   */
  async getBusinessImages(
    businessType: string,
    keywords: string[] = [],
    count: number = 6
  ): Promise<WebsiteImage[]> {
    console.log(`🖼️  Fetching ${count} images for ${businessType} with keywords:`, keywords)

    // Check cache first
    const cachedImages = websiteCache.getCachedImages(businessType, keywords)
    if (cachedImages && cachedImages.length >= count) {
      console.log('⚡ Using cached images for faster response')
      return cachedImages.slice(0, count)
    }

    // Try Unsplash first, then Pexels, then fallback
    let images = await this.getUnsplashImages(businessType, keywords, count)

    if (images.length === 0) {
      console.log('📸 Unsplash failed, trying Pexels...')
      images = await this.getPexelsImages(businessType, keywords, count)
    }

    if (images.length === 0) {
      console.log('⚠️  Both APIs failed, using fallback images...')
      images = this.getFallbackImages(businessType, count)
    }

    console.log(`✅ Successfully got ${images.length} images from ${images[0]?.source || 'fallback'}`)

    // Cache the images for future use
    if (images.length > 0) {
      websiteCache.cacheImages(businessType, keywords, images)
    }

    return images
  }

  /**
   * Get images from Unsplash
   */
  private async getUnsplashImages(
    businessType: string,
    keywords: string[] = [],
    count: number = 6
  ): Promise<WebsiteImage[]> {
    if (!this.unsplashAccessKey) {
      console.log('No Unsplash API key provided')
      return []
    }

    return withErrorHandling(async () => {
      const searchTerms = this.getSearchTermsForBusiness(businessType, keywords)
      const query = searchTerms.join(' ')

      const response = await fetch(
        `${this.unsplashBaseUrl}/search/photos?query=${encodeURIComponent(query)}&per_page=${count}&orientation=landscape`,
        {
          headers: {
            'Authorization': `Client-ID ${this.unsplashAccessKey}`
          },
          signal: AbortSignal.timeout(10000) // 10 second timeout
        }
      )

      if (!response.ok) {
        if (response.status === 429) {
          throw createError('API_RATE_LIMIT', new Error(`Unsplash rate limit: ${response.status}`), {
            component: 'ImageService',
            action: 'getUnsplashImages'
          })
        }
        if (response.status === 401) {
          throw createError('UNAUTHORIZED', new Error(`Unsplash auth error: ${response.status}`), {
            component: 'ImageService',
            action: 'getUnsplashImages'
          })
        }
        throw createError('NETWORK_UNAVAILABLE', new Error(`Unsplash API error: ${response.status}`), {
          component: 'ImageService',
          action: 'getUnsplashImages'
        })
      }

      const data = await response.json()

      if (!data.results || data.results.length === 0) {
        console.log(`⚠️ No Unsplash images found for query: ${query}`)
        return []
      }

      return data.results.map((photo: any) => ({
        id: photo.id,
        url: photo.urls.regular,
        alt: photo.alt_description || `${businessType} image`,
        width: photo.width,
        height: photo.height,
        photographer: photo.user.name,
        photographerUrl: photo.user.links.html,
        source: 'unsplash' as const,
        optimized: this.generateOptimizedUrls(photo.urls.raw, 'unsplash')
      }))
    }, {
      component: 'ImageService',
      action: 'getUnsplashImages',
      metadata: { businessType, keywords, count }
    }).catch((error) => {
      console.error('Error fetching Unsplash images:', error)
      return []
    })
  }

  /**
   * Get images from Pexels
   */
  private async getPexelsImages(
    businessType: string,
    keywords: string[] = [],
    count: number = 6
  ): Promise<WebsiteImage[]> {
    if (!this.pexelsApiKey) {
      console.log('No Pexels API key provided')
      return []
    }

    return withErrorHandling(async () => {
      const searchTerms = this.getSearchTermsForBusiness(businessType, keywords)
      const query = searchTerms.join(' ')

      const response = await fetch(
        `${this.pexelsBaseUrl}/search?query=${encodeURIComponent(query)}&per_page=${count}&orientation=landscape`,
        {
          headers: {
            'Authorization': this.pexelsApiKey
          },
          signal: AbortSignal.timeout(10000) // 10 second timeout
        }
      )

      if (!response.ok) {
        if (response.status === 429) {
          throw createError('API_RATE_LIMIT', new Error(`Pexels rate limit: ${response.status}`), {
            component: 'ImageService',
            action: 'getPexelsImages'
          })
        }
        if (response.status === 401) {
          throw createError('UNAUTHORIZED', new Error(`Pexels auth error: ${response.status}`), {
            component: 'ImageService',
            action: 'getPexelsImages'
          })
        }
        throw createError('NETWORK_UNAVAILABLE', new Error(`Pexels API error: ${response.status}`), {
          component: 'ImageService',
          action: 'getPexelsImages'
        })
      }

      const data = await response.json()

      if (!data.photos || data.photos.length === 0) {
        console.log(`⚠️ No Pexels images found for query: ${query}`)
        return []
      }

      return data.photos.map((photo: any) => ({
        id: photo.id.toString(),
        url: photo.src.large,
        alt: photo.alt || `${businessType} image`,
        width: photo.width,
        height: photo.height,
        photographer: photo.photographer,
        photographerUrl: photo.photographer_url,
        source: 'pexels' as const,
        optimized: this.generateOptimizedUrls(photo.src.original, 'pexels')
      }))
    }, {
      component: 'ImageService',
      action: 'getPexelsImages',
      metadata: { businessType, keywords, count }
    }).catch((error) => {
      console.error('Error fetching Pexels images:', error)
      return []
    })
  }

  /**
   * Get hero image for business
   */
  async getHeroImage(businessType: string, businessName: string): Promise<WebsiteImage | null> {
    console.log(`🎯 Fetching hero image for ${businessName} (${businessType})`)

    // Try Unsplash first, then Pexels
    let heroImage = await this.getUnsplashHeroImage(businessType, businessName)

    if (!heroImage) {
      console.log('📸 Unsplash hero failed, trying Pexels...')
      heroImage = await this.getPexelsHeroImage(businessType, businessName)
    }

    if (heroImage) {
      console.log(`✅ Got hero image from ${heroImage.source}: ${heroImage.alt}`)
    } else {
      console.log('❌ No hero image found from any source')
    }

    return heroImage
  }

  /**
   * Get hero image from Unsplash
   */
  private async getUnsplashHeroImage(businessType: string, businessName: string): Promise<WebsiteImage | null> {
    if (!this.unsplashAccessKey) {
      return null
    }

    try {
      const searchTerms = this.getHeroSearchTerms(businessType, businessName)
      const query = searchTerms.join(' ')

      const response = await fetch(
        `${this.unsplashBaseUrl}/search/photos?query=${encodeURIComponent(query)}&per_page=1&orientation=landscape`,
        {
          headers: {
            'Authorization': `Client-ID ${this.unsplashAccessKey}`
          }
        }
      )

      if (!response.ok) {
        console.log(`Unsplash API error: ${response.status}`)
        return null
      }

      const data = await response.json()

      if (data.results.length > 0) {
        const photo = data.results[0]
        return {
          id: photo.id,
          url: photo.urls.regular,
          alt: photo.alt_description || `${businessName} hero image`,
          width: photo.width,
          height: photo.height,
          photographer: photo.user.name,
          photographerUrl: photo.user.links.html,
          source: 'unsplash' as const
        }
      }

      return null
    } catch (error) {
      console.error('Error fetching Unsplash hero image:', error)
      return null
    }
  }

  /**
   * Get hero image from Pexels
   */
  private async getPexelsHeroImage(businessType: string, businessName: string): Promise<WebsiteImage | null> {
    if (!this.pexelsApiKey) {
      return null
    }

    try {
      const searchTerms = this.getHeroSearchTerms(businessType, businessName)
      const query = searchTerms.join(' ')

      const response = await fetch(
        `${this.pexelsBaseUrl}/search?query=${encodeURIComponent(query)}&per_page=1&orientation=landscape`,
        {
          headers: {
            'Authorization': this.pexelsApiKey
          }
        }
      )

      if (!response.ok) {
        throw new Error(`Pexels API error: ${response.status}`)
      }

      const data = await response.json()

      if (data.photos.length > 0) {
        const photo = data.photos[0]
        return {
          id: photo.id.toString(),
          url: photo.src.large,
          alt: photo.alt || `${businessName} hero image`,
          width: photo.width,
          height: photo.height,
          photographer: photo.photographer,
          photographerUrl: photo.photographer_url,
          source: 'pexels' as const
        }
      }

      return null
    } catch (error) {
      console.error('Error fetching Pexels hero image:', error)
      return null
    }
  }

  /**
   * Get search terms based on business type
   */
  private getSearchTermsForBusiness(businessType: string, keywords: string[]): string[] {
    const baseTerms: Record<string, string[]> = {
      'RESTAURANT': ['restaurant', 'food', 'dining', 'kitchen', 'chef', 'meal'],
      'RETAIL': ['shop', 'store', 'retail', 'shopping', 'products', 'business'],
      'SERVICES': ['service', 'professional', 'office', 'business', 'team'],
      'HEALTHCARE': ['healthcare', 'medical', 'clinic', 'doctor', 'health'],
      'EDUCATION': ['education', 'school', 'learning', 'students', 'classroom'],
      'TECHNOLOGY': ['technology', 'computer', 'digital', 'innovation', 'tech'],
      'AGRICULTURE': ['agriculture', 'farming', 'crops', 'rural', 'farm'],
      'TOURISM': ['tourism', 'travel', 'destination', 'adventure', 'landscape'],
      'MANUFACTURING': ['manufacturing', 'industry', 'factory', 'production'],
      'TRANSPORT': ['transport', 'logistics', 'delivery', 'vehicle', 'road'],
      'FINANCE': ['finance', 'banking', 'money', 'business', 'professional'],
      'REAL_ESTATE': ['real estate', 'property', 'house', 'building', 'home'],
      'ENTERTAINMENT': ['entertainment', 'event', 'music', 'performance', 'fun'],
      'FITNESS': ['fitness', 'gym', 'exercise', 'health', 'workout'],
      'BEAUTY': ['beauty salon', 'nail salon', 'hair salon', 'spa', 'manicure', 'pedicure', 'nails', 'hair styling', 'makeup', 'cosmetics'],
      'AUTOMOTIVE': ['automotive', 'car', 'vehicle', 'garage', 'repair'],
      'CONSTRUCTION': ['construction', 'building', 'architecture', 'development'],
      'CONSULTING': ['consulting', 'business', 'professional', 'office', 'meeting'],
      'NON_PROFIT': ['community', 'help', 'support', 'charity', 'volunteer'],
      'OTHER': ['business', 'professional', 'service', 'company']
    }

    const terms = baseTerms[businessType] || baseTerms['OTHER']
    return [...terms, ...keywords].slice(0, 3) // Limit to 3 terms for better results
  }

  /**
   * Get hero-specific search terms
   */
  private getHeroSearchTerms(businessType: string, businessName: string): string[] {
    const heroTerms: Record<string, string[]> = {
      'RESTAURANT': ['restaurant interior', 'dining room', 'food presentation'],
      'RETAIL': ['modern store', 'shop interior', 'retail space'],
      'SERVICES': ['professional office', 'business meeting', 'modern workspace'],
      'HEALTHCARE': ['modern clinic', 'healthcare facility', 'medical office'],
      'EDUCATION': ['modern classroom', 'learning environment', 'education'],
      'TECHNOLOGY': ['modern office', 'tech workspace', 'innovation'],
      'AGRICULTURE': ['farm landscape', 'agricultural field', 'rural'],
      'TOURISM': ['beautiful landscape', 'tourist destination', 'scenic'],
      'MANUFACTURING': ['modern factory', 'industrial facility', 'production'],
      'TRANSPORT': ['logistics center', 'transport hub', 'delivery'],
      'FINANCE': ['modern bank', 'financial office', 'professional'],
      'REAL_ESTATE': ['modern building', 'property', 'architecture'],
      'ENTERTAINMENT': ['event venue', 'entertainment space', 'stage'],
      'FITNESS': ['modern gym', 'fitness center', 'workout'],
      'BEAUTY': ['luxury beauty salon', 'nail salon interior', 'spa treatment room', 'hair salon', 'beauty parlor'],
      'AUTOMOTIVE': ['modern garage', 'car service', 'automotive'],
      'CONSTRUCTION': ['construction site', 'building', 'development'],
      'CONSULTING': ['modern office', 'business meeting', 'professional'],
      'NON_PROFIT': ['community center', 'helping hands', 'volunteer'],
      'OTHER': ['modern business', 'professional space', 'office']
    }

    return heroTerms[businessType] || heroTerms['OTHER']
  }

  /**
   * Get fallback images when both APIs are unavailable
   */
  private getFallbackImages(businessType: string, count: number): WebsiteImage[] {
    const fallbackUrls = [
      'https://images.unsplash.com/photo-1560472354-b33ff0c44a43?w=800',
      'https://images.unsplash.com/photo-1556909114-f6e7ad7d3136?w=800',
      'https://images.unsplash.com/photo-1542744173-8e7e53415bb0?w=800',
      'https://images.unsplash.com/photo-1497366216548-37526070297c?w=800',
      'https://images.unsplash.com/photo-1553877522-43269d4ea984?w=800',
      'https://images.unsplash.com/photo-1486406146926-c627a92ad1ab?w=800'
    ]

    return fallbackUrls.slice(0, count).map((url, index) => ({
      id: `fallback-${index}`,
      url,
      alt: `${businessType} image`,
      width: 800,
      height: 600,
      photographer: 'Unsplash',
      photographerUrl: 'https://unsplash.com',
      source: 'fallback' as const
    }))
  }
}

export const imageService = new ImageService()
