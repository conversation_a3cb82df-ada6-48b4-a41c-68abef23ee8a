import { prisma } from './db'
import type { 
  BusinessProfile, 
  GeneratedContent, 
  DesignSpec, 
  KenyanRegion, 
  BusinessType 
} from '@/types'

// User operations
export async function createUser(
  email: string,
  password: string,
  name?: string,
  subscriptionTier: 'starter' | 'premium' = 'starter'
) {
  return await prisma.user.create({
    data: {
      email,
      password,
      name,
      subscriptionTier
    }
  })
}

export async function getUserByEmail(email: string) {
  return await prisma.user.findUnique({
    where: { email },
    include: {
      websites: {
        orderBy: { createdAt: 'desc' }
      }
    }
  })
}

export async function getUserById(id: string) {
  return await prisma.user.findUnique({
    where: { id },
    include: {
      websites: {
        orderBy: { createdAt: 'desc' }
      }
    }
  })
}

// Website operations
export async function createWebsite(data: {
  userId: string
  name: string
  businessProfile: BusinessProfile
  generatedContent: GeneratedContent
  designSpec: DesignSpec
  htmlOutput: string
  cssOutput: string
  subdomain: string
}) {
  return await prisma.website.create({
    data: {
      ...data,
      businessProfile: data.businessProfile as any,
      generatedContent: data.generatedContent as any,
      designSpec: data.designSpec as any
    }
  })
}

export async function getWebsiteById(id: string) {
  return await prisma.website.findUnique({
    where: { id },
    include: {
      user: true,
      analytics: {
        orderBy: { createdAt: 'desc' },
        take: 10
      }
    }
  })
}

export async function getWebsiteBySubdomain(subdomain: string) {
  return await prisma.website.findUnique({
    where: { subdomain },
    include: {
      user: true
    }
  })
}

export async function updateWebsite(id: string, data: {
  name?: string
  businessProfile?: BusinessProfile
  generatedContent?: GeneratedContent
  designSpec?: DesignSpec
  htmlOutput?: string
  cssOutput?: string
  isPublished?: boolean
}) {
  const updateData: any = { ...data }
  
  // Convert objects to JSON for Prisma
  if (data.businessProfile) {
    updateData.businessProfile = data.businessProfile as any
  }
  if (data.generatedContent) {
    updateData.generatedContent = data.generatedContent as any
  }
  if (data.designSpec) {
    updateData.designSpec = data.designSpec as any
  }

  return await prisma.website.update({
    where: { id },
    data: updateData
  })
}

export async function deleteWebsite(id: string) {
  return await prisma.website.delete({
    where: { id }
  })
}

export async function getUserWebsites(userId: string) {
  return await prisma.website.findMany({
    where: { userId },
    orderBy: { createdAt: 'desc' }
  })
}

export async function getPublishedWebsites() {
  return await prisma.website.findMany({
    where: { isPublished: true },
    orderBy: { createdAt: 'desc' },
    include: {
      user: {
        select: { email: true }
      }
    }
  })
}

// Cultural template operations
export async function getCulturalTemplate(region: KenyanRegion, businessType: BusinessType) {
  return await prisma.culturalTemplate.findFirst({
    where: {
      region,
      businessType
    }
  })
}

export async function getCulturalTemplatesByRegion(region: KenyanRegion) {
  return await prisma.culturalTemplate.findMany({
    where: { region },
    orderBy: { businessType: 'asc' }
  })
}

export async function getCulturalTemplatesByBusinessType(businessType: BusinessType) {
  return await prisma.culturalTemplate.findMany({
    where: { businessType },
    orderBy: { region: 'asc' }
  })
}

export async function getAllCulturalTemplates() {
  return await prisma.culturalTemplate.findMany({
    orderBy: [
      { region: 'asc' },
      { businessType: 'asc' }
    ]
  })
}

// Analytics operations
export async function recordGenerationAnalytics(data: {
  websiteId?: string
  generationTimeMs: number
  userSatisfactionScore?: number
}) {
  return await prisma.generationAnalytics.create({
    data
  })
}

export async function getGenerationAnalytics(websiteId: string) {
  return await prisma.generationAnalytics.findMany({
    where: { websiteId },
    orderBy: { createdAt: 'desc' }
  })
}

export async function getAverageGenerationTime() {
  const result = await prisma.generationAnalytics.aggregate({
    _avg: {
      generationTimeMs: true
    }
  })
  
  return result._avg.generationTimeMs || 0
}

export async function getUserSatisfactionStats() {
  const result = await prisma.generationAnalytics.aggregate({
    _avg: {
      userSatisfactionScore: true
    },
    _count: {
      userSatisfactionScore: true
    },
    where: {
      userSatisfactionScore: {
        not: null
      }
    }
  })
  
  return {
    averageScore: result._avg.userSatisfactionScore || 0,
    totalResponses: result._count.userSatisfactionScore || 0
  }
}

// Database health check
export async function checkDatabaseConnection() {
  try {
    await prisma.$queryRaw`SELECT 1`
    return { status: 'healthy', message: 'Database connection successful' }
  } catch (error) {
    return { 
      status: 'unhealthy', 
      message: `Database connection failed: ${error instanceof Error ? error.message : 'Unknown error'}` 
    }
  }
}

// Cleanup function for tests
export async function cleanupDatabase() {
  if (process.env.NODE_ENV === 'test') {
    await prisma.generationAnalytics.deleteMany()
    await prisma.website.deleteMany()
    await prisma.culturalTemplate.deleteMany()
    await prisma.user.deleteMany()
  }
}