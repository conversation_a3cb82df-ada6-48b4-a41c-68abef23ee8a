/**
 * Authentication Middleware for PagesLab API Endpoints
 * Provides JWT token validation and user authentication
 */

import { NextRequest, NextResponse } from 'next/server'
import jwt from 'jsonwebtoken'

export interface AuthUser {
  id: string
  email: string
  name: string
  createdAt: string
  websites: any[]
}

export interface AuthContext {
  user: AuthUser
  token: string
}

// No more mock database - using Prisma only

/**
 * Extract and verify JWT token from request
 */
export function extractToken(request: NextRequest): string | null {
  // Try cookie first (more secure)
  const cookieToken = request.cookies.get('auth-token')?.value
  if (cookieToken) {
    return cookieToken
  }

  // Fall back to Authorization header
  const authHeader = request.headers.get('Authorization')
  if (authHeader && authHeader.startsWith('Bearer ')) {
    return authHeader.replace('Bearer ', '')
  }

  return null
}

/**
 * Verify JWT token and return decoded payload
 */
export function verifyToken(token: string): { userId: string; email: string } | null {
  try {
    const decoded = jwt.verify(
      token, 
      process.env.JWT_SECRET || 'fallback-secret-key'
    ) as { userId: string; email: string }
    
    return decoded
  } catch (error) {
    console.error('Token verification failed:', error)
    return null
  }
}

// getUserByEmail function removed - use Prisma database functions instead

/**
 * Authenticate request and return user context
 * Note: This function is now deprecated - use async authentication in API routes instead
 */
export function authenticateRequest(request: NextRequest): AuthContext | null {
  // This function is deprecated since we moved to async Prisma authentication
  // Use the async authentication pattern in API routes instead
  console.warn('authenticateRequest is deprecated - use async authentication in API routes')
  return null
}

/**
 * Authentication middleware - requires valid authentication
 */
export function requireAuth() {
  return async (request: NextRequest): Promise<NextResponse | AuthContext> => {
    const authContext = authenticateRequest(request)
    
    if (!authContext) {
      return NextResponse.json(
        {
          success: false,
          error: 'Authentication required',
          code: 'UNAUTHORIZED'
        },
        { status: 401 }
      )
    }

    return authContext
  }
}

/**
 * Optional authentication middleware - continues if no auth provided
 */
export function optionalAuth() {
  return async (request: NextRequest): Promise<AuthContext | null> => {
    return authenticateRequest(request)
  }
}

/**
 * Role-based authentication middleware
 */
export function requireRole(allowedRoles: string[]) {
  return async (request: NextRequest): Promise<NextResponse | AuthContext> => {
    const authContext = authenticateRequest(request)
    
    if (!authContext) {
      return NextResponse.json(
        {
          success: false,
          error: 'Authentication required',
          code: 'UNAUTHORIZED'
        },
        { status: 401 }
      )
    }

    // For now, all authenticated users have 'user' role
    // In production, you'd check user.role against allowedRoles
    const userRole = 'user'
    
    if (!allowedRoles.includes(userRole)) {
      return NextResponse.json(
        {
          success: false,
          error: 'Insufficient permissions',
          code: 'FORBIDDEN'
        },
        { status: 403 }
      )
    }

    return authContext
  }
}

/**
 * API key authentication middleware (for external integrations)
 */
export function requireApiKey() {
  return async (request: NextRequest): Promise<NextResponse | { apiKey: string }> => {
    const apiKey = request.headers.get('X-API-Key')
    
    if (!apiKey) {
      return NextResponse.json(
        {
          success: false,
          error: 'API key required',
          code: 'API_KEY_REQUIRED'
        },
        { status: 401 }
      )
    }

    // Validate API key (in production, check against database)
    const validApiKeys = [
      process.env.PAGESLAB_API_KEY,
      'demo-api-key-for-testing'
    ].filter(Boolean)

    if (!validApiKeys.includes(apiKey)) {
      return NextResponse.json(
        {
          success: false,
          error: 'Invalid API key',
          code: 'INVALID_API_KEY'
        },
        { status: 401 }
      )
    }

    return { apiKey }
  }
}

/**
 * Combine multiple middleware functions
 */
export function combineMiddleware(...middlewares: Array<(req: NextRequest) => Promise<any>>) {
  return async (request: NextRequest): Promise<any> => {
    const results: any[] = []
    
    for (const middleware of middlewares) {
      const result = await middleware(request)
      
      // If middleware returns a NextResponse, it's an error response
      if (result instanceof NextResponse) {
        return result
      }
      
      results.push(result)
    }
    
    // Return combined results
    return results.length === 1 ? results[0] : results
  }
}

/**
 * Utility function to add authentication headers to responses
 */
export function addAuthHeaders(response: NextResponse, authContext?: AuthContext): NextResponse {
  if (authContext) {
    response.headers.set('X-User-ID', authContext.user.id)
    response.headers.set('X-User-Email', authContext.user.email)
  }
  
  return response
}

/**
 * Create a protected API handler with authentication and rate limiting
 */
export function createProtectedHandler(
  handler: (request: NextRequest, authContext: AuthContext) => Promise<NextResponse>,
  options: {
    requireAuth?: boolean
    allowedRoles?: string[]
    rateLimit?: any
  } = {}
) {
  return async (request: NextRequest): Promise<NextResponse> => {
    try {
      // Apply rate limiting if configured
      if (options.rateLimit) {
        const rateLimitResult = await options.rateLimit(request)
        if (rateLimitResult instanceof NextResponse) {
          return rateLimitResult
        }
      }

      // Apply authentication if required
      let authContext: AuthContext | null = null
      
      if (options.requireAuth !== false) {
        const authResult = options.allowedRoles 
          ? await requireRole(options.allowedRoles)(request)
          : await requireAuth()(request)
        
        if (authResult instanceof NextResponse) {
          return authResult
        }
        
        authContext = authResult as AuthContext
      } else {
        authContext = await optionalAuth()(request)
      }

      // Call the actual handler
      const response = await handler(request, authContext!)
      
      // Add authentication headers
      return addAuthHeaders(response, authContext || undefined)
      
    } catch (error) {
      console.error('Protected handler error:', error)
      
      return NextResponse.json(
        {
          success: false,
          error: 'Internal server error',
          code: 'INTERNAL_ERROR'
        },
        { status: 500 }
      )
    }
  }
}

/**
 * Validate request origin for CORS
 */
export function validateOrigin(request: NextRequest): boolean {
  const origin = request.headers.get('origin')
  
  // Allow requests without origin (same-origin, mobile apps, etc.)
  if (!origin) {
    return true
  }

  // In production, maintain a whitelist of allowed origins
  const allowedOrigins = [
    'http://localhost:3000',
    'https://pageslab.co.ke',
    'https://www.pageslab.co.ke',
    process.env.NEXT_PUBLIC_APP_URL
  ].filter(Boolean)

  return allowedOrigins.includes(origin)
}

/**
 * CORS middleware
 */
export function corsMiddleware() {
  return async (request: NextRequest): Promise<NextResponse | null> => {
    if (!validateOrigin(request)) {
      return NextResponse.json(
        {
          success: false,
          error: 'Origin not allowed',
          code: 'CORS_ERROR'
        },
        { status: 403 }
      )
    }

    return null // Continue processing
  }
}
