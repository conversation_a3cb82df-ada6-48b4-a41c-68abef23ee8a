/**
 * Kenyan Address Formatting Utilities
 * Handles proper formatting of Kenyan addresses, postal codes, and location data
 */

export interface KenyanAddress {
  businessName?: string
  buildingName?: string
  streetAddress?: string
  area: string
  county: string
  region?: string
  postalCode?: string
  poBox?: string
  landmark?: string
  coordinates?: {
    lat: number
    lng: number
  }
}

export interface FormattedAddress {
  singleLine: string
  multiLine: string[]
  postal: string
  googleMapsQuery: string
  shortForm: string
}

/**
 * Format a Kenyan address according to local conventions
 */
export function formatKenyanAddress(address: KenyanAddress): FormattedAddress {
  const lines: string[] = []
  
  // Business name (if provided)
  if (address.businessName) {
    lines.push(address.businessName)
  }
  
  // Building name and street address
  if (address.buildingName) {
    lines.push(address.buildingName)
  }
  if (address.streetAddress) {
    lines.push(address.streetAddress)
  }
  
  // Area (required)
  lines.push(address.area)
  
  // County (required)
  lines.push(`${address.county} County`)
  
  // Postal code and P.O. Box
  if (address.postalCode || address.poBox) {
    const postalParts = []
    if (address.postalCode) postalParts.push(address.postalCode)
    if (address.poBox) postalParts.push(`P.O. Box ${address.poBox}`)
    lines.push(postalParts.join(', '))
  }
  
  // Country
  lines.push('Kenya')
  
  // Create different formats
  const singleLine = lines.join(', ')
  const multiLine = lines
  const postal = address.postalCode || ''
  const shortForm = `${address.area}, ${address.county}`
  
  // Google Maps query
  const googleMapsQuery = [
    address.businessName,
    address.area,
    address.county,
    'Kenya'
  ].filter(Boolean).join(', ')
  
  return {
    singleLine,
    multiLine,
    postal,
    googleMapsQuery,
    shortForm
  }
}

/**
 * Parse and validate Kenyan postal codes
 */
export function validateKenyanPostalCode(postalCode: string): boolean {
  // Kenyan postal codes are typically 5 digits
  const cleaned = postalCode.replace(/\D/g, '')
  return cleaned.length === 5 && /^\d{5}$/.test(cleaned)
}

/**
 * Format Kenyan postal code
 */
export function formatKenyanPostalCode(postalCode: string): string {
  const cleaned = postalCode.replace(/\D/g, '')
  if (cleaned.length === 5) {
    return cleaned
  }
  return postalCode // Return original if not valid
}

/**
 * Get county information
 */
export function getCountyInfo(countyName: string): CountyInfo {
  const counties: Record<string, CountyInfo> = {
    'Nairobi': {
      name: 'Nairobi',
      region: 'Central',
      capital: 'Nairobi',
      population: '4,397,073',
      area: '696 km²',
      description: 'Capital and largest city of Kenya, major business and financial hub',
      majorTowns: ['Nairobi', 'Kahawa', 'Ruiru'],
      economicActivities: ['Finance', 'Technology', 'Manufacturing', 'Services'],
      postalCodes: ['00100', '00200', '00300', '00400', '00500']
    },
    'Mombasa': {
      name: 'Mombasa',
      region: 'Coastal',
      capital: 'Mombasa',
      population: '1,208,333',
      area: '230 km²',
      description: 'Major port city and tourist destination on the Indian Ocean coast',
      majorTowns: ['Mombasa', 'Likoni', 'Changamwe'],
      economicActivities: ['Port Services', 'Tourism', 'Manufacturing', 'Trade'],
      postalCodes: ['80100', '80200', '80300']
    },
    'Kisumu': {
      name: 'Kisumu',
      region: 'Western',
      capital: 'Kisumu',
      population: '1,155,574',
      area: '2,086 km²',
      description: 'Major lakeside city and commercial center of western Kenya',
      majorTowns: ['Kisumu', 'Maseno', 'Katito'],
      economicActivities: ['Fishing', 'Agriculture', 'Trade', 'Manufacturing'],
      postalCodes: ['40100', '40200', '40300']
    },
    'Nakuru': {
      name: 'Nakuru',
      region: 'Rift Valley',
      capital: 'Nakuru',
      population: '2,162,202',
      area: '7,496 km²',
      description: 'Agricultural and industrial center in the Rift Valley',
      majorTowns: ['Nakuru', 'Naivasha', 'Gilgil'],
      economicActivities: ['Agriculture', 'Manufacturing', 'Tourism', 'Floriculture'],
      postalCodes: ['20100', '20117', '20300']
    },
    'Kiambu': {
      name: 'Kiambu',
      region: 'Central',
      capital: 'Kiambu',
      population: '2,417,735',
      area: '2,449 km²',
      description: 'Agricultural county known for coffee and tea production',
      majorTowns: ['Kiambu', 'Thika', 'Ruiru'],
      economicActivities: ['Agriculture', 'Manufacturing', 'Horticulture'],
      postalCodes: ['00900', '01000', '01100']
    }
  }
  
  return counties[countyName] || {
    name: countyName,
    region: 'Unknown',
    capital: countyName,
    population: 'N/A',
    area: 'N/A',
    description: `County in Kenya`,
    majorTowns: [countyName],
    economicActivities: ['Agriculture', 'Trade'],
    postalCodes: []
  }
}

/**
 * Generate directions text for common Kenyan locations
 */
export function generateDirectionsText(address: KenyanAddress): string {
  const county = address.county.toLowerCase()
  const area = address.area.toLowerCase()
  
  // Common direction patterns for major areas
  const directionTemplates: Record<string, string> = {
    'nairobi_cbd': 'Located in Nairobi CBD. Accessible via Uhuru Highway, Kenyatta Avenue, or Moi Avenue. Nearest matatu stages: Railways, Koja, or Country Bus.',
    'westlands': 'In Westlands area. Take Waiyaki Way from city center or use matatus from ABC Place, Westgate, or Sarit Centre.',
    'karen': 'Located in Karen. Use Langata Road or Karen Road. Accessible via matatus from Karen Shopping Centre.',
    'mombasa_town': 'In Mombasa town center. Accessible via Moi Avenue, Digo Road, or from Mwembe Tayari matatu stage.',
    'kisumu_town': 'Located in Kisumu town. Accessible via Kakamega Road, Kisumu-Busia Road, or from Kisumu Bus Park.'
  }
  
  const key = `${county}_${area.replace(/\s+/g, '_')}`
  
  return directionTemplates[key] || 
    `Located in ${address.area}, ${address.county} County. Accessible by local matatu and transport services. ${address.landmark ? `Look for ${address.landmark} as a landmark.` : ''}`
}

/**
 * Get transportation options for a county
 */
export function getTransportationOptions(county: string): TransportationInfo {
  const transportOptions: Record<string, TransportationInfo> = {
    'Nairobi': {
      matatu: ['City Hoppa', 'KBS', 'Double M', 'Citi Shuttle'],
      bus: ['Kenya Bus Service', 'City Hoppa'],
      taxi: ['Uber', 'Bolt', 'Little Cab'],
      other: ['Boda-boda', 'Tuk-tuk', 'SGR Train']
    },
    'Mombasa': {
      matatu: ['Mombasa Raha', 'Coast Bus'],
      bus: ['Tahmeed', 'Mombasa Raha'],
      taxi: ['Uber', 'Bolt', 'Tuk-tuk'],
      other: ['Boda-boda', 'Ferry services']
    },
    'Kisumu': {
      matatu: ['Kisumu matatus', 'Lake Basin'],
      bus: ['Easy Coach', 'Akamba'],
      taxi: ['Bolt', 'Local taxis'],
      other: ['Boda-boda', 'Lake transport']
    }
  }
  
  return transportOptions[county] || {
    matatu: ['Local matatus'],
    bus: ['Regional buses'],
    taxi: ['Local taxis'],
    other: ['Boda-boda']
  }
}

// Type definitions
export interface CountyInfo {
  name: string
  region: string
  capital: string
  population: string
  area: string
  description: string
  majorTowns: string[]
  economicActivities: string[]
  postalCodes: string[]
}

export interface TransportationInfo {
  matatu: string[]
  bus: string[]
  taxi: string[]
  other: string[]
}

/**
 * Create a Google Maps URL for a Kenyan address
 */
export function createGoogleMapsUrl(address: KenyanAddress, action: 'search' | 'directions' = 'search'): string {
  const formatted = formatKenyanAddress(address)
  const query = encodeURIComponent(formatted.googleMapsQuery)
  
  if (action === 'directions') {
    return `https://www.google.com/maps/dir/?api=1&destination=${query}`
  }
  
  return `https://www.google.com/maps/search/?api=1&query=${query}`
}

/**
 * Validate and normalize Kenyan address components
 */
export function normalizeKenyanAddress(address: Partial<KenyanAddress>): KenyanAddress {
  return {
    businessName: address.businessName?.trim(),
    buildingName: address.buildingName?.trim(),
    streetAddress: address.streetAddress?.trim(),
    area: address.area?.trim() || '',
    county: address.county?.trim() || '',
    region: address.region?.trim(),
    postalCode: address.postalCode ? formatKenyanPostalCode(address.postalCode) : undefined,
    poBox: address.poBox?.trim(),
    landmark: address.landmark?.trim(),
    coordinates: address.coordinates
  }
}
