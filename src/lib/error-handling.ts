/**
 * Comprehensive Error Handling and Monitoring System for PagesLab
 * Implements retry logic, user-friendly error messages, and monitoring integration
 */

export interface ErrorContext {
  userId?: string
  sessionId?: string
  userAgent?: string
  url?: string
  timestamp: string
  component?: string
  action?: string
  metadata?: Record<string, any>
}

export interface ErrorDetails {
  code: string
  message: string
  userMessage: string
  severity: 'low' | 'medium' | 'high' | 'critical'
  category: 'network' | 'validation' | 'generation' | 'auth' | 'system' | 'user'
  retryable: boolean
  context: ErrorContext
  stack?: string
  originalError?: Error
}

export interface RetryConfig {
  maxAttempts: number
  baseDelay: number
  maxDelay: number
  backoffMultiplier: number
  retryableErrors: string[]
}

export interface MonitoringEvent {
  type: 'error' | 'warning' | 'info' | 'performance'
  message: string
  data?: Record<string, any>
  timestamp: string
  context: ErrorContext
}

// Error codes and messages
export const ERROR_CODES = {
  // Network errors
  NETWORK_TIMEOUT: 'NETWORK_TIMEOUT',
  NETWORK_UNAVAILABLE: 'NETWORK_UNAVAILABLE',
  API_RATE_LIMIT: 'API_RATE_LIMIT',
  
  // Generation errors
  GENERATION_FAILED: 'GENERATION_FAILED',
  IMAGE_OPTIMIZATION_FAILED: 'IMAGE_OPTIMIZATION_FAILED',
  
  // Validation errors
  INVALID_INPUT: 'INVALID_INPUT',
  CONTENT_TOO_SHORT: 'CONTENT_TOO_SHORT',
  CONTENT_TOO_LONG: 'CONTENT_TOO_LONG',
  INAPPROPRIATE_CONTENT: 'INAPPROPRIATE_CONTENT',
  
  // Authentication errors
  UNAUTHORIZED: 'UNAUTHORIZED',
  SESSION_EXPIRED: 'SESSION_EXPIRED',
  INVALID_CREDENTIALS: 'INVALID_CREDENTIALS',
  
  // System errors
  DATABASE_ERROR: 'DATABASE_ERROR',
  CACHE_ERROR: 'CACHE_ERROR',
  FILE_UPLOAD_ERROR: 'FILE_UPLOAD_ERROR',
  INTERNAL_ERROR: 'INTERNAL_ERROR'
} as const

export const ERROR_MESSAGES = {
  [ERROR_CODES.NETWORK_TIMEOUT]: {
    user: 'Connection timed out. Please check your internet connection and try again.',
    userSw: 'Muda umekwisha. Tafadhali angalia muunganisho wako wa mtandao na ujaribu tena.',
    technical: 'Network request timed out after specified duration'
  },
  [ERROR_CODES.NETWORK_UNAVAILABLE]: {
    user: 'Unable to connect to our servers. Please check your internet connection.',
    userSw: 'Haiwezi kuunganisha na seva zetu. Tafadhali angalia muunganisho wako wa mtandao.',
    technical: 'Network is unavailable or server is unreachable'
  },
  [ERROR_CODES.API_RATE_LIMIT]: {
    user: 'Too many requests. Please wait a moment before trying again.',
    userSw: 'Maombi mengi sana. Tafadhali subiri kidogo kabla ya kujaribu tena.',
    technical: 'API rate limit exceeded'
  },
  [ERROR_CODES.GENERATION_FAILED]: {
    user: 'Website generation failed. Please try again with a different description.',
    userSw: 'Uundaji wa tovuti umeshindwa. Tafadhali jaribu tena na maelezo tofauti.',
    technical: 'Website generation process failed'
  },

  [ERROR_CODES.IMAGE_OPTIMIZATION_FAILED]: {
    user: 'Image processing failed. Please try uploading a different image.',
    userSw: 'Uchakataji wa picha umeshindwa. Tafadhali jaribu kupakia picha nyingine.',
    technical: 'Image optimization process failed'
  },
  [ERROR_CODES.INVALID_INPUT]: {
    user: 'Please provide valid information and try again.',
    userSw: 'Tafadhali toa taarifa sahihi na ujaribu tena.',
    technical: 'Input validation failed'
  },
  [ERROR_CODES.CONTENT_TOO_SHORT]: {
    user: 'Please provide a more detailed description of your business.',
    userSw: 'Tafadhali toa maelezo ya kina zaidi ya biashara yako.',
    technical: 'Content length below minimum requirement'
  },
  [ERROR_CODES.CONTENT_TOO_LONG]: {
    user: 'Description is too long. Please keep it under 5000 characters.',
    userSw: 'Maelezo ni marefu sana. Tafadhali yaweke chini ya herufi 5000.',
    technical: 'Content length exceeds maximum limit'
  },
  [ERROR_CODES.INAPPROPRIATE_CONTENT]: {
    user: 'Content contains inappropriate material. Please review and modify.',
    userSw: 'Maudhui yana vitu visivyofaa. Tafadhali kagua na urekebishe.',
    technical: 'Content flagged as inappropriate'
  },
  [ERROR_CODES.UNAUTHORIZED]: {
    user: 'Please log in to access this feature.',
    userSw: 'Tafadhali ingia ili kufikia kipengele hiki.',
    technical: 'User not authenticated'
  },
  [ERROR_CODES.SESSION_EXPIRED]: {
    user: 'Your session has expired. Please log in again.',
    userSw: 'Kipindi chako kimekwisha. Tafadhali ingia tena.',
    technical: 'User session has expired'
  },
  [ERROR_CODES.INVALID_CREDENTIALS]: {
    user: 'Invalid email or password. Please try again.',
    userSw: 'Barua pepe au nenosiri si sahihi. Tafadhali jaribu tena.',
    technical: 'Authentication credentials are invalid'
  },
  [ERROR_CODES.DATABASE_ERROR]: {
    user: 'A temporary issue occurred. Please try again in a moment.',
    userSw: 'Tatizo la muda limejitokeza. Tafadhali jaribu tena baada ya muda.',
    technical: 'Database operation failed'
  },
  [ERROR_CODES.CACHE_ERROR]: {
    user: 'Loading may take a bit longer than usual. Please wait.',
    userSw: 'Upakiaji unaweza kuchukua muda mrefu kuliko kawaida. Tafadhali subiri.',
    technical: 'Cache operation failed'
  },
  [ERROR_CODES.FILE_UPLOAD_ERROR]: {
    user: 'File upload failed. Please try again with a different file.',
    userSw: 'Upakiaji wa faili umeshindwa. Tafadhali jaribu tena na faili nyingine.',
    technical: 'File upload operation failed'
  },
  [ERROR_CODES.INTERNAL_ERROR]: {
    user: 'An unexpected error occurred. Our team has been notified.',
    userSw: 'Hitilafu isiyotarajiwa imetokea. Timu yetu imearifiwa.',
    technical: 'Internal server error'
  }
} as const

export class ErrorHandler {
  private static instance: ErrorHandler
  private retryConfig: RetryConfig
  private monitoringEnabled: boolean

  constructor() {
    this.retryConfig = {
      maxAttempts: 3,
      baseDelay: 1000,
      maxDelay: 10000,
      backoffMultiplier: 2,
      retryableErrors: [
        ERROR_CODES.NETWORK_TIMEOUT,
        ERROR_CODES.NETWORK_UNAVAILABLE,
        ERROR_CODES.API_RATE_LIMIT,
        ERROR_CODES.DATABASE_ERROR,
        ERROR_CODES.CACHE_ERROR
      ]
    }
    this.monitoringEnabled = process.env.NODE_ENV === 'production'
  }

  static getInstance(): ErrorHandler {
    if (!ErrorHandler.instance) {
      ErrorHandler.instance = new ErrorHandler()
    }
    return ErrorHandler.instance
  }

  /**
   * Create a standardized error object
   */
  createError(
    code: keyof typeof ERROR_CODES,
    originalError?: Error,
    context?: Partial<ErrorContext>,
    metadata?: Record<string, any>
  ): ErrorDetails {
    const errorCode = ERROR_CODES[code]
    const messages = ERROR_MESSAGES[errorCode]
    
    const errorContext: ErrorContext = {
      timestamp: new Date().toISOString(),
      url: typeof window !== 'undefined' ? window.location.href : undefined,
      userAgent: typeof window !== 'undefined' ? window.navigator.userAgent : undefined,
      ...context
    }

    const severity = this.determineSeverity(errorCode)
    const category = this.determineCategory(errorCode)

    return {
      code: errorCode,
      message: messages.technical,
      userMessage: messages.user,
      severity,
      category,
      retryable: this.retryConfig.retryableErrors.includes(errorCode),
      context: errorContext,
      stack: originalError?.stack,
      originalError,
      ...metadata && { metadata }
    }
  }

  /**
   * Handle error with automatic retry logic
   */
  async handleWithRetry<T>(
    operation: () => Promise<T>,
    context?: Partial<ErrorContext>,
    customRetryConfig?: Partial<RetryConfig>
  ): Promise<T> {
    const config = { ...this.retryConfig, ...customRetryConfig }
    let lastError: ErrorDetails | null = null

    for (let attempt = 1; attempt <= config.maxAttempts; attempt++) {
      try {
        return await operation()
      } catch (error) {
        const errorDetails = this.processError(error, context)
        lastError = errorDetails

        // Log the error
        this.logError(errorDetails, { attempt, maxAttempts: config.maxAttempts })

        // Check if error is retryable and we have attempts left
        if (!errorDetails.retryable || attempt === config.maxAttempts) {
          throw errorDetails
        }

        // Calculate delay for next attempt
        const delay = Math.min(
          config.baseDelay * Math.pow(config.backoffMultiplier, attempt - 1),
          config.maxDelay
        )

        // Wait before retrying
        await this.delay(delay)
      }
    }

    throw lastError
  }

  /**
   * Process any error into standardized format
   */
  processError(error: any, context?: Partial<ErrorContext>): ErrorDetails {
    // If it's already an ErrorDetails object, return it
    if (error && typeof error === 'object' && 'code' in error && 'userMessage' in error) {
      return error as ErrorDetails
    }

    // Handle different error types
    if (error instanceof TypeError && error.message.includes('fetch')) {
      return this.createError('NETWORK_UNAVAILABLE', error, context)
    }

    if (error instanceof Error && error.message.includes('timeout')) {
      return this.createError('NETWORK_TIMEOUT', error, context)
    }

    if (error?.status === 429) {
      return this.createError('API_RATE_LIMIT', error, context)
    }

    if (error?.status === 401) {
      return this.createError('UNAUTHORIZED', error, context)
    }

    if (error?.status === 403) {
      return this.createError('SESSION_EXPIRED', error, context)
    }

    // Default to internal error
    return this.createError('INTERNAL_ERROR', error instanceof Error ? error : new Error(String(error)), context)
  }

  /**
   * Log error to monitoring system
   */
  logError(error: ErrorDetails, metadata?: Record<string, any>): void {
    const logData = {
      ...error,
      ...metadata && {
        context: {
          ...error.context,
          metadata: { ...error.context.metadata, ...metadata }
        }
      }
    }

    // Console logging for development
    if (process.env.NODE_ENV === 'development') {
      console.error('🚨 Error:', logData)
    }

    // Send to monitoring service in production
    if (this.monitoringEnabled) {
      this.sendToMonitoring({
        type: 'error',
        message: error.message,
        data: logData,
        timestamp: error.context.timestamp,
        context: error.context
      })
    }
  }

  /**
   * Log performance metrics
   */
  logPerformance(metric: string, value: number, context?: Partial<ErrorContext>): void {
    const event: MonitoringEvent = {
      type: 'performance',
      message: `Performance metric: ${metric}`,
      data: { metric, value, unit: 'ms' },
      timestamp: new Date().toISOString(),
      context: {
        timestamp: new Date().toISOString(),
        ...context
      }
    }

    if (process.env.NODE_ENV === 'development') {
      console.log(`📊 Performance: ${metric} = ${value}ms`)
    }

    if (this.monitoringEnabled) {
      this.sendToMonitoring(event)
    }
  }

  /**
   * Get user-friendly error message
   */
  getUserMessage(error: ErrorDetails, language: 'en' | 'sw' = 'en'): string {
    const messages = ERROR_MESSAGES[error.code as keyof typeof ERROR_MESSAGES]
    if (!messages) return error.userMessage

    return language === 'sw' && messages.userSw ? messages.userSw : messages.user
  }

  /**
   * Get recovery suggestions for an error
   */
  getRecoverySuggestions(error: ErrorDetails, language: 'en' | 'sw' = 'en'): string[] {
    const suggestions: { [key: string]: { en: string[], sw: string[] } } = {
      [ERROR_CODES.NETWORK_TIMEOUT]: {
        en: [
          'Check your internet connection',
          'Try refreshing the page',
          'Wait a moment and try again'
        ],
        sw: [
          'Angalia muunganisho wako wa mtandao',
          'Jaribu kuonyesha upya ukurasa',
          'Subiri kidogo na ujaribu tena'
        ]
      },
      [ERROR_CODES.GENERATION_FAILED]: {
        en: [
          'Try describing your business differently',
          'Make sure your description is detailed',
          'Check for any inappropriate content'
        ],
        sw: [
          'Jaribu kuelezea biashara yako kwa njia nyingine',
          'Hakikisha maelezo yako ni ya kina',
          'Angalia kama kuna maudhui yasiyofaa'
        ]
      }
    }

    const errorSuggestions = suggestions[error.code]
    if (!errorSuggestions) return []

    return language === 'sw' ? errorSuggestions.sw : errorSuggestions.en
  }

  // Private helper methods
  private determineSeverity(code: string): ErrorDetails['severity'] {
    const criticalErrors: string[] = [ERROR_CODES.INTERNAL_ERROR, ERROR_CODES.DATABASE_ERROR]
    const highErrors: string[] = [ERROR_CODES.GENERATION_FAILED, ERROR_CODES.UNAUTHORIZED]
    const mediumErrors: string[] = [ERROR_CODES.NETWORK_TIMEOUT]

    if (criticalErrors.includes(code)) return 'critical'
    if (highErrors.includes(code)) return 'high'
    if (mediumErrors.includes(code)) return 'medium'
    return 'low'
  }

  private determineCategory(code: string): ErrorDetails['category'] {
    const networkErrors: string[] = [ERROR_CODES.NETWORK_TIMEOUT, ERROR_CODES.NETWORK_UNAVAILABLE, ERROR_CODES.API_RATE_LIMIT]
    const validationErrors: string[] = [ERROR_CODES.INVALID_INPUT, ERROR_CODES.CONTENT_TOO_SHORT, ERROR_CODES.CONTENT_TOO_LONG]
    const generationErrors: string[] = [ERROR_CODES.GENERATION_FAILED, ERROR_CODES.IMAGE_OPTIMIZATION_FAILED]
    const authErrors: string[] = [ERROR_CODES.UNAUTHORIZED, ERROR_CODES.SESSION_EXPIRED, ERROR_CODES.INVALID_CREDENTIALS]
    const userErrors: string[] = [ERROR_CODES.INAPPROPRIATE_CONTENT]

    if (networkErrors.includes(code)) return 'network'
    if (validationErrors.includes(code)) return 'validation'
    if (generationErrors.includes(code)) return 'generation'
    if (authErrors.includes(code)) return 'auth'
    if (userErrors.includes(code)) return 'user'
    return 'system'
  }

  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms))
  }

  private sendToMonitoring(event: MonitoringEvent): void {
    // In production, this would send to Sentry, DataDog, or similar service
    // For now, we'll simulate the monitoring call
    if (typeof window !== 'undefined' && (window as any).gtag) {
      // Send to Google Analytics if available
      (window as any).gtag('event', 'exception', {
        description: event.message,
        fatal: event.type === 'error'
      })
    }

    // Simulate sending to monitoring service
    console.log('📡 Monitoring event:', event)
  }
}

// Singleton instance
export const errorHandler = ErrorHandler.getInstance()

// Utility functions
export const withErrorHandling = async <T>(
  operation: () => Promise<T>,
  context?: Partial<ErrorContext>
): Promise<T> => {
  return errorHandler.handleWithRetry(operation, context)
}

export const createError = (
  code: keyof typeof ERROR_CODES,
  originalError?: Error,
  context?: Partial<ErrorContext>
): ErrorDetails => {
  return errorHandler.createError(code, originalError, context)
}

export const logError = (error: ErrorDetails): void => {
  errorHandler.logError(error)
}

export const logPerformance = (metric: string, value: number, context?: Partial<ErrorContext>): void => {
  errorHandler.logPerformance(metric, value, context)
}
