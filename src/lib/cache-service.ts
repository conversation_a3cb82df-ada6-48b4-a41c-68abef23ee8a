/**
 * Cache Service for PagesLab
 * Implements multiple caching strategies for improved performance
 */

export interface CacheOptions {
  ttl?: number // Time to live in seconds
  maxSize?: number // Maximum cache size
  strategy?: 'lru' | 'fifo' | 'lfu'
}

export interface CacheEntry<T> {
  value: T
  timestamp: number
  accessCount: number
  ttl: number
}

export class MemoryCache<T> {
  private cache = new Map<string, CacheEntry<T>>()
  private options: Required<CacheOptions>

  constructor(options: CacheOptions = {}) {
    this.options = {
      ttl: options.ttl || 3600, // 1 hour default
      maxSize: options.maxSize || 100,
      strategy: options.strategy || 'lru'
    }
  }

  /**
   * Get value from cache
   */
  get(key: string): T | null {
    const entry = this.cache.get(key)
    
    if (!entry) {
      return null
    }

    // Check if expired
    if (this.isExpired(entry)) {
      this.cache.delete(key)
      return null
    }

    // Update access count for LFU strategy
    entry.accessCount++
    
    return entry.value
  }

  /**
   * Set value in cache
   */
  set(key: string, value: T, customTtl?: number): void {
    const ttl = customTtl || this.options.ttl
    const entry: CacheEntry<T> = {
      value,
      timestamp: Date.now(),
      accessCount: 1,
      ttl: ttl * 1000 // Convert to milliseconds
    }

    // Check if we need to evict entries
    if (this.cache.size >= this.options.maxSize) {
      this.evict()
    }

    this.cache.set(key, entry)
  }

  /**
   * Check if entry has expired
   */
  private isExpired(entry: CacheEntry<T>): boolean {
    return Date.now() - entry.timestamp > entry.ttl
  }

  /**
   * Evict entries based on strategy
   */
  private evict(): void {
    if (this.cache.size === 0) return

    let keyToEvict: string

    switch (this.options.strategy) {
      case 'lru':
        keyToEvict = this.findLRUKey()
        break
      case 'lfu':
        keyToEvict = this.findLFUKey()
        break
      case 'fifo':
      default:
        const firstKey = this.cache.keys().next().value
        keyToEvict = firstKey || ''
        break
    }

    this.cache.delete(keyToEvict)
  }

  /**
   * Find least recently used key
   */
  private findLRUKey(): string {
    let oldestKey = ''
    let oldestTime = Date.now()

    for (const [key, entry] of this.cache.entries()) {
      if (entry.timestamp < oldestTime) {
        oldestTime = entry.timestamp
        oldestKey = key
      }
    }

    return oldestKey
  }

  /**
   * Find least frequently used key
   */
  private findLFUKey(): string {
    let leastUsedKey = ''
    let leastCount = Infinity

    for (const [key, entry] of this.cache.entries()) {
      if (entry.accessCount < leastCount) {
        leastCount = entry.accessCount
        leastUsedKey = key
      }
    }

    return leastUsedKey
  }

  /**
   * Clear all cache entries
   */
  clear(): void {
    this.cache.clear()
  }

  /**
   * Get cache statistics
   */
  getStats(): {
    size: number
    maxSize: number
    hitRate: number
    entries: Array<{ key: string; accessCount: number; age: number }>
  } {
    const entries = Array.from(this.cache.entries()).map(([key, entry]) => ({
      key,
      accessCount: entry.accessCount,
      age: Date.now() - entry.timestamp
    }))

    return {
      size: this.cache.size,
      maxSize: this.options.maxSize,
      hitRate: 0, // Would need to track hits/misses for accurate calculation
      entries
    }
  }
}

/**
 * Website Generation Cache
 * Caches generated websites and their components
 */
export class WebsiteCache {
  private websiteCache = new MemoryCache<any>({ ttl: 1800, maxSize: 50 }) // 30 minutes
  private imageCache = new MemoryCache<any>({ ttl: 3600, maxSize: 200 }) // 1 hour
  private contentCache = new MemoryCache<any>({ ttl: 900, maxSize: 100 }) // 15 minutes

  /**
   * Generate cache key from business description
   */
  private generateCacheKey(businessDescription: string, options: any = {}): string {
    const optionsStr = JSON.stringify(options)
    const hash = this.simpleHash(businessDescription + optionsStr)
    return `website_${hash}`
  }

  /**
   * Simple hash function for cache keys
   */
  private simpleHash(str: string): string {
    let hash = 0
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i)
      hash = ((hash << 5) - hash) + char
      hash = hash & hash // Convert to 32-bit integer
    }
    return Math.abs(hash).toString(36)
  }

  /**
   * Get cached website
   */
  getCachedWebsite(businessDescription: string, options: any = {}): any | null {
    const key = this.generateCacheKey(businessDescription, options)
    return this.websiteCache.get(key)
  }

  /**
   * Cache generated website
   */
  cacheWebsite(businessDescription: string, website: any, options: any = {}): void {
    const key = this.generateCacheKey(businessDescription, options)
    this.websiteCache.set(key, website)
    
    console.log(`🗄️ Cached website: ${key}`)
  }

  /**
   * Get cached images for business type
   */
  getCachedImages(businessType: string, keywords: string[]): any[] | null {
    const key = `images_${businessType}_${keywords.join('_')}`
    return this.imageCache.get(key)
  }

  /**
   * Cache images for business type
   */
  cacheImages(businessType: string, keywords: string[], images: any[]): void {
    const key = `images_${businessType}_${keywords.join('_')}`
    this.imageCache.set(key, images)
    
    console.log(`🖼️ Cached images: ${key}`)
  }

  /**
   * Get cached AI-generated content
   */
  getCachedContent(prompt: string, type: string): any | null {
    const key = `content_${type}_${this.simpleHash(prompt)}`
    return this.contentCache.get(key)
  }

  /**
   * Cache AI-generated content
   */
  cacheContent(prompt: string, type: string, content: any): void {
    const key = `content_${type}_${this.simpleHash(prompt)}`
    this.contentCache.set(key, content)
    
    console.log(`📝 Cached content: ${key}`)
  }

  /**
   * Clear all caches
   */
  clearAll(): void {
    this.websiteCache.clear()
    this.imageCache.clear()
    this.contentCache.clear()
    
    console.log('🗑️ All caches cleared')
  }

  /**
   * Get cache statistics
   */
  getStats(): {
    websites: any
    images: any
    content: any
  } {
    return {
      websites: this.websiteCache.getStats(),
      images: this.imageCache.getStats(),
      content: this.contentCache.getStats()
    }
  }
}

/**
 * Browser Cache Utilities
 * For client-side caching in generated websites
 */
export class BrowserCacheUtils {
  /**
   * Set cache headers for static assets
   */
  static getCacheHeaders(assetType: 'image' | 'css' | 'js' | 'html'): Record<string, string> {
    const headers: Record<string, string> = {}

    switch (assetType) {
      case 'image':
        headers['Cache-Control'] = 'public, max-age=31536000, immutable' // 1 year
        break
      case 'css':
      case 'js':
        headers['Cache-Control'] = 'public, max-age=31536000, immutable' // 1 year
        headers['ETag'] = `"${Date.now()}"` // Simple ETag
        break
      case 'html':
        headers['Cache-Control'] = 'public, max-age=300, must-revalidate' // 5 minutes
        break
    }

    return headers
  }

  /**
   * Generate service worker for offline caching
   */
  static generateServiceWorker(): string {
    return `
      const CACHE_NAME = 'pageslab-v1';
      const urlsToCache = [
        '/',
        '/styles.css',
        '/script.js'
      ];

      self.addEventListener('install', event => {
        event.waitUntil(
          caches.open(CACHE_NAME)
            .then(cache => cache.addAll(urlsToCache))
        );
      });

      self.addEventListener('fetch', event => {
        event.respondWith(
          caches.match(event.request)
            .then(response => {
              if (response) {
                return response;
              }
              return fetch(event.request);
            })
        );
      });
    `
  }
}

// Export singleton instance
export const websiteCache = new WebsiteCache()
