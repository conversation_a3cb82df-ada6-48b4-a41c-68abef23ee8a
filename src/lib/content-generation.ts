import OpenAI from 'openai'
import type {
  BusinessProfile,
  KenyanRegion,
  BusinessType,
  CulturalContext,
  GeneratedContent
} from '@/types'
import { culturalLocalizationService } from './cultural-localization'
import { withErrorHandling, createError, errorHandler } from './error-handling'

/**
 * Content Generation Service
 * Integrates OpenAI GPT-4o API for culturally-aware content generation
 * tailored for Kenyan businesses with local terminology and cultural phrases
 */
export class ContentGenerationService {
  private openai: OpenAI | null = null
  private contentCache: Map<string, GeneratedContent> = new Map()

  constructor() {
    // Initialize OpenAI client if API key is available
    const apiKey = process.env.OPENAI_API_KEY
    if (apiKey) {
      this.openai = new OpenAI({
        apiKey: apiKey
      })
    }
  }

  /**
   * Generate complete website content for a business
   */
  async generateWebsiteContent(
    businessProfile: BusinessProfile,
    culturalContext: CulturalContext
  ): Promise<any> {
    // TEMPORARILY DISABLE CACHE FOR TESTING - Generate fresh content every time
    console.log('🔄 Cache disabled for testing - generating fresh content...')
    console.log('🔍 CONTENT DEBUG - Business Name:', businessProfile.name)
    console.log('🔍 CONTENT DEBUG - Business Description:', businessProfile.description)
    console.log('🔍 CONTENT DEBUG - OpenAI Available:', this.isAIAvailable())

    try {
      console.log('🤖 Attempting AI content generation...')
      const content = await this.generateContentWithAI(businessProfile, culturalContext)
      console.log('✅ AI content generation successful!')
      console.log('🔍 CONTENT DEBUG - Generated headline:', content.heroSection?.headline)
      // Don't cache during testing
      return content
    } catch (error) {
      console.error('❌ Error generating content with AI:', error)
      console.log('🔄 Falling back to template-based generation...')
      // Fallback to template-based generation
      const templateContent = this.generateContentWithTemplates(businessProfile, culturalContext)
      console.log('🔍 CONTENT DEBUG - Template headline:', templateContent.heroSection?.headline)
      return templateContent
    }
  }

  /**
   * Generate headlines for different sections
   */
  async generateHeadlines(
    businessProfile: BusinessProfile,
    culturalContext: CulturalContext
  ): Promise<HeadlineSet> {
    const localizedTerminology = culturalLocalizationService.getLocalizedBusinessTerminology(
      culturalContext.region,
      businessProfile.type
    )

    if (this.openai) {
      try {
        const prompt = this.buildHeadlinePrompt(businessProfile, culturalContext, localizedTerminology)
        const response = await this.openai.chat.completions.create({
          model: 'gpt-4o',
          messages: [{ role: 'user', content: prompt }],
          max_tokens: 300,
          temperature: 0.7
        })

        const aiContent = response.choices[0]?.message?.content
        if (aiContent) {
          return this.parseHeadlinesFromAI(aiContent, localizedTerminology)
        }
      } catch (error) {
        console.error('Error generating headlines with AI:', error)
      }
    }

    // Fallback to template-based headlines
    return this.generateTemplateHeadlines(businessProfile, culturalContext, localizedTerminology)
  }

  /**
   * Generate business descriptions
   */
  async generateDescriptions(
    businessProfile: BusinessProfile,
    culturalContext: CulturalContext
  ): Promise<DescriptionSet> {
    const localizedTerminology = culturalLocalizationService.getLocalizedBusinessTerminology(
      culturalContext.region,
      businessProfile.type
    )

    if (this.openai) {
      try {
        const result = await withErrorHandling(async () => {
          const prompt = this.buildDescriptionPrompt(businessProfile, culturalContext, localizedTerminology)
          const response = await this.openai!.chat.completions.create({
            model: 'gpt-4o',
            messages: [{ role: 'user', content: prompt }],
            max_tokens: 500,
            temperature: 0.7
          })

          const aiContent = response.choices[0]?.message?.content
          if (!aiContent) {
            throw createError('GENERATION_FAILED', new Error('No content generated by AI'), {
              component: 'ContentGenerationService',
              action: 'generateDescriptions'
            })
          }

          return this.parseDescriptionsFromAI(aiContent, localizedTerminology)
        }, {
          component: 'ContentGenerationService',
          action: 'generateDescriptions',
          metadata: { businessType: businessProfile.type, region: culturalContext.region }
        })

        return result
      } catch (error) {
        console.error('Error generating descriptions with AI:', error)
        errorHandler.logError(errorHandler.processError(error))
      }
    }

    // Fallback to template-based descriptions
    return this.generateTemplateDescriptions(businessProfile, culturalContext, localizedTerminology)
  }

  /**
   * Generate service listings
   */
  async generateServices(
    businessProfile: BusinessProfile,
    culturalContext: CulturalContext
  ): Promise<ServiceItem[]> {
    const localizedTerminology = culturalLocalizationService.getLocalizedBusinessTerminology(
      culturalContext.region,
      businessProfile.type
    )

    if (this.openai) {
      try {
        const result = await withErrorHandling(async () => {
          const prompt = this.buildServicesPrompt(businessProfile, culturalContext, localizedTerminology)
          const response = await this.openai!.chat.completions.create({
            model: 'gpt-4o',
            messages: [{ role: 'user', content: prompt }],
            max_tokens: 400,
            temperature: 0.7
          })

          const aiContent = response.choices[0]?.message?.content
          if (!aiContent) {
            throw createError('GENERATION_FAILED', new Error('No services generated by AI'), {
              component: 'ContentGenerationService',
              action: 'generateServices'
            })
          }

          return this.parseServicesFromAI(aiContent, localizedTerminology)
        }, {
          component: 'ContentGenerationService',
          action: 'generateServices',
          metadata: { businessType: businessProfile.type, region: culturalContext.region }
        })

        return result
      } catch (error) {
        console.error('Error generating services with AI:', error)
        errorHandler.logError(errorHandler.processError(error))
      }
    }

    // Fallback to template-based services
    return this.generateTemplateServices(businessProfile, culturalContext, localizedTerminology)
  }

  /**
   * Generate culturally-appropriate call-to-action phrases
   */
  generateCallToActions(
    businessProfile: BusinessProfile,
    culturalContext: CulturalContext
  ): CallToActionSet {
    const localizedTerminology = culturalLocalizationService.getLocalizedBusinessTerminology(
      culturalContext.region,
      businessProfile.type
    )

    const regionalCTAs = this.getRegionalCallToActions(culturalContext.region)
    const businessTypeCTAs = this.getBusinessTypeCallToActions(businessProfile.type)

    return {
      primary: this.localizeCallToAction(regionalCTAs.primary, localizedTerminology),
      secondary: this.localizeCallToAction(regionalCTAs.secondary, localizedTerminology),
      contact: this.localizeCallToAction(businessTypeCTAs.contact || regionalCTAs.contact, localizedTerminology),
      whatsapp: this.localizeCallToAction(regionalCTAs.whatsapp, localizedTerminology),
      location: this.localizeCallToAction(regionalCTAs.location, localizedTerminology)
    }
  }

  /**
   * Generate content using AI with cultural awareness
   */
  private async generateContentWithAI(
    businessProfile: BusinessProfile,
    culturalContext: CulturalContext
  ): Promise<any> {
    if (!this.openai) {
      throw new Error('OpenAI client not initialized')
    }

    const localizedTerminology = culturalLocalizationService.getLocalizedBusinessTerminology(
      culturalContext.region,
      businessProfile.type
    )

    const prompt = this.buildComprehensivePrompt(businessProfile, culturalContext, localizedTerminology)

    const response = await this.openai.chat.completions.create({
      model: 'gpt-4o',
      messages: [{ role: 'user', content: prompt }],
      max_tokens: 1000,
      temperature: 0.7
    })

    const aiContent = response.choices[0]?.message?.content
    if (!aiContent) {
      throw new Error('No content generated from AI')
    }

    return this.parseCompleteContentFromAI(aiContent, businessProfile, culturalContext)
  }

  /**
   * Generate content using culturally-aware templates
   */
  private generateContentWithTemplates(
    businessProfile: BusinessProfile,
    culturalContext: CulturalContext
  ): any {
    const localizedTerminology = culturalLocalizationService.getLocalizedBusinessTerminology(
      culturalContext.region,
      businessProfile.type
    )

    const headlines = this.generateTemplateHeadlines(businessProfile, culturalContext, localizedTerminology)
    const descriptions = this.generateTemplateDescriptions(businessProfile, culturalContext, localizedTerminology)
    const services = this.generateTemplateServices(businessProfile, culturalContext, localizedTerminology)
    const callToActionSet = this.generateCallToActions(businessProfile, culturalContext)

    return {
      // Core GeneratedContent interface properties
      headline: headlines.main,
      subheadline: headlines.tagline,
      aboutSection: descriptions.about,
      services,
      callToActions: callToActionSet,
      contactSection: this.generateContactSection(businessProfile, culturalContext),

      // Additional properties for backward compatibility and enhanced features
      headlines,
      descriptions,
      heroSection: {
        headline: headlines.main,
        subheadline: headlines.tagline,
        ctaText: 'Get Started'
      },
      servicesSection: {
        title: headlines.services,
        subtitle: 'Discover what we can do for you',
        services: services
      }
    }
  }

  /**
   * Build comprehensive AI prompt with cultural context
   */
  private buildComprehensivePrompt(
    businessProfile: BusinessProfile,
    culturalContext: CulturalContext,
    localizedTerminology: any
  ): string {
    const regionalTheme = culturalLocalizationService.getRegionalStyle(culturalContext.region)
    
    return `Generate website content for a Kenyan ${businessProfile.type} business with the following details:

Business Name: ${businessProfile.name}
Business Type: ${businessProfile.type} (${localizedTerminology.businessType})
Location: ${businessProfile.location.area}, ${businessProfile.location.county} (${culturalContext.region} region)
Services: ${businessProfile.services?.join(', ') || 'General services'}

Cultural Context:
- Region: ${culturalContext.region} (${regionalTheme.culturalMotifs.join(', ')})
- Primary Language: English only
- Business Context: Kenyan business environment
- Cultural Considerations: Kenyan business practices, mobile-first audience, M-Pesa payments

Please generate complete website content in this exact format:

HEADLINES:
1. Main Headline: "[Compelling main headline for ${businessProfile.name}]"
2. Tagline: "[Catchy tagline]"
3. About Section Header: "[About section title]"
4. Services Section Header: "[Services section title]"
5. Contact Section Header: "[Contact section title]"

DESCRIPTIONS:
6. Hero Description: "[Brief compelling description for hero section]"
7. About Description: "[About the business description]"
8. Why Choose Us: "[Why customers should choose this business]"

SERVICES:
9. Service 1: "[Service Name]: [Service Description]"
10. Service 2: "[Service Name]: [Service Description]"
11. Service 3: "[Service Name]: [Service Description]"
12. Service 4: "[Service Name]: [Service Description]"

Requirements:
- Generate all content in English only (no local language terms)
- Use professional, clear English appropriate for Kenyan business context
- Emphasize the business location and services
- Keep content concise and mobile-friendly
- Use warm, welcoming tone appropriate for Kenyan hospitality
- Make content specific to ${businessProfile.name} and ${businessProfile.type} business
- Include location context (${businessProfile.location.area}, ${businessProfile.location.county})
- For services, use actual services from the business: ${businessProfile.services?.join(', ') || 'General services'}

Generate exactly 12 lines as shown above with quotes around each piece of content.`
  }

  /**
   * Build headline-specific prompt
   */
  private buildHeadlinePrompt(
    businessProfile: BusinessProfile,
    culturalContext: CulturalContext,
    localizedTerminology: any
  ): string {
    return `Create compelling headlines for a ${businessProfile.type} in ${businessProfile.location}, Kenya.

Business: ${businessProfile.name}
Region: ${culturalContext.region}
Local term: ${localizedTerminology.businessType}
Greeting: ${localizedTerminology.greetings.general}

Generate:
1. Main headline (catchy, includes business type)
2. Tagline (short, memorable)
3. About section header
4. Services section header
5. Contact section header

Use Kenyan cultural elements and ${culturalContext.region} regional style. Keep mobile-friendly and welcoming.`
  }

  /**
   * Build description-specific prompt
   */
  private buildDescriptionPrompt(
    businessProfile: BusinessProfile,
    culturalContext: CulturalContext,
    localizedTerminology: any
  ): string {
    return `Write engaging descriptions for a ${businessProfile.type} in ${businessProfile.location}, Kenya.

Business: ${businessProfile.name}
Services: ${businessProfile.services?.join(', ') || 'General services'}
Region: ${culturalContext.region}

Generate:
1. Hero description (2-3 sentences, welcoming)
2. About section (3-4 sentences, builds trust)
3. Why choose us (2-3 key points)

Include:
- Local greeting: ${localizedTerminology.greetings.general}
- Cultural warmth and hospitality
- Mobile-first language
- Trust-building elements for Kenyan market`
  }

  /**
   * Build services-specific prompt
   */
  private buildServicesPrompt(
    businessProfile: BusinessProfile,
    culturalContext: CulturalContext,
    localizedTerminology: any
  ): string {
    return `List specific services for a ${businessProfile.type} in ${businessProfile.location}, Kenya.

Business Type: ${businessProfile.type} (${localizedTerminology.businessType})
Known Services: ${businessProfile.services?.join(', ') || 'Not specified'}
Region: ${culturalContext.region}

Generate 4-5 services with:
- Service name
- Brief description (1-2 sentences)
- Appropriate for ${culturalContext.region} region
- Include local terminology where natural
- Mobile-friendly descriptions`
  }

  /**
   * Parse complete content from AI response
   */
  private parseCompleteContentFromAI(
    aiContent: string,
    businessProfile: BusinessProfile,
    culturalContext: CulturalContext
  ): any {
    try {
      // Try to parse as JSON first
      const parsed = JSON.parse(aiContent)
      return this.normalizeAIContent(parsed, businessProfile, culturalContext)
    } catch {
      // Fallback to text parsing
      return this.parseTextContent(aiContent, businessProfile, culturalContext)
    }
  }

  /**
   * Parse headlines from AI response
   */
  private parseHeadlinesFromAI(aiContent: string, localizedTerminology: any): HeadlineSet {
    const lines = aiContent.split('\n').filter(line => line.trim())

    // Extract content from quotes if present, otherwise use the line as-is
    const extractContent = (line: string): string => {
      const match = line.match(/"([^"]+)"/)
      return match ? match[1] : line.replace(/^\d+\.\s*[^:]*:\s*/, '').trim()
    }

    return {
      main: extractContent(lines[0]) || `${localizedTerminology.greetings.general}! Welcome to our business`,
      tagline: extractContent(lines[1]) || `Quality ${localizedTerminology.businessType} services`,
      about: extractContent(lines[2]) || 'About Us',
      services: extractContent(lines[3]) || 'Our Services',
      contact: extractContent(lines[4]) || 'Contact Us'
    }
  }

  /**
   * Parse descriptions from AI response
   */
  private parseDescriptionsFromAI(aiContent: string, localizedTerminology: any): DescriptionSet {
    const sections = aiContent.split('\n\n')

    // Extract content from quotes if present
    const extractContent = (text: string | undefined): string => {
      if (!text || typeof text !== 'string') return ''
      const match = text.match(/"([^"]+)"/)
      return match ? match[1] : text.replace(/^\d+\.\s*[^:]*:\s*/, '').trim()
    }

    return {
      hero: extractContent(sections[0]) || `${localizedTerminology.greetings.general}! We provide quality services.`,
      about: extractContent(sections[1]) || `We are a trusted ${localizedTerminology.businessType} serving our community.`,
      whyChooseUs: extractContent(sections[2]) || 'Choose us for reliable, quality service.'
    }
  }

  /**
   * Parse services from AI response
   */
  private parseServicesFromAI(aiContent: string, localizedTerminology: any): ServiceItem[] {
    const lines = aiContent.split('\n').filter(line => line.trim())
    const services: ServiceItem[] = []

    // Extract content from quotes if present
    const extractContent = (text: string | undefined): string => {
      if (!text || typeof text !== 'string') return ''
      const match = text.match(/"([^"]+)"/)
      return match ? match[1] : text.replace(/^\d+\.\s*[^:]*:\s*/, '').trim()
    }

    for (let i = 0; i < Math.min(lines.length, 5); i++) {
      const line = lines[i]
      if (!line) continue
      const cleanLine = extractContent(line)
      const [name, ...descParts] = cleanLine.split(':')
      services.push({
        name: name.trim() || `Service ${i + 1}`,
        description: descParts.join(':').trim() || `Quality ${localizedTerminology.businessType} service`
      })
    }

    return services.length > 0 ? services : this.getDefaultServices(localizedTerminology)
  }

  /**
   * Generate template-based headlines
   */
  private generateTemplateHeadlines(
    businessProfile: BusinessProfile,
    culturalContext: CulturalContext,
    localizedTerminology: any
  ): HeadlineSet {
    const businessName = businessProfile.name
    const businessType = localizedTerminology.businessType
    const greeting = localizedTerminology.greetings.general
    
    return {
      main: `${greeting}! Welcome to ${businessName}`,
      tagline: `Your trusted ${businessType} in ${businessProfile.location}`,
      about: `About ${businessName}`,
      services: `Our ${businessType} Services`,
      contact: 'Get in Touch'
    }
  }

  /**
   * Generate template-based descriptions
   */
  private generateTemplateDescriptions(
    businessProfile: BusinessProfile,
    culturalContext: CulturalContext,
    localizedTerminology: any
  ): DescriptionSet {
    const businessName = businessProfile.name
    const businessType = localizedTerminology.businessType
    const welcome = localizedTerminology.commonPhrases.welcome
    const qualityService = localizedTerminology.commonPhrases.qualityService
    
    return {
      hero: `${welcome} to ${businessName}! We are your trusted ${businessType} providing ${qualityService} in ${businessProfile.location}.`,
      about: `${businessName} has been serving the ${businessProfile.location} community with dedication and excellence. We understand the needs of our customers and strive to provide the best ${businessType} experience.`,
      whyChooseUs: `Choose us for reliable service, competitive prices, and genuine care for our customers. We accept M-Pesa and other convenient payment methods.`
    }
  }

  /**
   * Generate template-based services
   */
  private generateTemplateServices(
    businessProfile: BusinessProfile,
    culturalContext: CulturalContext,
    localizedTerminology: any
  ): ServiceItem[] {
    const businessType = businessProfile.type.toLowerCase()
    const serviceTemplates = this.getServiceTemplates()
    
    return serviceTemplates[businessType] || this.getDefaultServices(localizedTerminology)
  }

  /**
   * Get service templates by business type
   */
  private getServiceTemplates(): Record<string, ServiceItem[]> {
    return {
      restaurant: [
        { name: 'Fresh Meals', description: 'Delicious, freshly prepared meals using local ingredients' },
        { name: 'Catering Services', description: 'Professional catering for events and celebrations' },
        { name: 'Takeaway & Delivery', description: 'Quick takeaway and delivery services' },
        { name: 'Special Occasions', description: 'Custom menus for weddings and special events' }
      ],
      shop: [
        { name: 'Quality Products', description: 'Wide selection of quality products at fair prices' },
        { name: 'Customer Service', description: 'Friendly and helpful customer service' },
        { name: 'Home Delivery', description: 'Convenient delivery to your doorstep' },
        { name: 'Bulk Orders', description: 'Special pricing for bulk purchases' }
      ],
      salon: [
        { name: 'Hair Styling', description: 'Professional hair cutting, styling, and treatments' },
        { name: 'Beauty Services', description: 'Makeup, manicure, and beauty treatments' },
        { name: 'Bridal Packages', description: 'Complete bridal beauty packages' },
        { name: 'Hair Products', description: 'Quality hair care products and accessories' }
      ],
      hotel: [
        { name: 'Comfortable Rooms', description: 'Clean, comfortable accommodation' },
        { name: 'Restaurant Services', description: 'On-site dining with local and international cuisine' },
        { name: 'Event Hosting', description: 'Conference rooms and event hosting facilities' },
        { name: 'Tour Assistance', description: 'Local tour guidance and travel assistance' }
      ],
      clinic: [
        { name: 'General Consultation', description: 'Professional medical consultation and diagnosis' },
        { name: 'Preventive Care', description: 'Health screenings and preventive care services' },
        { name: 'Emergency Care', description: '24/7 emergency medical services' },
        { name: 'Health Education', description: 'Community health education and awareness' }
      ]
    }
  }

  /**
   * Get default services
   */
  private getDefaultServices(localizedTerminology: any): ServiceItem[] {
    return [
      { name: 'Quality Service', description: `Professional ${localizedTerminology.businessType} services` },
      { name: 'Customer Care', description: 'Dedicated customer service and support' },
      { name: 'Convenient Location', description: 'Easy to find and accessible location' },
      { name: 'Fair Pricing', description: 'Competitive and transparent pricing' }
    ]
  }

  /**
   * Get regional call-to-actions
   */
  private getRegionalCallToActions(region: KenyanRegion): CallToActionSet {
    const regionalCTAs = {
      COASTAL: {
        primary: 'Visit Us Today',
        secondary: 'Learn More',
        contact: 'Call Now',
        whatsapp: 'WhatsApp Us',
        location: 'Find Us'
      },
      HIGHLAND: {
        primary: 'Get Started',
        secondary: 'View Services',
        contact: 'Contact Us',
        whatsapp: 'Chat on WhatsApp',
        location: 'Our Location'
      },
      WESTERN: {
        primary: 'Join Our Community',
        secondary: 'See What We Offer',
        contact: 'Reach Out',
        whatsapp: 'Message Us',
        location: 'Visit Our Shop'
      },
      NORTHERN: {
        primary: 'Connect With Us',
        secondary: 'Explore Services',
        contact: 'Get in Touch',
        whatsapp: 'WhatsApp Now',
        location: 'Come See Us'
      },
      CENTRAL: {
        primary: 'Book Now',
        secondary: 'Learn More',
        contact: 'Contact',
        whatsapp: 'WhatsApp',
        location: 'Location'
      },
      EASTERN: {
        primary: 'Experience Quality',
        secondary: 'Our Services',
        contact: 'Call Today',
        whatsapp: 'Chat Now',
        location: 'Find Us'
      }
    }

    return regionalCTAs[region] || regionalCTAs.CENTRAL
  }

  /**
   * Get business type specific call-to-actions
   */
  private getBusinessTypeCallToActions(businessType: BusinessType): Partial<CallToActionSet> {
    const businessCTAs: Partial<Record<BusinessType, Partial<CallToActionSet>>> = {
      RESTAURANT: { contact: 'Order Now', primary: 'Book Table' },
      SHOP: { contact: 'Shop Now', primary: 'Browse Products' },
      SALON: { contact: 'Book Appointment', primary: 'Schedule Visit' },
      HOTEL: { contact: 'Reserve Room', primary: 'Book Stay' },
      CLINIC: { contact: 'Schedule Appointment', primary: 'Book Consultation' },
      GARAGE: { contact: 'Book Service', primary: 'Schedule Repair' },
      SCHOOL: { contact: 'Inquire Now', primary: 'Learn More' },
      CHURCH: { contact: 'Contact Us', primary: 'Join Us' },
      CONSULTANCY: { contact: 'Book Consultation', primary: 'Get Started' },
      TECH_SERVICES: { contact: 'Get Quote', primary: 'Learn More' },
      AGRICULTURE: { contact: 'Contact Us', primary: 'View Products' },
      TRANSPORT: { contact: 'Book Ride', primary: 'Get Quote' },
      CONSTRUCTION: { contact: 'Get Quote', primary: 'View Services' },
      ENTERTAINMENT: { contact: 'Book Event', primary: 'Learn More' },
      OTHER: { contact: 'Contact Us', primary: 'Get Started' }
    }

    return businessCTAs[businessType] || { contact: 'Contact Us', primary: 'Get Started' }
  }

  /**
   * Localize call-to-action with cultural context (English only)
   */
  private localizeCallToAction(cta: string, localizedTerminology: any): string {
    // Return English-only call-to-actions
    const englishCTAs: Record<string, string> = {
      'Contact Us': 'Contact Us',
      'WhatsApp Us': 'WhatsApp Us',
      'Call Now': 'Call Now',
      'Visit Us': 'Visit Us',
      'Get Started': 'Get Started',
      'Learn More': 'Learn More'
    }

    return englishCTAs[cta] || cta
  }

  /**
   * Convert CallToActionSet to CallToAction array format
   */
  private convertCallToActionSetToArray(callToActionSet: CallToActionSet): import('@/types').CallToAction[] {
    return [
      {
        text: callToActionSet.primary,
        type: 'PRIMARY',
        action: 'contact'
      },
      {
        text: callToActionSet.secondary,
        type: 'SECONDARY',
        action: 'services'
      },
      {
        text: callToActionSet.contact,
        type: 'CONTACT',
        action: 'contact'
      },
      {
        text: callToActionSet.whatsapp,
        type: 'WHATSAPP',
        action: 'whatsapp'
      }
    ]
  }

  /**
   * Generate contact section for the business
   */
  private generateContactSection(
    businessProfile: BusinessProfile,
    culturalContext: CulturalContext
  ): import('@/types').ContactSection {
    const localizedTerminology = culturalLocalizationService.getLocalizedBusinessTerminology(
      culturalContext.region,
      businessProfile.type
    )

    const phone = businessProfile.contactInfo?.phone || ''
    const email = businessProfile.contactInfo?.email || ''
    const whatsapp = businessProfile.contactInfo?.whatsapp || phone
    const address = businessProfile.contactInfo?.address || `${businessProfile.location.area}, ${businessProfile.location.county}`

    return {
      title: 'Contact Us',
      description: `Get in touch with ${businessProfile.name} for quality ${localizedTerminology.businessType} services.`,
      methods: [
        ...(phone ? [{
          type: 'PHONE' as const,
          value: phone,
          label: 'Call Us',
          icon: 'phone'
        }] : []),
        ...(whatsapp ? [{
          type: 'WHATSAPP' as const,
          value: whatsapp,
          label: 'WhatsApp',
          icon: 'whatsapp'
        }] : []),
        ...(email ? [{
          type: 'EMAIL' as const,
          value: email,
          label: 'Email',
          icon: 'email'
        }] : [])
      ],
      location: {
        address: address,
        directions: `Located in ${businessProfile.location.area}, ${businessProfile.location.county}`
      }
    }
  }

  /**
   * Generate cache key for content
   */
  private generateCacheKey(businessProfile: BusinessProfile, culturalContext: CulturalContext): string {
    return `${businessProfile.name}-${businessProfile.type}-${businessProfile.location}-${culturalContext.region}`
  }

  /**
   * Normalize AI content to expected format
   */
  private normalizeAIContent(
    parsed: any,
    businessProfile: BusinessProfile,
    culturalContext: CulturalContext
  ): any {
    const localizedTerminology = culturalLocalizationService.getLocalizedBusinessTerminology(
      culturalContext.region,
      businessProfile.type
    )

    const headlines = parsed.headlines || this.generateTemplateHeadlines(businessProfile, culturalContext, localizedTerminology)
    const descriptions = parsed.descriptions || this.generateTemplateDescriptions(businessProfile, culturalContext, localizedTerminology)
    const services = parsed.services || this.generateTemplateServices(businessProfile, culturalContext, localizedTerminology)
    const callToActionSet = parsed.callToActions || this.generateCallToActions(businessProfile, culturalContext)

    return {
      // Core GeneratedContent interface properties
      headline: headlines.main,
      subheadline: headlines.tagline,
      aboutSection: descriptions.about,
      services,
      callToActions: callToActionSet,
      contactSection: this.generateContactSection(businessProfile, culturalContext),

      // Additional properties for backward compatibility and enhanced features
      headlines,
      descriptions,
      heroSection: {
        headline: headlines.main,
        subheadline: headlines.tagline,
        ctaText: 'Get Started'
      },
      servicesSection: {
        title: headlines.services,
        subtitle: 'Discover what we can do for you',
        services: services
      }
    }
  }

  /**
   * Parse text content when JSON parsing fails
   */
  private parseTextContent(
    aiContent: string,
    businessProfile: BusinessProfile,
    culturalContext: CulturalContext
  ): any {
    // Parse the complete AI content with new format
    const localizedTerminology = culturalLocalizationService.getLocalizedBusinessTerminology(
      culturalContext.region,
      businessProfile.type
    )

    const lines = aiContent.split('\n').filter(line => line.trim() && !line.match(/^(HEADLINES|DESCRIPTIONS|SERVICES):?\s*$/i))

    // Extract content from quotes if present
    const extractContent = (text: string | undefined): string => {
      if (!text || typeof text !== 'string') return ''
      const match = text.match(/"([^"]+)"/)
      return match ? match[1] : text.replace(/^\d+\.\s*[^:]*:\s*/, '').trim()
    }

    // Find content lines by looking for numbered items
    const headlineLines = lines.filter(line => line.match(/^[1-5]\./))
    const descriptionLines = lines.filter(line => line.match(/^[6-8]\./))
    const serviceLines = lines.filter(line => line.match(/^(9|10|11|12)\./))

    // Parse headlines (items 1-5)
    const headlines = {
      main: extractContent(headlineLines[0]) || `Welcome to ${businessProfile.name}`,
      tagline: extractContent(headlineLines[1]) || `Quality ${localizedTerminology.businessType} services`,
      about: extractContent(headlineLines[2]) || 'About Us',
      services: extractContent(headlineLines[3]) || 'Our Services',
      contact: extractContent(headlineLines[4]) || 'Contact Us'
    }

    // Parse descriptions (items 6-8)
    const descriptions = {
      hero: extractContent(descriptionLines[0]) || headlines.main,
      about: extractContent(descriptionLines[1]) || `We are a trusted ${localizedTerminology.businessType} serving our community.`,
      whyChooseUs: extractContent(descriptionLines[2]) || 'Choose us for reliable, quality service.'
    }

    // Parse services (items 9-12)
    const services = []
    for (const line of serviceLines) {
      if (!line) continue
      const cleanLine = extractContent(line)
      const [name, ...descParts] = cleanLine.split(':')
      if (name && name.trim()) {
        services.push({
          name: name.trim(),
          description: descParts.join(':').trim() || `Quality ${localizedTerminology.businessType} service`
        })
      }
    }

    // Fallback to default services if parsing failed
    if (services.length === 0) {
      services.push(...this.getDefaultServices(localizedTerminology))
    }

    const callToActionSet = this.generateCallToActions(businessProfile, culturalContext)

    return {
      headlines,
      descriptions,
      services,
      callToActions: callToActionSet,
      contactSection: this.generateContactSection(businessProfile, culturalContext),
      // Add heroSection for premium template system compatibility
      heroSection: {
        headline: headlines.main,
        subheadline: headlines.tagline,
        ctaText: 'Get Started'
      },
      // Add aboutSection for premium template system compatibility
      aboutSection: {
        title: headlines.about,
        description: descriptions.about,
        features: [
          'Quality Service',
          'Expert Team',
          'Customer Focus'
        ]
      },
      // Add servicesSection for premium template system compatibility
      servicesSection: {
        title: headlines.services,
        subtitle: 'Discover what we can do for you',
        services: services
      }
    }
  }

  /**
   * Clear content cache
   */
  clearCache(): void {
    this.contentCache.clear()
  }

  /**
   * Check if OpenAI is available
   */
  isAIAvailable(): boolean {
    return this.openai !== null
  }
}

// Type definitions for content generation
export interface HeadlineSet {
  main: string
  tagline: string
  about: string
  services: string
  contact: string
}

export interface DescriptionSet {
  hero: string
  about: string
  whyChooseUs: string
}

export interface ServiceItem {
  name: string
  description: string
}

export interface CallToActionSet {
  primary: string
  secondary: string
  contact: string
  whatsapp: string
  location: string
}

// Export singleton instance
export const contentGenerationService = new ContentGenerationService()
