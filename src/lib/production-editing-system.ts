/**
 * Production-Grade Multi-Stage Editing System
 * 
 * This system implements a robust, multi-stage validation pipeline for website editing
 * that ensures precision, safety, and user control over all changes.
 */

import { OpenAI } from 'openai'

export interface EditProposal {
  id: string
  description: string
  changes: EditChange[]
  riskLevel: 'low' | 'medium' | 'high'
  confidence: number
  estimatedImpact: string
  previewHtml: string
}

export interface EditChange {
  type: 'add' | 'modify' | 'remove'
  element: string
  before?: string
  after?: string
  location: string
}

export interface ValidationResult {
  isValid: boolean
  issues: string[]
  warnings: string[]
  score: number
  recommendations: string[]
}

export interface EditIntent {
  action: string
  target?: string
  scope: 'element' | 'section' | 'page'
  priority: 'low' | 'medium' | 'high'
}

export class ProductionEditingSystem {
  private openai: OpenAI

  constructor() {
    this.openai = new OpenAI({
      baseURL: 'https://openrouter.ai/api/v1',
      apiKey: process.env.OPENROUTER_API_KEY,
    })
  }

  /**
   * Stage 1: Parse and understand edit intent
   */
  async parseEditIntent(instruction: string): Promise<EditIntent> {
    const prompt = `Analyze this editing instruction and extract the intent:

INSTRUCTION: "${instruction}"

Determine:
1. What action is being requested (add, modify, remove, style, etc.)
2. What is the target (specific element, section, or entire page)
3. What is the scope (element-level, section-level, or page-level)
4. What is the priority/urgency

Return JSON: {
  "action": "string",
  "target": "string or null",
  "scope": "element|section|page", 
  "priority": "low|medium|high"
}`

    const response = await this.openai.chat.completions.create({
      model: 'deepseek/deepseek-r1-0528:free',
      messages: [
        { role: 'system', content: 'You are an expert at understanding web editing instructions.' },
        { role: 'user', content: prompt }
      ],
      temperature: 0.1,
      max_tokens: 500
    })

    try {
      const result = JSON.parse(response.choices[0]?.message?.content || '{}')
      return {
        action: result.action || 'modify',
        target: result.target || null,
        scope: result.scope || 'section',
        priority: result.priority || 'medium'
      }
    } catch {
      return {
        action: 'modify',
        target: null,
        scope: 'section',
        priority: 'medium'
      }
    }
  }

  /**
   * Stage 2: Generate multiple edit proposals
   */
  async generateEditProposals(
    currentHtml: string,
    instruction: string,
    intent: EditIntent
  ): Promise<EditProposal[]> {
    const proposals: EditProposal[] = []

    // Generate 3 different approaches
    const approaches = [
      { name: 'Conservative', temperature: 0.2, focus: 'minimal changes' },
      { name: 'Balanced', temperature: 0.5, focus: 'balanced approach' },
      { name: 'Creative', temperature: 0.8, focus: 'creative solution' }
    ]

    for (const approach of approaches) {
      try {
        const proposal = await this.generateSingleProposal(
          currentHtml,
          instruction,
          intent,
          approach
        )
        if (proposal) {
          proposals.push(proposal)
        }
      } catch (error) {
        console.warn(`Failed to generate ${approach.name} proposal:`, error)
      }
    }

    return proposals
  }

  /**
   * Generate a single edit proposal
   */
  private async generateSingleProposal(
    currentHtml: string,
    instruction: string,
    intent: EditIntent,
    approach: { name: string; temperature: number; focus: string }
  ): Promise<EditProposal | null> {
    const systemPrompt = `You are a professional web editor creating a ${approach.focus} edit proposal.

CRITICAL RULES:
1. Preserve all existing content unless specifically requested to remove
2. Make only the changes requested in the instruction
3. Maintain website structure and functionality
4. Ensure mobile responsiveness
5. Keep Kenyan business context appropriate

APPROACH: ${approach.name} - ${approach.focus}

Return JSON with:
{
  "description": "Clear description of what will be changed",
  "changes": [{"type": "add|modify|remove", "element": "description", "before": "old content", "after": "new content", "location": "where"}],
  "riskLevel": "low|medium|high",
  "confidence": 0.0-1.0,
  "estimatedImpact": "description of impact",
  "previewHtml": "complete updated HTML"
}`

    const userPrompt = `
CURRENT HTML:
${currentHtml}

EDIT INSTRUCTION: ${instruction}

EDIT INTENT:
- Action: ${intent.action}
- Target: ${intent.target || 'general'}
- Scope: ${intent.scope}
- Priority: ${intent.priority}

Generate a ${approach.name.toLowerCase()} edit proposal that applies the requested changes safely.`

    const response = await this.openai.chat.completions.create({
      model: 'deepseek/deepseek-r1-0528:free',
      messages: [
        { role: 'system', content: systemPrompt },
        { role: 'user', content: userPrompt }
      ],
      temperature: approach.temperature,
      max_tokens: 8000
    })

    try {
      const result = JSON.parse(response.choices[0]?.message?.content || '{}')
      
      return {
        id: `${approach.name.toLowerCase()}-${Date.now()}`,
        description: result.description || `${approach.name} approach to: ${instruction}`,
        changes: result.changes || [],
        riskLevel: result.riskLevel || 'medium',
        confidence: result.confidence || 0.5,
        estimatedImpact: result.estimatedImpact || 'Moderate changes to website',
        previewHtml: result.previewHtml || currentHtml
      }
    } catch (error) {
      console.error(`Failed to parse ${approach.name} proposal:`, error)
      return null
    }
  }

  /**
   * Stage 3: Validate edit proposals
   */
  async validateProposal(
    originalHtml: string,
    proposal: EditProposal
  ): Promise<ValidationResult> {
    const issues: string[] = []
    const warnings: string[] = []
    const recommendations: string[] = []

    // Size validation
    if (proposal.previewHtml.length < originalHtml.length * 0.3) {
      issues.push('Proposal results in significant content loss (>70%)')
    } else if (proposal.previewHtml.length < originalHtml.length * 0.7) {
      warnings.push('Proposal reduces content by more than 30%')
    }

    // Structure validation
    const originalStructure = this.extractStructure(originalHtml)
    const proposalStructure = this.extractStructure(proposal.previewHtml)
    
    if (proposalStructure.sections < originalStructure.sections * 0.8) {
      issues.push('Proposal removes essential website sections')
    }

    // HTML validity
    if (!this.isValidHtml(proposal.previewHtml)) {
      issues.push('Proposal generates invalid HTML structure')
    }

    // Risk assessment
    if (proposal.riskLevel === 'high' && proposal.confidence < 0.7) {
      warnings.push('High-risk proposal with low confidence')
    }

    // Content preservation check
    const contentSimilarity = this.calculateContentSimilarity(originalHtml, proposal.previewHtml)
    if (contentSimilarity < 0.5 && proposal.changes.length < 3) {
      warnings.push('Significant content changes for a simple edit request')
    }

    // Generate recommendations
    if (proposal.confidence < 0.8) {
      recommendations.push('Consider reviewing the edit instruction for clarity')
    }
    if (proposal.riskLevel === 'high') {
      recommendations.push('Create a backup before applying this proposal')
    }

    const score = this.calculateValidationScore(issues, warnings, proposal)
    const isValid = issues.length === 0 && score >= 60

    return {
      isValid,
      issues,
      warnings,
      score,
      recommendations
    }
  }

  /**
   * Extract basic structure information from HTML
   */
  private extractStructure(html: string): { sections: number; elements: number } {
    const sectionTags = ['header', 'main', 'section', 'article', 'aside', 'footer', 'nav']
    const sections = sectionTags.reduce((count, tag) => {
      return count + (html.match(new RegExp(`<${tag}`, 'gi')) || []).length
    }, 0)

    const elements = (html.match(/<[^\/][^>]*>/g) || []).length

    return { sections, elements }
  }

  /**
   * Basic HTML validity check
   */
  private isValidHtml(html: string): boolean {
    return html.includes('<!DOCTYPE') && 
           html.includes('<html') && 
           html.includes('</html>') &&
           html.includes('<body') &&
           html.includes('</body>')
  }

  /**
   * Calculate content similarity between two HTML strings
   */
  private calculateContentSimilarity(html1: string, html2: string): number {
    const text1 = html1.replace(/<[^>]*>/g, '').replace(/\s+/g, ' ').trim()
    const text2 = html2.replace(/<[^>]*>/g, '').replace(/\s+/g, ' ').trim()
    
    const words1 = text1.split(' ').filter(w => w.length > 2)
    const words2 = text2.split(' ').filter(w => w.length > 2)
    
    const commonWords = words1.filter(word => words2.includes(word))
    return commonWords.length / Math.max(words1.length, 1)
  }

  /**
   * Calculate overall validation score
   */
  private calculateValidationScore(
    issues: string[],
    warnings: string[],
    proposal: EditProposal
  ): number {
    let score = 100
    
    // Deduct for issues and warnings
    score -= issues.length * 30
    score -= warnings.length * 10
    
    // Adjust for confidence and risk
    score *= proposal.confidence
    
    if (proposal.riskLevel === 'high') score *= 0.8
    else if (proposal.riskLevel === 'low') score *= 1.1
    
    return Math.max(0, Math.min(100, score))
  }

  /**
   * Stage 4: Create diff preview for user
   */
  generateDiffPreview(originalHtml: string, proposalHtml: string): {
    summary: string
    changes: Array<{ type: string; description: string; impact: string }>
  } {
    const changes: Array<{ type: string; description: string; impact: string }> = []
    
    // Simple diff analysis
    const sizeDiff = proposalHtml.length - originalHtml.length
    const sizeChange = Math.abs(sizeDiff) / originalHtml.length * 100
    
    if (sizeDiff > 0) {
      changes.push({
        type: 'addition',
        description: `Added approximately ${Math.round(sizeChange)}% more content`,
        impact: sizeChange > 20 ? 'significant' : 'moderate'
      })
    } else if (sizeDiff < 0) {
      changes.push({
        type: 'removal',
        description: `Removed approximately ${Math.round(sizeChange)}% of content`,
        impact: sizeChange > 20 ? 'significant' : 'moderate'
      })
    }

    const summary = changes.length > 0 
      ? `${changes.length} changes detected with ${changes.filter(c => c.impact === 'significant').length} significant impacts`
      : 'Minor changes detected'

    return { summary, changes }
  }
}
