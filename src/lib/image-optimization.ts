/**
 * Image Optimization Service for PagesLab
 * Handles image compression, format conversion, and optimization for web delivery
 */

export interface ImageOptimizationOptions {
  maxWidth?: number
  maxHeight?: number
  quality?: number
  format?: 'webp' | 'jpeg' | 'png' | 'auto'
  progressive?: boolean
  removeMetadata?: boolean
}

export interface OptimizedImage {
  blob: Blob
  dataUrl: string
  size: number
  originalSize: number
  compressionRatio: number
  format: string
  dimensions: {
    width: number
    height: number
  }
}

export class ImageOptimizer {
  private canvas: HTMLCanvasElement | null = null
  private ctx: CanvasRenderingContext2D | null = null

  constructor() {
    // Only initialize on client side
    if (typeof window !== 'undefined') {
      this.canvas = document.createElement('canvas')
      this.ctx = this.canvas.getContext('2d')!
    }
  }

  private ensureCanvas() {
    if (!this.canvas || !this.ctx) {
      if (typeof window === 'undefined') {
        throw new Error('ImageOptimizer can only be used on the client side')
      }
      this.canvas = document.createElement('canvas')
      this.ctx = this.canvas.getContext('2d')!
    }
  }

  /**
   * Optimize an image file for web delivery
   */
  async optimizeImage(
    file: File, 
    options: ImageOptimizationOptions = {}
  ): Promise<OptimizedImage> {
    const {
      maxWidth = 1920,
      maxHeight = 1080,
      quality = 0.85,
      format = 'auto',
      progressive = true,
      removeMetadata = true
    } = options

    return new Promise((resolve, reject) => {
      const img = new Image()
      
      img.onload = () => {
        try {
          this.ensureCanvas()

          // Calculate optimal dimensions
          const { width, height } = this.calculateOptimalDimensions(
            img.width,
            img.height,
            maxWidth,
            maxHeight
          )

          // Set canvas dimensions
          this.canvas!.width = width
          this.canvas!.height = height

          // Clear canvas and draw image
          this.ctx!.clearRect(0, 0, width, height)
          this.ctx!.drawImage(img, 0, 0, width, height)

          // Determine output format
          const outputFormat = this.determineOutputFormat(file.type, format)
          
          // Convert to blob
          this.canvas!.toBlob(
            (blob) => {
              if (!blob) {
                reject(new Error('Failed to create optimized image'))
                return
              }

              // Create data URL
              const reader = new FileReader()
              reader.onload = () => {
                const dataUrl = reader.result as string
                
                resolve({
                  blob,
                  dataUrl,
                  size: blob.size,
                  originalSize: file.size,
                  compressionRatio: Math.round((1 - blob.size / file.size) * 100),
                  format: outputFormat,
                  dimensions: { width, height }
                })
              }
              reader.readAsDataURL(blob)
            },
            outputFormat,
            quality
          )
        } catch (error) {
          reject(error)
        }
      }

      img.onerror = () => reject(new Error('Failed to load image'))
      img.src = URL.createObjectURL(file)
    })
  }

  /**
   * Create multiple sizes for responsive images
   */
  async createResponsiveImages(
    file: File,
    sizes: number[] = [320, 640, 1024, 1920]
  ): Promise<{ [key: string]: OptimizedImage }> {
    const results: { [key: string]: OptimizedImage } = {}

    for (const size of sizes) {
      try {
        const optimized = await this.optimizeImage(file, {
          maxWidth: size,
          maxHeight: size,
          quality: size <= 640 ? 0.8 : 0.85,
          format: 'webp'
        })
        results[`${size}w`] = optimized
      } catch (error) {
        console.warn(`Failed to create ${size}w version:`, error)
      }
    }

    return results
  }

  /**
   * Calculate optimal dimensions while maintaining aspect ratio
   */
  private calculateOptimalDimensions(
    originalWidth: number,
    originalHeight: number,
    maxWidth: number,
    maxHeight: number
  ): { width: number; height: number } {
    let { width, height } = { width: originalWidth, height: originalHeight }

    // Calculate scaling factor
    const widthRatio = maxWidth / width
    const heightRatio = maxHeight / height
    const ratio = Math.min(widthRatio, heightRatio, 1) // Don't upscale

    width = Math.round(width * ratio)
    height = Math.round(height * ratio)

    return { width, height }
  }

  /**
   * Determine the best output format
   */
  private determineOutputFormat(
    originalFormat: string, 
    preferredFormat: string
  ): string {
    if (preferredFormat !== 'auto') {
      return `image/${preferredFormat}`
    }

    // Check WebP support
    if (this.supportsWebP()) {
      return 'image/webp'
    }

    // Fallback to JPEG for photos, PNG for graphics
    if (originalFormat.includes('png') || originalFormat.includes('gif')) {
      return 'image/png'
    }

    return 'image/jpeg'
  }

  /**
   * Check if browser supports WebP
   */
  private supportsWebP(): boolean {
    const canvas = document.createElement('canvas')
    canvas.width = 1
    canvas.height = 1
    return canvas.toDataURL('image/webp').indexOf('data:image/webp') === 0
  }

  /**
   * Generate a placeholder image (blur/low quality)
   */
  async generatePlaceholder(file: File): Promise<string> {
    const placeholder = await this.optimizeImage(file, {
      maxWidth: 20,
      maxHeight: 20,
      quality: 0.1,
      format: 'jpeg'
    })

    return placeholder.dataUrl
  }

  /**
   * Analyze image and provide optimization recommendations
   */
  async analyzeImage(file: File): Promise<{
    recommendations: string[]
    potentialSavings: number
    currentSize: string
    optimalSize: string
  }> {
    const recommendations: string[] = []
    let potentialSavings = 0

    // Test optimization
    const optimized = await this.optimizeImage(file, {
      maxWidth: 1920,
      maxHeight: 1080,
      quality: 0.85,
      format: 'webp'
    })

    potentialSavings = optimized.compressionRatio

    // Generate recommendations
    if (file.size > 1024 * 1024) { // > 1MB
      recommendations.push('Image is quite large. Consider reducing dimensions or quality.')
    }

    if (file.type === 'image/png' && !this.hasTransparency(file)) {
      recommendations.push('PNG without transparency could be converted to JPEG for better compression.')
    }

    if (!file.type.includes('webp')) {
      recommendations.push('Converting to WebP format could reduce file size significantly.')
    }

    if (potentialSavings > 50) {
      recommendations.push(`Optimization could reduce file size by ${potentialSavings}%.`)
    }

    return {
      recommendations,
      potentialSavings,
      currentSize: this.formatFileSize(file.size),
      optimalSize: this.formatFileSize(optimized.size)
    }
  }

  /**
   * Check if PNG has transparency (simplified check)
   */
  private hasTransparency(file: File): boolean {
    // This is a simplified check - in a real implementation,
    // you'd need to analyze the actual PNG data
    return file.type === 'image/png'
  }

  /**
   * Format file size for display
   */
  private formatFileSize(bytes: number): string {
    if (bytes === 0) return '0 Bytes'
    
    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }
}

// Singleton instance
export const imageOptimizer = new ImageOptimizer()

// Utility functions
export const optimizeImageFile = async (
  file: File, 
  options?: ImageOptimizationOptions
): Promise<OptimizedImage> => {
  return imageOptimizer.optimizeImage(file, options)
}

export const createResponsiveImageSet = async (
  file: File,
  sizes?: number[]
): Promise<{ [key: string]: OptimizedImage }> => {
  return imageOptimizer.createResponsiveImages(file, sizes)
}

export const generateImagePlaceholder = async (file: File): Promise<string> => {
  return imageOptimizer.generatePlaceholder(file)
}

export const analyzeImageOptimization = async (file: File) => {
  return imageOptimizer.analyzeImage(file)
}

// Image lazy loading utilities
export class LazyImageLoader {
  private observer: IntersectionObserver | null = null
  private images: Set<HTMLImageElement> = new Set()

  constructor() {
    if (typeof window !== 'undefined' && 'IntersectionObserver' in window) {
      this.observer = new IntersectionObserver(
        this.handleIntersection.bind(this),
        {
          rootMargin: '50px 0px',
          threshold: 0.01
        }
      )
    }
  }

  /**
   * Add an image to lazy loading
   */
  observe(img: HTMLImageElement): void {
    if (this.observer) {
      this.images.add(img)
      this.observer.observe(img)
    } else {
      // Fallback for browsers without IntersectionObserver
      this.loadImage(img)
    }
  }

  /**
   * Remove an image from lazy loading
   */
  unobserve(img: HTMLImageElement): void {
    if (this.observer) {
      this.observer.unobserve(img)
      this.images.delete(img)
    }
  }

  /**
   * Handle intersection changes
   */
  private handleIntersection(entries: IntersectionObserverEntry[]): void {
    entries.forEach(entry => {
      if (entry.isIntersecting) {
        const img = entry.target as HTMLImageElement
        this.loadImage(img)
        this.unobserve(img)
      }
    })
  }

  /**
   * Load an image
   */
  private loadImage(img: HTMLImageElement): void {
    const src = img.dataset.src
    const srcset = img.dataset.srcset

    if (src) {
      img.src = src
    }

    if (srcset) {
      img.srcset = srcset
    }

    img.classList.remove('lazy')
    img.classList.add('loaded')
  }

  /**
   * Disconnect the observer
   */
  disconnect(): void {
    if (this.observer) {
      this.observer.disconnect()
      this.images.clear()
    }
  }
}

// Singleton instance
export const lazyImageLoader = new LazyImageLoader()
