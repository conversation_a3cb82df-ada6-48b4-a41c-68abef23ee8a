/**
 * Content Quality Analysis System for PagesLab
 * Implements content quality scoring and cultural sensitivity checks
 */

export interface ContentAnalysis {
  score: number // 0-100
  issues: ContentIssue[]
  suggestions: ContentSuggestion[]
  culturalSensitivity: CulturalSensitivityCheck
  readability: ReadabilityScore
  seoScore: SEOAnalysis
}

export interface ContentIssue {
  type: 'cultural' | 'seo' | 'readability'
  severity: 'low' | 'medium' | 'high'
  message: string
  suggestion?: string
  position?: { start: number; end: number }
}

export interface ContentSuggestion {
  type: 'improvement' | 'cultural' | 'seo' | 'engagement'
  message: string
  example?: string
  priority: 'low' | 'medium' | 'high'
}

export interface CulturalSensitivityCheck {
  score: number
  issues: string[]
  recommendations: string[]
  kenyanContext: {
    appropriate: boolean
    culturalReferences: string[]
    languageUsage: 'appropriate' | 'needs_review' | 'inappropriate'
  }
}



export interface ReadabilityScore {
  score: number
  level: 'elementary' | 'middle' | 'high' | 'college' | 'graduate'
  averageSentenceLength: number
  averageWordsPerSentence: number
  complexWords: number
  recommendations: string[]
}

export interface SEOAnalysis {
  score: number
  keywordDensity: { [keyword: string]: number }
  metaDescription: {
    present: boolean
    length: number
    optimal: boolean
  }
  headingStructure: {
    h1Count: number
    h2Count: number
    h3Count: number
    proper: boolean
  }
  recommendations: string[]
}

export class ContentModerator {
  private culturallyInsensitiveTerms!: Set<string>
  private kenyanBusinessTerms!: Set<string>
  private swahiliDictionary!: Set<string>
  private englishDictionary!: Set<string>

  constructor() {
    this.initializeDictionaries()
  }

  /**
   * Analyze content for quality, appropriateness, and cultural sensitivity
   */
  async analyzeContent(
    content: string,
    language: 'en' | 'sw' | 'auto' = 'auto',
    context: 'business' | 'general' = 'business'
  ): Promise<ContentAnalysis> {
    const detectedLanguage = language === 'auto' ? this.detectLanguage(content) : language

    // Run all analysis in parallel
    const [
      culturalSensitivity,
      readability,
      seoAnalysis
    ] = await Promise.all([
      this.checkCulturalSensitivity(content, context),
      this.analyzeReadability(content, detectedLanguage),
      this.analyzeSEO(content)
    ])

    const issues: ContentIssue[] = [
      ...culturalSensitivity.issues.map(issue => ({
        type: 'cultural' as const,
        severity: 'medium' as const,
        message: issue
      }))
    ]

    const suggestions = this.generateSuggestions(
      content,
      issues,
      culturalSensitivity,
      readability,
      seoAnalysis,
      detectedLanguage
    )

    const overallScore = this.calculateOverallScore(
      issues,
      culturalSensitivity.score,
      readability.score,
      seoAnalysis.score
    )

    return {
      score: overallScore,
      issues,
      suggestions,
      culturalSensitivity,
      readability,
      seoScore: seoAnalysis
    }
  }



  /**
   * Check cultural sensitivity for Kenyan context
   */
  private async checkCulturalSensitivity(
    content: string,
    context: string
  ): Promise<CulturalSensitivityCheck> {
    const issues: string[] = []
    const recommendations: string[] = []
    const culturalReferences: string[] = []

    // Check for culturally insensitive terms
    const words = content.toLowerCase().split(/\s+/)
    for (const word of words) {
      if (this.culturallyInsensitiveTerms.has(word)) {
        issues.push(`Potentially insensitive term: "${word}"`)
      }
    }

    // Check for positive Kenyan cultural references
    for (const term of this.kenyanBusinessTerms) {
      if (content.toLowerCase().includes(term)) {
        culturalReferences.push(term)
      }
    }

    // Analyze language usage
    const hasSwahiliElements = this.hasSwahiliElements(content)
    const hasKenyanReferences = culturalReferences.length > 0

    let languageUsage: 'appropriate' | 'needs_review' | 'inappropriate' = 'appropriate'

    if (context === 'business' && !hasKenyanReferences && content.length > 100) {
      recommendations.push('Consider adding Kenyan cultural context or local references')
      languageUsage = 'needs_review'
    }

    if (hasSwahiliElements && !this.isSwahiliAppropriate(content)) {
      issues.push('Swahili usage may not be contextually appropriate')
      languageUsage = 'needs_review'
    }

    // Generate cultural recommendations
    if (context === 'business') {
      recommendations.push('Consider mentioning local landmarks or areas in Kenya')
      recommendations.push('Use inclusive language that respects all Kenyan communities')
      recommendations.push('Consider adding Swahili greetings or phrases where appropriate')
    }

    const score = Math.max(0, 100 - (issues.length * 20) - (recommendations.length * 5))

    return {
      score,
      issues,
      recommendations,
      kenyanContext: {
        appropriate: issues.length === 0,
        culturalReferences,
        languageUsage
      }
    }
  }



  /**
   * Analyze readability of content
   */
  private analyzeReadability(content: string, language: 'en' | 'sw' | 'mixed'): ReadabilityScore {
    const sentences = content.split(/[.!?]+/).filter(s => s.trim().length > 0)
    const words = content.match(/\b\w+\b/g) || []
    const syllables = this.countSyllables(content)

    const averageSentenceLength = sentences.length > 0 ? words.length / sentences.length : 0
    const averageWordsPerSentence = averageSentenceLength
    const averageSyllablesPerWord = words.length > 0 ? syllables / words.length : 0

    // Simplified Flesch Reading Ease calculation
    const fleschScore = 206.835 - (1.015 * averageSentenceLength) - (84.6 * averageSyllablesPerWord)

    let level: ReadabilityScore['level'] = 'middle'
    if (fleschScore >= 90) level = 'elementary'
    else if (fleschScore >= 80) level = 'middle'
    else if (fleschScore >= 70) level = 'high'
    else if (fleschScore >= 60) level = 'college'
    else level = 'graduate'

    const complexWords = words.filter(word => this.countWordSyllables(word) >= 3).length
    const recommendations: string[] = []

    if (averageSentenceLength > 20) {
      recommendations.push('Consider using shorter sentences for better readability')
    }
    if (complexWords / words.length > 0.15) {
      recommendations.push('Consider using simpler words where possible')
    }
    if (fleschScore < 60) {
      recommendations.push('Content may be too complex for general audience')
    }

    return {
      score: Math.max(0, Math.min(100, fleschScore)),
      level,
      averageSentenceLength,
      averageWordsPerSentence,
      complexWords,
      recommendations
    }
  }

  /**
   * Analyze SEO aspects of content
   */
  private analyzeSEO(content: string): SEOAnalysis {
    const words = content.toLowerCase().match(/\b\w+\b/g) || []
    const wordCount = words.length

    // Calculate keyword density
    const wordFreq: { [word: string]: number } = {}
    words.forEach(word => {
      if (word.length > 3) { // Ignore short words
        wordFreq[word] = (wordFreq[word] || 0) + 1
      }
    })

    const keywordDensity: { [keyword: string]: number } = {}
    Object.entries(wordFreq).forEach(([word, count]) => {
      const density = (count / wordCount) * 100
      if (density > 1) { // Only include words with >1% density
        keywordDensity[word] = density
      }
    })

    // Check heading structure (simplified)
    const h1Count = (content.match(/<h1/gi) || []).length
    const h2Count = (content.match(/<h2/gi) || []).length
    const h3Count = (content.match(/<h3/gi) || []).length

    const recommendations: string[] = []

    if (h1Count === 0) {
      recommendations.push('Add an H1 heading for better SEO')
    }
    if (h1Count > 1) {
      recommendations.push('Use only one H1 heading per page')
    }
    if (h2Count === 0 && content.length > 300) {
      recommendations.push('Add H2 headings to structure your content')
    }
    if (wordCount < 150) {
      recommendations.push('Consider adding more content for better SEO')
    }

    const score = Math.min(100,
      (h1Count === 1 ? 25 : 0) +
      (h2Count > 0 ? 25 : 0) +
      (wordCount >= 150 ? 25 : wordCount / 150 * 25) +
      (Object.keys(keywordDensity).length > 0 ? 25 : 0)
    )

    return {
      score,
      keywordDensity,
      metaDescription: {
        present: content.includes('meta name="description"'),
        length: 0, // Would extract from actual meta tag
        optimal: false
      },
      headingStructure: {
        h1Count,
        h2Count,
        h3Count,
        proper: h1Count === 1 && h2Count > 0
      },
      recommendations
    }
  }

  // Helper methods
  private initializeDictionaries(): void {
    this.culturallyInsensitiveTerms = new Set([
      'primitive', 'backward', 'uncivilized', 'tribal' // Add more as needed
    ])

    this.kenyanBusinessTerms = new Set([
      'nairobi', 'mombasa', 'kisumu', 'nakuru', 'eldoret', 'thika', 'machakos',
      'kenya', 'kenyan', 'east africa', 'safari', 'maasai', 'kikuyu', 'luo',
      'mpesa', 'safaricom', 'equity', 'kcb', 'cooperative', 'sacco',
      'matatu', 'boda', 'nyama choma', 'ugali', 'sukuma wiki'
    ])

    this.swahiliDictionary = new Set([
      'habari', 'jambo', 'karibu', 'asante', 'pole', 'harambee', 'uhuru',
      'biashara', 'duka', 'sokoni', 'chakula', 'maji', 'nyumba', 'gari',
      'pesa', 'kazi', 'shule', 'hospitali', 'polisi', 'serikali'
    ])

    this.englishDictionary = new Set([
      'business', 'service', 'quality', 'professional', 'customer', 'client',
      'company', 'organization', 'team', 'experience', 'solution', 'product'
      // Add more common English words
    ])
  }



  private detectLanguage(content: string): 'en' | 'sw' | 'mixed' {
    const words = content.toLowerCase().match(/\b\w+\b/g) || []
    let englishCount = 0
    let swahiliCount = 0

    words.forEach(word => {
      if (this.englishDictionary.has(word)) englishCount++
      if (this.swahiliDictionary.has(word)) swahiliCount++
    })

    if (englishCount > swahiliCount * 2) return 'en'
    if (swahiliCount > englishCount * 2) return 'sw'
    return 'mixed'
  }

  private hasSwahiliElements(content: string): boolean {
    const words = content.toLowerCase().match(/\b\w+\b/g) || []
    return words.some(word => this.swahiliDictionary.has(word))
  }

  private isSwahiliAppropriate(content: string): boolean {
    // Simple check - in production, implement more sophisticated analysis
    return !content.toLowerCase().includes('inappropriate swahili pattern')
  }



  private countSyllables(text: string): number {
    const words = text.match(/\b\w+\b/g) || []
    return words.reduce((total, word) => total + this.countWordSyllables(word), 0)
  }

  private countWordSyllables(word: string): number {
    word = word.toLowerCase()
    if (word.length <= 3) return 1

    const vowels = 'aeiouy'
    let syllableCount = 0
    let previousWasVowel = false

    for (let i = 0; i < word.length; i++) {
      const isVowel = vowels.includes(word[i])
      if (isVowel && !previousWasVowel) {
        syllableCount++
      }
      previousWasVowel = isVowel
    }

    // Handle silent e
    if (word.endsWith('e')) syllableCount--

    return Math.max(1, syllableCount)
  }

  private generateSuggestions(
    content: string,
    issues: ContentIssue[],
    culturalSensitivity: CulturalSensitivityCheck,
    readability: ReadabilityScore,
    seoAnalysis: SEOAnalysis,
    language: 'en' | 'sw' | 'mixed'
  ): ContentSuggestion[] {
    const suggestions: ContentSuggestion[] = []

    // Cultural suggestions
    if (culturalSensitivity.kenyanContext.culturalReferences.length === 0) {
      suggestions.push({
        type: 'cultural',
        message: 'Consider adding Kenyan cultural context to make your content more relatable',
        example: 'Mention local areas like "serving customers in Westlands" or "located near Sarit Centre"',
        priority: 'medium'
      })
    }

    // Readability suggestions
    if (readability.score < 60) {
      suggestions.push({
        type: 'improvement',
        message: 'Simplify your language for better readability',
        example: 'Use shorter sentences and common words',
        priority: 'high'
      })
    }

    // SEO suggestions
    if (seoAnalysis.score < 70) {
      suggestions.push({
        type: 'seo',
        message: 'Improve SEO by adding proper headings and keywords',
        example: 'Add H1 and H2 headings, include relevant keywords naturally',
        priority: 'medium'
      })
    }

    // Language-specific suggestions
    if (language === 'mixed') {
      suggestions.push({
        type: 'improvement',
        message: 'Consider using consistent language throughout your content',
        priority: 'low'
      })
    }

    return suggestions
  }

  private calculateOverallScore(
    issues: ContentIssue[],
    culturalScore: number,
    readabilityScore: number,
    seoScore: number
  ): number {
    // Weight different aspects (redistributed without spelling)
    const weights = {
      issues: 0.4,
      cultural: 0.3,
      readability: 0.2,
      seo: 0.1
    }

    // Calculate issue penalty
    const issuePenalty = issues.reduce((penalty, issue) => {
      switch (issue.severity) {
        case 'high': return penalty + 20
        case 'medium': return penalty + 10
        case 'low': return penalty + 5
        default: return penalty
      }
    }, 0)

    const issueScore = Math.max(0, 100 - issuePenalty)

    // Calculate weighted average
    const weightedScore =
      (issueScore * weights.issues) +
      (culturalScore * weights.cultural) +
      (readabilityScore * weights.readability) +
      (seoScore * weights.seo)

    return Math.round(Math.max(0, Math.min(100, weightedScore)))
  }
}

// Singleton instance
export const contentModerator = new ContentModerator()

// Utility functions
export const analyzeContent = async (
  content: string,
  language?: 'en' | 'sw' | 'auto',
  context?: 'business' | 'general'
): Promise<ContentAnalysis> => {
  return contentModerator.analyzeContent(content, language, context)
}

export const isContentAppropriate = async (content: string): Promise<boolean> => {
  const analysis = await contentModerator.analyzeContent(content)

  // Content is appropriate if it has a reasonable quality score
  // Since we removed strict content moderation, we're more lenient
  return analysis.score >= 30
}
