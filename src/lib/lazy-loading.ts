/**
 * Lazy Loading System for Generated Websites
 * Improves performance by loading images and content only when needed
 */

export interface LazyLoadOptions {
  rootMargin?: string
  threshold?: number
  enablePlaceholder?: boolean
  fadeInDuration?: number
}

export class LazyLoader {
  private observer: IntersectionObserver | null = null
  private options: LazyLoadOptions

  constructor(options: LazyLoadOptions = {}) {
    this.options = {
      rootMargin: '50px 0px',
      threshold: 0.1,
      enablePlaceholder: true,
      fadeInDuration: 300,
      ...options
    }

    this.initializeObserver()
  }

  private initializeObserver(): void {
    if (typeof window === 'undefined' || !('IntersectionObserver' in window)) {
      return
    }

    this.observer = new IntersectionObserver(
      this.handleIntersection.bind(this),
      {
        rootMargin: this.options.rootMargin,
        threshold: this.options.threshold
      }
    )
  }

  private handleIntersection(entries: IntersectionObserverEntry[]): void {
    entries.forEach(entry => {
      if (entry.isIntersecting) {
        const element = entry.target as HTMLElement
        
        if (element.tagName === 'IMG') {
          this.loadImage(element as HTMLImageElement)
        } else {
          this.loadContent(element)
        }
        
        this.observer?.unobserve(element)
      }
    })
  }

  private loadImage(img: HTMLImageElement): void {
    const src = img.dataset.src
    const srcset = img.dataset.srcset
    
    if (!src) return

    // Create a new image to preload
    const imageLoader = new Image()
    
    imageLoader.onload = () => {
      // Set the actual source
      if (src) img.src = src
      if (srcset) img.srcset = srcset
      
      // Add loaded class for CSS transitions
      img.classList.add('lazy-loaded')
      img.classList.remove('lazy-loading')
      
      // Fade in effect
      if (this.options.fadeInDuration) {
        img.style.transition = `opacity ${this.options.fadeInDuration}ms ease-in-out`
        img.style.opacity = '1'
      }
    }
    
    imageLoader.onerror = () => {
      // Handle error - show fallback or placeholder
      img.classList.add('lazy-error')
      img.classList.remove('lazy-loading')
    }
    
    // Start loading
    img.classList.add('lazy-loading')
    imageLoader.src = src
    if (srcset) imageLoader.srcset = srcset
  }

  private loadContent(element: HTMLElement): void {
    // For content sections, trigger any animations or load dynamic content
    element.classList.add('lazy-loaded')
    element.classList.remove('lazy-loading')
    
    // Trigger any custom load events
    const event = new CustomEvent('lazyLoaded', { detail: { element } })
    element.dispatchEvent(event)
  }

  /**
   * Observe an element for lazy loading
   */
  observe(element: HTMLElement): void {
    if (!this.observer) {
      // Fallback for browsers without IntersectionObserver
      if (element.tagName === 'IMG') {
        this.loadImage(element as HTMLImageElement)
      } else {
        this.loadContent(element)
      }
      return
    }

    // Add initial classes
    element.classList.add('lazy-loading')
    
    // Set up placeholder for images
    if (element.tagName === 'IMG' && this.options.enablePlaceholder) {
      this.setupImagePlaceholder(element as HTMLImageElement)
    }
    
    this.observer.observe(element)
  }

  private setupImagePlaceholder(img: HTMLImageElement): void {
    // Create a low-quality placeholder
    const placeholder = img.dataset.placeholder
    if (placeholder) {
      img.src = placeholder
      img.style.filter = 'blur(5px)'
      img.style.transition = `filter ${this.options.fadeInDuration}ms ease-in-out`
    } else {
      // Use a simple gradient placeholder
      img.style.background = 'linear-gradient(45deg, #f0f0f0 25%, transparent 25%), linear-gradient(-45deg, #f0f0f0 25%, transparent 25%), linear-gradient(45deg, transparent 75%, #f0f0f0 75%), linear-gradient(-45deg, transparent 75%, #f0f0f0 75%)'
      img.style.backgroundSize = '20px 20px'
      img.style.backgroundPosition = '0 0, 0 10px, 10px -10px, -10px 0px'
    }
  }

  /**
   * Unobserve an element
   */
  unobserve(element: HTMLElement): void {
    this.observer?.unobserve(element)
  }

  /**
   * Disconnect the observer
   */
  disconnect(): void {
    this.observer?.disconnect()
  }
}

/**
 * Generate lazy loading HTML for images
 */
export function generateLazyImageHTML(
  src: string,
  alt: string,
  options: {
    srcSet?: string
    placeholder?: string
    className?: string
    width?: number
    height?: number
  } = {}
): string {
  const {
    srcSet,
    placeholder,
    className = '',
    width,
    height
  } = options

  const attributes = [
    `data-src="${src}"`,
    `alt="${alt}"`,
    srcSet ? `data-srcset="${srcSet}"` : '',
    placeholder ? `data-placeholder="${placeholder}"` : '',
    width ? `width="${width}"` : '',
    height ? `height="${height}"` : '',
    `class="lazy-image ${className}"`
  ].filter(Boolean).join(' ')

  return `<img ${attributes} />`
}

/**
 * Generate CSS for lazy loading effects
 */
export function generateLazyLoadingCSS(): string {
  return `
    /* Lazy Loading Styles */
    .lazy-image {
      opacity: 0;
      transition: opacity 300ms ease-in-out;
    }
    
    .lazy-image.lazy-loading {
      opacity: 0.3;
    }
    
    .lazy-image.lazy-loaded {
      opacity: 1;
      filter: none !important;
    }
    
    .lazy-image.lazy-error {
      opacity: 0.5;
      background: #f5f5f5;
      display: flex;
      align-items: center;
      justify-content: center;
    }
    
    .lazy-image.lazy-error::after {
      content: "⚠️ Image failed to load";
      color: #666;
      font-size: 14px;
    }
    
    /* Content lazy loading */
    .lazy-content {
      opacity: 0;
      transform: translateY(20px);
      transition: opacity 300ms ease-in-out, transform 300ms ease-in-out;
    }
    
    .lazy-content.lazy-loaded {
      opacity: 1;
      transform: translateY(0);
    }
    
    /* Skeleton loading animation */
    .lazy-skeleton {
      background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
      background-size: 200% 100%;
      animation: skeleton-loading 1.5s infinite;
    }
    
    @keyframes skeleton-loading {
      0% { background-position: 200% 0; }
      100% { background-position: -200% 0; }
    }
    
    /* Responsive lazy loading */
    @media (max-width: 768px) {
      .lazy-image {
        transition: opacity 200ms ease-in-out;
      }
    }
    
    /* Reduced motion support */
    @media (prefers-reduced-motion: reduce) {
      .lazy-image,
      .lazy-content {
        transition: none;
      }
      
      .lazy-skeleton {
        animation: none;
        background: #f0f0f0;
      }
    }
  `
}

/**
 * Initialize lazy loading for a generated website
 */
export function initializeLazyLoading(container?: HTMLElement): LazyLoader {
  const loader = new LazyLoader({
    rootMargin: '100px 0px',
    threshold: 0.1,
    enablePlaceholder: true,
    fadeInDuration: 300
  })

  // Find all lazy elements in the container
  const root = container || document
  const lazyImages = root.querySelectorAll('img[data-src]')
  const lazyContent = root.querySelectorAll('.lazy-content')

  // Observe all lazy elements
  lazyImages.forEach(img => loader.observe(img as HTMLImageElement))
  lazyContent.forEach(content => loader.observe(content as HTMLElement))

  return loader
}

// Export singleton instance
export const globalLazyLoader = new LazyLoader()
