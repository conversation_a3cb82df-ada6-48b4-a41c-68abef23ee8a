import OpenAI from 'openai'
import type { 
  BusinessProfile, 
  BusinessType, 
  KenyanLocation, 
  KenyanRegion,
  CulturalContext,
  BusinessPractices,
  ContactInfo
} from '@/types'
import { getRegionFromCounty } from './utils'
import { getCulturalTemplate } from './database'

// OpenAI client will be initialized when needed

// Business type mapping for better recognition (ordered by specificity)
const BUSINESS_TYPE_KEYWORDS: Record<string, BusinessType> = {
  // Tech services (more specific first)
  'computer repair': 'TECH_SERVICES',
  'it support': 'TECH_SERVICES',
  'tech': 'TECH_SERVICES',
  'technology': 'TECH_SERVICES',
  'software': 'TECH_SERVICES',
  'computer': 'TECH_SERVICES',
  'it': 'TECH_SERVICES',
  
  // Salon services
  'salon': 'SALON',
  'beauty': 'SALON',
  'hair': 'SALON',
  'barber': 'SALON',
  'spa': 'SALON',
  'nail': 'SALON',
  
  // Restaurant services
  'restaurant': 'RESTAURANT',
  'hotel': 'HOTEL',
  'food': 'RESTAURANT',
  'cafe': 'RESTAURANT',
  'eatery': 'RESTAURANT',
  
  // Shop services
  'shop': 'SHOP',
  'store': 'SHOP',
  'retail': 'SHOP',
  'boutique': 'SHOP',
  
  // Medical services
  'clinic': 'CLINIC',
  'hospital': 'CLINIC',
  'medical': 'CLINIC',
  'doctor': 'CLINIC',
  'health': 'CLINIC',
  
  // Garage services (less specific)
  'garage': 'GARAGE',
  'mechanic': 'GARAGE',
  'auto': 'GARAGE',
  'car': 'GARAGE',
  'repair': 'GARAGE', // This should come after more specific repair types
  
  // Education services
  'school': 'SCHOOL',
  'education': 'SCHOOL',
  'training': 'SCHOOL',
  'academy': 'SCHOOL',
  
  // Religious services
  'church': 'CHURCH',
  'ministry': 'CHURCH',
  'worship': 'CHURCH',
  
  // Consulting services
  'consultancy': 'CONSULTANCY',
  'consulting': 'CONSULTANCY',
  'advisory': 'CONSULTANCY',
  
  // Agriculture
  'agriculture': 'AGRICULTURE',
  'farming': 'AGRICULTURE',
  'crops': 'AGRICULTURE',
  'livestock': 'AGRICULTURE',
  
  // Transport
  'transport': 'TRANSPORT',
  'taxi': 'TRANSPORT',
  'matatu': 'TRANSPORT',
  'logistics': 'TRANSPORT',
  
  // Construction
  'construction': 'CONSTRUCTION',
  'building': 'CONSTRUCTION',
  'contractor': 'CONSTRUCTION',
  
  // Entertainment
  'entertainment': 'ENTERTAINMENT',
  'music': 'ENTERTAINMENT',
  'events': 'ENTERTAINMENT'
}

// Kenyan location keywords
const KENYAN_LOCATIONS: Record<string, { county: string; region: KenyanRegion }> = {
  // Major cities and areas
  'nairobi': { county: 'Nairobi', region: 'HIGHLAND' },
  'westlands': { county: 'Nairobi', region: 'HIGHLAND' },
  'karen': { county: 'Nairobi', region: 'HIGHLAND' },
  'kileleshwa': { county: 'Nairobi', region: 'HIGHLAND' },
  'kilimani': { county: 'Nairobi', region: 'HIGHLAND' },
  'lavington': { county: 'Nairobi', region: 'HIGHLAND' },
  'kibera': { county: 'Nairobi', region: 'HIGHLAND' },
  'kasarani': { county: 'Nairobi', region: 'HIGHLAND' },
  'roysambu': { county: 'Nairobi', region: 'HIGHLAND' },
  
  'mombasa': { county: 'Mombasa', region: 'COASTAL' },
  'diani': { county: 'Kwale', region: 'COASTAL' },
  'malindi': { county: 'Kilifi', region: 'COASTAL' },
  'watamu': { county: 'Kilifi', region: 'COASTAL' },
  'lamu': { county: 'Lamu', region: 'COASTAL' },
  
  'kisumu': { county: 'Kisumu', region: 'WESTERN' },
  'eldoret': { county: 'Uasin Gishu', region: 'NORTHERN' },
  'nakuru': { county: 'Nakuru', region: 'HIGHLAND' },
  'thika': { county: 'Kiambu', region: 'HIGHLAND' },
  'meru': { county: 'Meru', region: 'NORTHERN' },
  'nyeri': { county: 'Nyeri', region: 'HIGHLAND' },
  'machakos': { county: 'Machakos', region: 'EASTERN' },
  'kitui': { county: 'Kitui', region: 'EASTERN' },
  'garissa': { county: 'Garissa', region: 'EASTERN' },
  'turkana': { county: 'Turkana', region: 'NORTHERN' },
  'kakamega': { county: 'Kakamega', region: 'WESTERN' },
  'bungoma': { county: 'Bungoma', region: 'WESTERN' },
  'kericho': { county: 'Kericho', region: 'HIGHLAND' },
  'bomet': { county: 'Bomet', region: 'HIGHLAND' }
}

export interface BusinessEntities {
  businessName?: string
  businessType?: BusinessType
  location?: KenyanLocation
  services: string[]
  description: string
  contactPhone?: string
  contactEmail?: string
}

export interface ValidationResult {
  isComplete: boolean
  missingFields: string[]
  suggestions: string[]
}

export class BusinessAnalysisEngine {
  /**
   * Analyze natural language input and extract business information
   */
  async analyzeInput(input: string): Promise<BusinessProfile> {
    console.log('🔍 BUSINESS ANALYSIS DEBUG - Input:', input)
    console.log('🔍 BUSINESS ANALYSIS DEBUG - Input Length:', input?.length)

    try {
      // First, extract basic entities using keyword matching
      const entities = this.extractEntities(input)
      console.log('🔍 BUSINESS ANALYSIS DEBUG - Extracted entities:', entities)
      
      // Use OpenAI to enhance and fill gaps
      const enhancedEntities = await this.enhanceWithAI(input, entities)
      
      // Build complete business profile
      const businessProfile = await this.buildBusinessProfile(enhancedEntities)
      
      return businessProfile
    } catch (error) {
      console.error('Error analyzing business input:', error)
      throw new Error('Failed to analyze business information')
    }
  }

  /**
   * Extract entities from text using keyword matching and patterns
   */
  extractEntities(text: string): BusinessEntities {
    const lowerText = text.toLowerCase()
    
    // Extract business type (check longer phrases first)
    let businessType: BusinessType | undefined
    const sortedKeywords = Object.entries(BUSINESS_TYPE_KEYWORDS).sort((a, b) => b[0].length - a[0].length)
    for (const [keyword, type] of sortedKeywords) {
      if (lowerText.includes(keyword)) {
        businessType = type
        break
      }
    }
    
    // Extract location
    let location: KenyanLocation | undefined
    for (const [locationName, locationData] of Object.entries(KENYAN_LOCATIONS)) {
      if (lowerText.includes(locationName)) {
        location = {
          county: locationData.county,
          area: locationName.charAt(0).toUpperCase() + locationName.slice(1),
          region: locationData.region
        }
        break
      }
    }
    
    // Extract services (simple keyword extraction)
    const services: string[] = []
    const serviceKeywords = [
      'hair', 'nail', 'nails', 'massage', 'facial', 'makeup',
      'food', 'drinks', 'breakfast', 'lunch', 'dinner',
      'repair', 'service', 'maintenance', 'installation',
      'consultation', 'training', 'education', 'coaching'
    ]
    
    serviceKeywords.forEach(keyword => {
      if (lowerText.includes(keyword)) {
        const serviceName = keyword === 'nail' || keyword === 'nails' ? 'Nails' : 
                           keyword.charAt(0).toUpperCase() + keyword.slice(1)
        if (!services.includes(serviceName)) {
          services.push(serviceName)
        }
      }
    })
    
    // Extract phone number pattern
    const phonePattern = /(\+?254|0)[0-9]{9}/g
    const phoneMatch = text.match(phonePattern)
    const contactPhone = phoneMatch ? phoneMatch[0] : undefined
    
    // Extract email pattern
    const emailPattern = /[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}/g
    const emailMatch = text.match(emailPattern)
    const contactEmail = emailMatch ? emailMatch[0] : undefined
    
    return {
      businessType,
      location,
      services: services.length > 0 ? services : ['General services'],
      description: text,
      contactPhone,
      contactEmail
    }
  }

  /**
   * Use OpenAI to enhance extracted entities and fill gaps
   */
  private async enhanceWithAI(originalInput: string, entities: BusinessEntities): Promise<BusinessEntities> {
    // For development/testing, return enhanced entities without OpenAI if no API key
    if (!process.env.OPENAI_API_KEY) {
      console.log('No OpenAI API key found, using fallback')
      return this.enhanceWithoutAI(originalInput, entities)
    }
    
    console.log('OpenAI API key found, attempting to use OpenAI...', process.env.OPENAI_API_KEY?.substring(0, 20) + '...')
    const prompt = `
You are an AI assistant helping to analyze Kenyan business descriptions. 
Extract and enhance the following information from this business description:

Input: "${originalInput}"

Current extracted data:
- Business Type: ${entities.businessType || 'Unknown'}
- Location: ${entities.location?.area || 'Unknown'}, ${entities.location?.county || 'Unknown'}
- Services: ${entities.services.join(', ')}

Please provide a JSON response with the following structure:
{
  "businessName": "Suggested business name based on description",
  "businessType": "One of: SALON, RESTAURANT, SHOP, CLINIC, GARAGE, HOTEL, SCHOOL, CHURCH, CONSULTANCY, TECH_SERVICES, AGRICULTURE, TRANSPORT, CONSTRUCTION, ENTERTAINMENT, OTHER",
  "location": {
    "county": "Kenyan county name",
    "area": "Specific area/neighborhood",
    "region": "One of: COASTAL, HIGHLAND, WESTERN, NORTHERN, CENTRAL, EASTERN"
  },
  "services": ["List of specific services offered"],
  "targetAudience": "Description of target customers",
  "enhancedDescription": "Professional description of the business"
}

Focus on Kenyan context and local business practices. If location is unclear, suggest common Kenyan locations.
`

    try {
      // Initialize OpenAI client
      const openai = new OpenAI({
        apiKey: process.env.OPENAI_API_KEY
      })
      
      const response = await openai.chat.completions.create({
        model: 'gpt-3.5-turbo',
        messages: [
          {
            role: 'system',
            content: 'You are an expert in Kenyan business analysis. Provide accurate, culturally appropriate business information.'
          },
          {
            role: 'user',
            content: prompt
          }
        ],
        temperature: 0.3,
        max_tokens: 1000
      })

      const aiResponse = response.choices[0]?.message?.content
      if (!aiResponse) {
        throw new Error('No response from AI')
      }

      // Clean the response to extract JSON
      let cleanedResponse = aiResponse.trim()
      
      // Remove markdown code blocks if present
      if (cleanedResponse.startsWith('```json')) {
        cleanedResponse = cleanedResponse.replace(/^```json\s*/, '').replace(/\s*```$/, '')
      } else if (cleanedResponse.startsWith('```')) {
        cleanedResponse = cleanedResponse.replace(/^```\s*/, '').replace(/\s*```$/, '')
      }
      
      const enhancedData = JSON.parse(cleanedResponse)
      
      return {
        businessName: enhancedData.businessName,
        businessType: enhancedData.businessType as BusinessType,
        location: {
          county: enhancedData.location.county,
          area: enhancedData.location.area,
          region: enhancedData.location.region as KenyanRegion
        },
        services: enhancedData.services,
        description: enhancedData.enhancedDescription,
        contactPhone: entities.contactPhone,
        contactEmail: entities.contactEmail
      }
    } catch (error) {
      console.error('Error enhancing with AI:', error)
      // Fallback to enhanceWithoutAI method
      return this.enhanceWithoutAI(originalInput, entities)
    }
  }

  /**
   * Build complete business profile from enhanced entities
   */
  private async buildBusinessProfile(entities: BusinessEntities): Promise<BusinessProfile> {
    console.log('🔍 BUSINESS ANALYSIS DEBUG - Building profile from entities:', entities)
    console.log('🔍 BUSINESS ANALYSIS DEBUG - Business name from entities:', entities.businessName)

    // Get cultural context based on location and business type
    const culturalContext = await this.getCulturalContext(
      entities.location!.region,
      entities.businessType!
    )
    
    // Build contact info
    const contactInfo: ContactInfo = {
      phone: entities.contactPhone || '+254700000000',
      email: entities.contactEmail,
      address: `${entities.location!.area}, ${entities.location!.county}`,
      businessHours: culturalContext.businessPractices.businessHours
    }
    
    // Add WhatsApp if phone is provided
    if (entities.contactPhone) {
      contactInfo.whatsapp = entities.contactPhone
    }
    
    return {
      name: entities.businessName!,
      type: entities.businessType!,
      location: entities.location!,
      services: entities.services,
      description: entities.description,
      targetAudience: 'Local customers and community members',
      contactInfo,
      culturalContext
    }
  }

  /**
   * Get cultural context for business based on region and type
   */
  private async getCulturalContext(region: KenyanRegion, businessType: BusinessType): Promise<CulturalContext> {
    try {
      // Try to get cultural template from database
      const template = await getCulturalTemplate(region, businessType)
      
      if (template && template.templateData) {
        const templateData = template.templateData as any
        return {
          region,
          language: 'ENGLISH',
          businessPractices: templateData.businessPractices || this.getDefaultBusinessPractices(),
          culturalElements: templateData.culturalElements || []
        }
      }
    } catch (error) {
      console.warn('Database unavailable, using default cultural context:', (error as Error).message)
    }
    
    // Fallback to default cultural context
    return {
      region,
      language: 'ENGLISH',
      businessPractices: this.getDefaultBusinessPractices(),
      culturalElements: []
    }
  }

  /**
   * Get default business practices for Kenyan businesses
   */
  private getDefaultBusinessPractices(): BusinessPractices {
    return {
      paymentMethods: [
        {
          type: 'MPESA',
          displayName: 'M-Pesa',
          isActive: true
        },
        {
          type: 'CASH',
          displayName: 'Cash Payment',
          isActive: true
        }
      ],
      communicationPreferences: ['WhatsApp', 'Phone calls'],
      businessHours: 'Mon-Sat: 8AM-6PM',
      culturalConsiderations: ['Local community focus', 'Respectful service']
    }
  }

  /**
   * Validate completeness of business profile
   */
  validateCompleteness(profile: BusinessProfile): ValidationResult {
    const missingFields: string[] = []
    const suggestions: string[] = []

    if (!profile.name || profile.name.trim().length < 2) {
      missingFields.push('businessName')
      suggestions.push('What is the name of your business?')
    }

    if (!profile.type || profile.type === 'OTHER') {
      missingFields.push('businessType')
      suggestions.push('What type of business do you run?')
    }

    if (!profile.location || !profile.location.area) {
      missingFields.push('location')
      suggestions.push('Where is your business located?')
    }

    if (!profile.services || profile.services.length === 0) {
      missingFields.push('services')
      suggestions.push('What services do you offer?')
    }

    if (!profile.contactInfo.phone || profile.contactInfo.phone === '+254700000000') {
      missingFields.push('contactPhone')
      suggestions.push('What is your business phone number?')
    }

    return {
      isComplete: missingFields.length === 0,
      missingFields,
      suggestions
    }
  }

  /**
   * Enhance entities without OpenAI (fallback method)
   */
  private enhanceWithoutAI(originalInput: string, entities: BusinessEntities): BusinessEntities {
    // Generate a business name based on type and location
    let businessName = 'My Business'
    if (entities.businessType && entities.location) {
      const typeNames = {
        'SALON': 'Salon',
        'RESTAURANT': 'Restaurant',
        'SHOP': 'Shop',
        'CLINIC': 'Clinic',
        'GARAGE': 'Garage',
        'HOTEL': 'Hotel',
        'SCHOOL': 'School',
        'CHURCH': 'Church',
        'CONSULTANCY': 'Consultancy',
        'TECH_SERVICES': 'Tech Services',
        'AGRICULTURE': 'Farm',
        'TRANSPORT': 'Transport',
        'CONSTRUCTION': 'Construction',
        'ENTERTAINMENT': 'Entertainment',
        'OTHER': 'Business'
      }
      businessName = `${entities.location.area} ${typeNames[entities.businessType] || 'Business'}`
    }

    // Enhance services based on business type
    let enhancedServices = entities.services
    if (entities.businessType && entities.services.length === 1 && entities.services[0] === 'General services') {
      const servicesByType: Record<BusinessType, string[]> = {
        'SALON': ['Hair styling', 'Hair cutting', 'Manicure', 'Pedicure'],
        'RESTAURANT': ['Local dishes', 'Beverages', 'Takeaway'],
        'SHOP': ['Retail goods', 'Customer service'],
        'CLINIC': ['Medical consultation', 'Health services'],
        'GARAGE': ['Car repair', 'Maintenance', 'Spare parts'],
        'HOTEL': ['Accommodation', 'Room service', 'Catering'],
        'SCHOOL': ['Education', 'Training', 'Tutoring'],
        'CHURCH': ['Worship services', 'Community events'],
        'CONSULTANCY': ['Professional advice', 'Business consulting'],
        'TECH_SERVICES': ['Computer repair', 'IT support', 'Software services'],
        'AGRICULTURE': ['Crop farming', 'Livestock', 'Agricultural products'],
        'TRANSPORT': ['Passenger transport', 'Cargo services'],
        'CONSTRUCTION': ['Building construction', 'Renovation', 'Maintenance'],
        'ENTERTAINMENT': ['Events', 'Music', 'Entertainment services'],
        'OTHER': ['General services', 'Professional services']
      }
      enhancedServices = servicesByType[entities.businessType] || ['General services']
    }

    return {
      businessName,
      businessType: entities.businessType || 'OTHER',
      location: entities.location || {
        county: 'Nairobi',
        area: 'CBD',
        region: 'HIGHLAND'
      },
      services: enhancedServices,
      description: originalInput,
      contactPhone: entities.contactPhone,
      contactEmail: entities.contactEmail
    }
  }

  /**
   * Enhance business profile with additional context
   */
  async enhanceWithContext(profile: BusinessProfile): Promise<BusinessProfile> {
    // This could include additional AI enhancement, market research, etc.
    // For now, just return the profile as-is
    return profile
  }
}

// Export singleton instance
export const businessAnalysisService = new BusinessAnalysisEngine()