/**
 * API Versioning System for PagesLab
 * Handles API version detection, routing, and compatibility
 */

import { NextRequest, NextResponse } from 'next/server'

export type ApiVersion = 'v1' | 'v2'

export interface VersionedEndpoint {
  version: ApiVersion
  path: string
  handler: (request: NextRequest) => Promise<NextResponse>
  deprecated?: boolean
  deprecationDate?: string
  migrationGuide?: string
}

export interface ApiVersionInfo {
  version: ApiVersion
  status: 'stable' | 'beta' | 'deprecated'
  releaseDate: string
  deprecationDate?: string
  supportEndDate?: string
  features: string[]
  breaking_changes?: string[]
}

// API version configurations
export const API_VERSIONS: Record<ApiVersion, ApiVersionInfo> = {
  v1: {
    version: 'v1',
    status: 'stable',
    releaseDate: '2024-01-01',
    features: [
      'Basic website generation',
      'Business analysis',
      'Content generation',
      'Design generation',
      'Authentication',
      'Rate limiting'
    ]
  },
  v2: {
    version: 'v2',
    status: 'beta',
    releaseDate: '2024-07-01',
    features: [
      'Enhanced website generation',
      'Advanced business analysis',
      'AI-powered content generation',
      'Premium design templates',
      'Performance optimization',
      'Analytics tracking',
      'File upload support',
      'Mobile optimization'
    ]
  }
}

/**
 * Extract API version from request
 */
export function extractApiVersion(request: NextRequest): ApiVersion {
  // Check version in URL path (e.g., /api/v2/generate-website)
  const pathMatch = request.nextUrl.pathname.match(/^\/api\/(v\d+)\//)
  if (pathMatch) {
    const version = pathMatch[1] as ApiVersion
    if (isValidVersion(version)) {
      return version
    }
  }

  // Check version in Accept header (e.g., Accept: application/vnd.pageslab.v2+json)
  const acceptHeader = request.headers.get('Accept')
  if (acceptHeader) {
    const versionMatch = acceptHeader.match(/application\/vnd\.pageslab\.(v\d+)\+json/)
    if (versionMatch) {
      const version = versionMatch[1] as ApiVersion
      if (isValidVersion(version)) {
        return version
      }
    }
  }

  // Check version in custom header
  const versionHeader = request.headers.get('X-API-Version')
  if (versionHeader && isValidVersion(versionHeader as ApiVersion)) {
    return versionHeader as ApiVersion
  }

  // Default to latest stable version
  return 'v2'
}

/**
 * Validate if version is supported
 */
export function isValidVersion(version: string): version is ApiVersion {
  return version in API_VERSIONS
}

/**
 * Get version information
 */
export function getVersionInfo(version: ApiVersion): ApiVersionInfo {
  return API_VERSIONS[version]
}

/**
 * Check if version is deprecated
 */
export function isVersionDeprecated(version: ApiVersion): boolean {
  const info = API_VERSIONS[version]
  return info.status === 'deprecated'
}

/**
 * Add version headers to response
 */
export function addVersionHeaders(response: NextResponse, version: ApiVersion): NextResponse {
  const info = API_VERSIONS[version]
  
  response.headers.set('X-API-Version', version)
  response.headers.set('X-API-Status', info.status)
  
  if (info.deprecationDate) {
    response.headers.set('X-API-Deprecation-Date', info.deprecationDate)
  }
  
  if (info.supportEndDate) {
    response.headers.set('X-API-Support-End-Date', info.supportEndDate)
  }

  // Add deprecation warning if applicable
  if (info.status === 'deprecated') {
    response.headers.set('Warning', `299 - "API version ${version} is deprecated. Please migrate to a newer version."`)
  }

  return response
}

/**
 * Create versioned API handler
 */
export function createVersionedHandler(
  handlers: Partial<Record<ApiVersion, (request: NextRequest) => Promise<NextResponse>>>,
  defaultVersion: ApiVersion = 'v2'
) {
  return async (request: NextRequest): Promise<NextResponse> => {
    const version = extractApiVersion(request)
    const handler = handlers[version]

    if (!handler) {
      return NextResponse.json({
        success: false,
        error: `API version ${version} is not supported for this endpoint`,
        code: 'UNSUPPORTED_VERSION',
        supportedVersions: Object.keys(handlers),
        currentVersion: version
      }, { status: 400 })
    }

    try {
      const response = await handler(request)
      return addVersionHeaders(response, version)
    } catch (error) {
      console.error(`API ${version} handler error:`, error)
      
      const errorResponse = NextResponse.json({
        success: false,
        error: 'Internal server error',
        code: 'INTERNAL_ERROR',
        version
      }, { status: 500 })
      
      return addVersionHeaders(errorResponse, version)
    }
  }
}

/**
 * Version compatibility middleware
 */
export function versionCompatibility() {
  return async (request: NextRequest): Promise<NextResponse | null> => {
    const version = extractApiVersion(request)
    const info = API_VERSIONS[version]

    // Check if version exists
    if (!info) {
      return NextResponse.json({
        success: false,
        error: `API version ${version} does not exist`,
        code: 'VERSION_NOT_FOUND',
        availableVersions: Object.keys(API_VERSIONS)
      }, { status: 404 })
    }

    // Check if version is still supported
    if (info.supportEndDate && new Date() > new Date(info.supportEndDate)) {
      return NextResponse.json({
        success: false,
        error: `API version ${version} is no longer supported`,
        code: 'VERSION_UNSUPPORTED',
        supportEndDate: info.supportEndDate,
        availableVersions: Object.keys(API_VERSIONS).filter(v => {
          const vInfo = API_VERSIONS[v as ApiVersion]
          return !vInfo.supportEndDate || new Date() <= new Date(vInfo.supportEndDate)
        })
      }, { status: 410 })
    }

    // Continue processing
    return null
  }
}

/**
 * Get API version documentation
 */
export function getVersionDocumentation() {
  return {
    versions: API_VERSIONS,
    currentVersion: 'v2',
    versioningStrategy: {
      urlPath: 'Include version in URL path: /api/v2/endpoint',
      acceptHeader: 'Use Accept header: Accept: application/vnd.pageslab.v2+json',
      customHeader: 'Use custom header: X-API-Version: v2'
    },
    deprecationPolicy: {
      notice: 'Deprecated versions receive 6 months notice before removal',
      support: 'Security updates only for deprecated versions',
      migration: 'Migration guides provided for breaking changes'
    }
  }
}

/**
 * Migration helper for version upgrades
 */
export function createMigrationHelper(fromVersion: ApiVersion, toVersion: ApiVersion) {
  return {
    from: fromVersion,
    to: toVersion,
    changes: getMigrationChanges(fromVersion, toVersion),
    guide: getMigrationGuide(fromVersion, toVersion)
  }
}

function getMigrationChanges(from: ApiVersion, to: ApiVersion): string[] {
  // Define migration changes between versions
  const migrations: Record<string, string[]> = {
    'v1-v2': [
      'Enhanced business analysis with cultural context',
      'AI-powered image keyword generation',
      'Performance optimization features',
      'Advanced error handling',
      'Analytics tracking',
      'File upload support'
    ]
  }

  return migrations[`${from}-${to}`] || []
}

function getMigrationGuide(from: ApiVersion, to: ApiVersion): string {
  const guides: Record<string, string> = {
    'v1-v2': `
Migration Guide: v1 to v2

1. Update API endpoints to use /api/v2/ prefix
2. Update request headers to include X-API-Version: v2
3. Review new response formats for enhanced data
4. Implement error handling for new error codes
5. Take advantage of new performance features
6. Update authentication to use enhanced middleware

Breaking Changes:
- Enhanced business analysis response format
- New required fields for cultural context
- Updated error response structure

New Features:
- AI-powered content generation
- Performance optimization
- Analytics tracking
- File upload support
    `
  }

  return guides[`${from}-${to}`] || 'No migration guide available'
}
