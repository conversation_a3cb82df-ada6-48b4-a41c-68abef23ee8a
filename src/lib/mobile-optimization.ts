/**
 * Mobile Optimization Service for PagesLab
 * Provides comprehensive mobile-first optimizations and PWA features
 */

export interface MobileOptimizationConfig {
  touchOptimization: boolean
  gestureSupport: boolean
  orientationHandling: boolean
  viewportOptimization: boolean
  performanceOptimization: boolean
  accessibilityEnhancements: boolean
  offlineSupport: boolean
}

export interface TouchGesture {
  type: 'tap' | 'swipe' | 'pinch' | 'long-press'
  direction?: 'left' | 'right' | 'up' | 'down'
  element: HTMLElement
  callback: (event: TouchEvent) => void
}

export interface ViewportConfig {
  width: 'device-width'
  initialScale: number
  maximumScale: number
  minimumScale: number
  userScalable: boolean
  viewportFit: 'cover' | 'contain' | 'auto'
}

class MobileOptimizationService {
  private touchGestures: Map<string, TouchGesture> = new Map()
  private orientationChangeCallbacks: Array<(orientation: string) => void> = []
  private isTouch = false
  private lastTouchTime = 0

  constructor() {
    // Only initialize in browser environment
    if (typeof window !== 'undefined') {
      this.detectTouchDevice()
      this.setupOrientationHandling()
      this.setupViewportOptimization()
      this.setupPerformanceOptimizations()
    }
  }

  /**
   * Detect if device supports touch
   */
  private detectTouchDevice(): void {
    // Check if we're in a browser environment
    if (typeof window === 'undefined') {
      this.isTouch = false
      return
    }

    this.isTouch = 'ontouchstart' in window || navigator.maxTouchPoints > 0

    if (this.isTouch) {
      document.body.classList.add('touch-device')
      this.setupTouchOptimizations()
    } else {
      document.body.classList.add('no-touch')
    }
  }

  /**
   * Setup touch-specific optimizations
   */
  private setupTouchOptimizations(): void {
    // Prevent 300ms click delay
    document.addEventListener('touchstart', () => {
      this.lastTouchTime = Date.now()
    }, { passive: true })

    // Fast tap handling
    document.addEventListener('touchend', (e) => {
      if (Date.now() - this.lastTouchTime < 150) {
        const target = e.target as HTMLElement
        if (target && typeof target.click === 'function') {
          e.preventDefault()
          target.click()
        }
      }
    }, { passive: false })

    // Prevent zoom on double tap for buttons
    document.addEventListener('touchend', (e) => {
      const target = e.target as HTMLElement
      if (target.matches('button, .btn, [role="button"]')) {
        e.preventDefault()
      }
    })

    // Add touch-action CSS for better performance
    this.addTouchActionStyles()
  }

  /**
   * Add touch-action CSS for better performance
   */
  private addTouchActionStyles(): void {
    const style = document.createElement('style')
    style.textContent = `
      /* Touch optimization styles */
      button, .btn, [role="button"] {
        touch-action: manipulation;
        -webkit-tap-highlight-color: transparent;
      }
      
      .scrollable {
        touch-action: pan-y;
        -webkit-overflow-scrolling: touch;
      }
      
      .draggable {
        touch-action: none;
      }
      
      .pinch-zoom {
        touch-action: pinch-zoom;
      }
      
      /* Improve touch targets */
      .touch-device button,
      .touch-device .btn,
      .touch-device [role="button"],
      .touch-device input[type="checkbox"],
      .touch-device input[type="radio"] {
        min-height: 44px;
        min-width: 44px;
      }
      
      /* Better focus indicators for touch */
      .touch-device *:focus {
        outline: 3px solid #2563eb;
        outline-offset: 2px;
      }
    `
    document.head.appendChild(style)
  }

  /**
   * Setup orientation change handling
   */
  private setupOrientationHandling(): void {
    if (typeof window === 'undefined') return

    const handleOrientationChange = () => {
      const orientation = window.screen?.orientation?.type ||
                         (window.innerHeight > window.innerWidth ? 'portrait' : 'landscape')

      document.body.setAttribute('data-orientation', orientation)
      
      // Notify callbacks
      this.orientationChangeCallbacks.forEach(callback => {
        try {
          callback(orientation)
        } catch (error) {
          console.error('Orientation change callback error:', error)
        }
      })

      // Fix viewport height on mobile browsers
      this.fixViewportHeight()
    }

    // Listen for orientation changes
    if (window.screen?.orientation) {
      window.screen.orientation.addEventListener('change', handleOrientationChange)
    } else {
      window.addEventListener('orientationchange', handleOrientationChange)
    }

    // Initial setup
    handleOrientationChange()
  }

  /**
   * Fix viewport height issues on mobile browsers
   */
  private fixViewportHeight(): void {
    // Set CSS custom property for real viewport height
    const vh = window.innerHeight * 0.01
    document.documentElement.style.setProperty('--vh', `${vh}px`)

    // Update on resize
    const updateVH = () => {
      const vh = window.innerHeight * 0.01
      document.documentElement.style.setProperty('--vh', `${vh}px`)
    }

    window.addEventListener('resize', updateVH, { passive: true })
  }

  /**
   * Setup viewport optimization
   */
  private setupViewportOptimization(): void {
    if (typeof window === 'undefined') return

    const viewport = document.querySelector('meta[name="viewport"]') as HTMLMetaElement
    
    if (!viewport) {
      const meta = document.createElement('meta')
      meta.name = 'viewport'
      meta.content = 'width=device-width, initial-scale=1.0, maximum-scale=5.0, user-scalable=yes, viewport-fit=cover'
      document.head.appendChild(meta)
    } else {
      // Ensure proper viewport settings
      viewport.content = 'width=device-width, initial-scale=1.0, maximum-scale=5.0, user-scalable=yes, viewport-fit=cover'
    }

    // Add safe area CSS variables
    this.addSafeAreaStyles()
  }

  /**
   * Add safe area styles for notched devices
   */
  private addSafeAreaStyles(): void {
    const style = document.createElement('style')
    style.textContent = `
      /* Safe area support for notched devices */
      :root {
        --safe-area-inset-top: env(safe-area-inset-top, 0px);
        --safe-area-inset-right: env(safe-area-inset-right, 0px);
        --safe-area-inset-bottom: env(safe-area-inset-bottom, 0px);
        --safe-area-inset-left: env(safe-area-inset-left, 0px);
      }
      
      .safe-area-padding {
        padding-top: var(--safe-area-inset-top);
        padding-right: var(--safe-area-inset-right);
        padding-bottom: var(--safe-area-inset-bottom);
        padding-left: var(--safe-area-inset-left);
      }
      
      .safe-area-margin {
        margin-top: var(--safe-area-inset-top);
        margin-right: var(--safe-area-inset-right);
        margin-bottom: var(--safe-area-inset-bottom);
        margin-left: var(--safe-area-inset-left);
      }
    `
    document.head.appendChild(style)
  }

  /**
   * Setup performance optimizations
   */
  private setupPerformanceOptimizations(): void {
    if (typeof window === 'undefined') return

    // Passive event listeners for better scroll performance
    const passiveEvents = ['touchstart', 'touchmove', 'wheel', 'scroll']
    passiveEvents.forEach(event => {
      document.addEventListener(event, () => {}, { passive: true })
    })

    // Optimize animations for mobile
    this.optimizeAnimations()

    // Setup intersection observer for lazy loading
    this.setupIntersectionObserver()
  }

  /**
   * Optimize animations for mobile devices
   */
  private optimizeAnimations(): void {
    const style = document.createElement('style')
    style.textContent = `
      /* Optimize animations for mobile */
      @media (max-width: 768px) {
        * {
          animation-duration: 0.3s !important;
          transition-duration: 0.3s !important;
        }
      }
      
      /* Reduce motion for users who prefer it */
      @media (prefers-reduced-motion: reduce) {
        * {
          animation-duration: 0.01ms !important;
          animation-iteration-count: 1 !important;
          transition-duration: 0.01ms !important;
          scroll-behavior: auto !important;
        }
      }
      
      /* GPU acceleration for smooth animations */
      .animate-gpu {
        transform: translateZ(0);
        will-change: transform;
      }
    `
    document.head.appendChild(style)
  }

  /**
   * Setup intersection observer for performance
   */
  private setupIntersectionObserver(): void {
    if ('IntersectionObserver' in window) {
      const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
          if (entry.isIntersecting) {
            entry.target.classList.add('in-viewport')
          }
        })
      }, {
        threshold: 0.1,
        rootMargin: '50px'
      })

      // Observe elements that need viewport-based optimization
      document.querySelectorAll('[data-observe]').forEach(el => {
        observer.observe(el)
      })
    }
  }

  /**
   * Register touch gesture
   */
  registerGesture(id: string, gesture: TouchGesture): void {
    this.touchGestures.set(id, gesture)
    this.setupGestureListener(gesture)
  }

  /**
   * Setup gesture listener
   */
  private setupGestureListener(gesture: TouchGesture): void {
    let startX = 0, startY = 0, startTime = 0

    gesture.element.addEventListener('touchstart', (e) => {
      const touch = e.touches[0]
      startX = touch.clientX
      startY = touch.clientY
      startTime = Date.now()
    }, { passive: true })

    gesture.element.addEventListener('touchend', (e) => {
      if (e.touches.length > 0) return

      const touch = e.changedTouches[0]
      const endX = touch.clientX
      const endY = touch.clientY
      const endTime = Date.now()

      const deltaX = endX - startX
      const deltaY = endY - startY
      const deltaTime = endTime - startTime

      // Detect gesture type
      if (gesture.type === 'tap' && Math.abs(deltaX) < 10 && Math.abs(deltaY) < 10 && deltaTime < 300) {
        gesture.callback(e)
      } else if (gesture.type === 'swipe' && deltaTime < 500) {
        const distance = Math.sqrt(deltaX * deltaX + deltaY * deltaY)
        if (distance > 50) {
          const direction = Math.abs(deltaX) > Math.abs(deltaY) 
            ? (deltaX > 0 ? 'right' : 'left')
            : (deltaY > 0 ? 'down' : 'up')
          
          if (!gesture.direction || gesture.direction === direction) {
            gesture.callback(e)
          }
        }
      } else if (gesture.type === 'long-press' && deltaTime > 500 && Math.abs(deltaX) < 10 && Math.abs(deltaY) < 10) {
        gesture.callback(e)
      }
    }, { passive: true })
  }

  /**
   * Add orientation change callback
   */
  onOrientationChange(callback: (orientation: string) => void): void {
    this.orientationChangeCallbacks.push(callback)
  }

  /**
   * Get device information
   */
  getDeviceInfo(): {
    isTouch: boolean
    isMobile: boolean
    isTablet: boolean
    isDesktop: boolean
    orientation: string
    screenSize: { width: number; height: number }
    pixelRatio: number
  } {
    if (typeof window === 'undefined') {
      return {
        isTouch: false,
        isMobile: false,
        isTablet: false,
        isDesktop: true,
        orientation: 'landscape',
        screenSize: { width: 1024, height: 768 },
        pixelRatio: 1
      }
    }

    const width = window.innerWidth
    const height = window.innerHeight

    return {
      isTouch: this.isTouch,
      isMobile: width < 768,
      isTablet: width >= 768 && width < 1024,
      isDesktop: width >= 1024,
      orientation: height > width ? 'portrait' : 'landscape',
      screenSize: { width, height },
      pixelRatio: window.devicePixelRatio || 1
    }
  }

  /**
   * Optimize images for mobile
   */
  optimizeImagesForMobile(): void {
    const images = document.querySelectorAll('img')
    images.forEach(img => {
      // Add loading="lazy" if not present
      if (!img.hasAttribute('loading')) {
        img.setAttribute('loading', 'lazy')
      }

      // Add proper sizes attribute for responsive images
      if (!img.hasAttribute('sizes')) {
        img.setAttribute('sizes', '(max-width: 768px) 100vw, (max-width: 1024px) 50vw, 33vw')
      }

      // Add decode="async" for better performance
      img.setAttribute('decoding', 'async')
    })
  }

  /**
   * Setup PWA features
   */
  setupPWAFeatures(): void {
    // Add to home screen prompt
    let deferredPrompt: any

    window.addEventListener('beforeinstallprompt', (e) => {
      e.preventDefault()
      deferredPrompt = e
      this.showInstallPrompt()
    })

    // Handle app installation
    window.addEventListener('appinstalled', () => {
      console.log('PWA was installed')
      deferredPrompt = null
    })
  }

  /**
   * Show install prompt
   */
  private showInstallPrompt(): void {
    // This would show a custom install prompt
    // Implementation depends on your UI framework
    console.log('PWA install prompt available')
  }

  /**
   * Initialize mobile optimizations
   */
  init(config: Partial<MobileOptimizationConfig> = {}): void {
    const defaultConfig: MobileOptimizationConfig = {
      touchOptimization: true,
      gestureSupport: true,
      orientationHandling: true,
      viewportOptimization: true,
      performanceOptimization: true,
      accessibilityEnhancements: true,
      offlineSupport: true
    }

    const finalConfig = { ...defaultConfig, ...config }

    if (finalConfig.touchOptimization) {
      this.setupTouchOptimizations()
    }

    if (finalConfig.orientationHandling) {
      this.setupOrientationHandling()
    }

    if (finalConfig.viewportOptimization) {
      this.setupViewportOptimization()
    }

    if (finalConfig.performanceOptimization) {
      this.setupPerformanceOptimizations()
    }

    if (finalConfig.accessibilityEnhancements) {
      this.setupAccessibilityEnhancements()
    }

    if (finalConfig.offlineSupport) {
      this.setupPWAFeatures()
    }

    console.log('Mobile optimization initialized with config:', finalConfig)
  }

  /**
   * Setup accessibility enhancements
   */
  private setupAccessibilityEnhancements(): void {
    // Improve focus management for mobile
    document.addEventListener('focusin', (e) => {
      const target = e.target as HTMLElement
      if (target && this.isTouch) {
        target.scrollIntoView({ behavior: 'smooth', block: 'center' })
      }
    })

    // Add skip links for mobile navigation
    this.addSkipLinks()
  }

  /**
   * Add skip links for better mobile navigation
   */
  private addSkipLinks(): void {
    const skipLink = document.createElement('a')
    skipLink.href = '#main-content'
    skipLink.textContent = 'Skip to main content'
    skipLink.className = 'sr-only focus:not-sr-only focus:absolute focus:top-0 focus:left-0 bg-blue-600 text-white p-2 z-50'
    
    document.body.insertBefore(skipLink, document.body.firstChild)
  }
}

// Singleton instance
export const mobileOptimization = new MobileOptimizationService()

// Utility functions
export const isMobileDevice = () => mobileOptimization.getDeviceInfo().isMobile
export const isTabletDevice = () => mobileOptimization.getDeviceInfo().isTablet
export const isTouchDevice = () => mobileOptimization.getDeviceInfo().isTouch
