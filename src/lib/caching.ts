/**
 * Caching Service for PagesLab
 * Implements multi-level caching with browser storage and server-side Redis simulation
 */

export interface CacheOptions {
  ttl?: number // Time to live in seconds
  compress?: boolean
  namespace?: string
}

export interface CacheEntry<T = any> {
  data: T
  timestamp: number
  ttl: number
  compressed?: boolean
}

export class CacheManager {
  private namespace: string
  private defaultTTL: number

  constructor(namespace = 'pageslab', defaultTTL = 3600) {
    this.namespace = namespace
    this.defaultTTL = defaultTTL
  }

  /**
   * Set a value in cache
   */
  async set<T>(
    key: string, 
    value: T, 
    options: CacheOptions = {}
  ): Promise<void> {
    const {
      ttl = this.defaultTTL,
      compress = false,
      namespace = this.namespace
    } = options

    const entry: CacheEntry<T> = {
      data: value,
      timestamp: Date.now(),
      ttl: ttl * 1000, // Convert to milliseconds
      compressed: compress
    }

    const cacheKey = this.buildKey(key, namespace)
    
    try {
      let serialized = JSON.stringify(entry)
      
      if (compress && typeof value === 'string') {
        serialized = this.compress(serialized)
      }

      // TEMPORARILY DISABLE LOCALSTORAGE CACHE FOR TESTING
      // Store in localStorage (client-side)
      // if (typeof window !== 'undefined') {
      //   localStorage.setItem(cacheKey, serialized)
      // }

      // In a real implementation, also store in Redis
      await this.setInRedis(cacheKey, serialized, ttl)
    } catch (error) {
      console.warn('Cache set failed:', error)
    }
  }

  /**
   * Get a value from cache
   */
  async get<T>(
    key: string, 
    options: Pick<CacheOptions, 'namespace'> = {}
  ): Promise<T | null> {
    const { namespace = this.namespace } = options
    const cacheKey = this.buildKey(key, namespace)

    try {
      // TEMPORARILY DISABLE LOCALSTORAGE CACHE FOR TESTING
      // Try localStorage first (faster)
      let serialized: string | null = null

      // if (typeof window !== 'undefined') {
      //   serialized = localStorage.getItem(cacheKey)
      // }

      // Fallback to Redis
      if (!serialized) {
        serialized = await this.getFromRedis(cacheKey)
      }

      if (!serialized) {
        return null
      }

      // First parse to check if compressed
      const tempEntry = JSON.parse(serialized)
      const entry: CacheEntry<T> = JSON.parse(
        tempEntry.compressed ? this.decompress(serialized) : serialized
      )

      // Check if expired
      if (this.isExpired(entry)) {
        await this.delete(key, options)
        return null
      }

      return entry.data
    } catch (error) {
      console.warn('Cache get failed:', error)
      return null
    }
  }

  /**
   * Delete a value from cache
   */
  async delete(
    key: string, 
    options: Pick<CacheOptions, 'namespace'> = {}
  ): Promise<void> {
    const { namespace = this.namespace } = options
    const cacheKey = this.buildKey(key, namespace)

    try {
      // Remove from localStorage
      if (typeof window !== 'undefined') {
        localStorage.removeItem(cacheKey)
      }

      // Remove from Redis
      await this.deleteFromRedis(cacheKey)
    } catch (error) {
      console.warn('Cache delete failed:', error)
    }
  }

  /**
   * Check if a key exists in cache
   */
  async exists(
    key: string, 
    options: Pick<CacheOptions, 'namespace'> = {}
  ): Promise<boolean> {
    const value = await this.get(key, options)
    return value !== null
  }

  /**
   * Clear all cache entries for a namespace
   */
  async clear(namespace = this.namespace): Promise<void> {
    try {
      // Clear localStorage entries
      if (typeof window !== 'undefined') {
        const keys = Object.keys(localStorage)
        const prefix = `${namespace}:`
        
        keys.forEach(key => {
          if (key.startsWith(prefix)) {
            localStorage.removeItem(key)
          }
        })
      }

      // Clear Redis entries
      await this.clearRedis(namespace)
    } catch (error) {
      console.warn('Cache clear failed:', error)
    }
  }

  /**
   * Get cache statistics
   */
  async getStats(): Promise<{
    totalKeys: number
    totalSize: number
    hitRate: number
    namespaces: string[]
  }> {
    let totalKeys = 0
    let totalSize = 0
    const namespaces = new Set<string>()

    if (typeof window !== 'undefined') {
      const keys = Object.keys(localStorage)
      
      keys.forEach(key => {
        if (key.includes(':')) {
          const namespace = key.split(':')[0]
          namespaces.add(namespace)
          totalKeys++
          
          const value = localStorage.getItem(key)
          if (value) {
            totalSize += value.length
          }
        }
      })
    }

    return {
      totalKeys,
      totalSize,
      hitRate: 0, // Would be calculated from actual usage metrics
      namespaces: Array.from(namespaces)
    }
  }

  /**
   * Memoize a function with caching
   */
  memoize<T extends (...args: any[]) => any>(
    fn: T,
    options: CacheOptions & { keyGenerator?: (...args: Parameters<T>) => string } = {}
  ): T {
    const { keyGenerator, ...cacheOptions } = options

    return ((...args: Parameters<T>) => {
      const key = keyGenerator 
        ? keyGenerator(...args)
        : `memoized:${fn.name}:${JSON.stringify(args)}`

      return this.get(key).then(cached => {
        if (cached !== null) {
          return cached
        }

        const result = fn(...args)
        
        if (result instanceof Promise) {
          return result.then(value => {
            this.set(key, value, cacheOptions)
            return value
          })
        } else {
          this.set(key, result, cacheOptions)
          return result
        }
      })
    }) as T
  }

  /**
   * Build cache key with namespace
   */
  private buildKey(key: string, namespace: string): string {
    return `${namespace}:${key}`
  }

  /**
   * Check if cache entry is expired
   */
  private isExpired(entry: CacheEntry): boolean {
    return Date.now() - entry.timestamp > entry.ttl
  }

  /**
   * Simple compression (in real implementation, use proper compression)
   */
  private compress(data: string): string {
    try {
      // Simplified compression - in production, use LZ-string or similar
      // Handle unicode characters by encoding first
      return btoa(encodeURIComponent(data))
    } catch (error) {
      console.warn('Compression failed, storing uncompressed:', error)
      return data
    }
  }

  /**
   * Simple decompression
   */
  private decompress(data: string): string {
    try {
      return decodeURIComponent(atob(data))
    } catch (error) {
      console.warn('Decompression failed, returning as-is:', error)
      return data
    }
  }

  /**
   * Mock Redis operations (in production, use actual Redis client)
   */
  private async setInRedis(key: string, value: string, ttl: number): Promise<void> {
    // Mock implementation - in production, use Redis client
    // await redis.setex(key, ttl, value)
  }

  private async getFromRedis(key: string): Promise<string | null> {
    // Mock implementation - in production, use Redis client
    // return await redis.get(key)
    return null
  }

  private async deleteFromRedis(key: string): Promise<void> {
    // Mock implementation - in production, use Redis client
    // await redis.del(key)
  }

  private async clearRedis(namespace: string): Promise<void> {
    // Mock implementation - in production, use Redis client
    // const keys = await redis.keys(`${namespace}:*`)
    // if (keys.length > 0) {
    //   await redis.del(...keys)
    // }
  }
}

// Specialized cache managers
export class WebsiteCache extends CacheManager {
  constructor() {
    super('websites', 7200) // 2 hours default TTL
  }

  async cacheWebsite(id: string, website: any): Promise<void> {
    await this.set(`website:${id}`, website, { ttl: 7200 })
  }

  async getCachedWebsite(id: string): Promise<any | null> {
    return this.get(`website:${id}`)
  }

  async cacheGeneratedHTML(businessInput: string, html: string): Promise<void> {
    const key = `generated:${this.hashString(businessInput)}`
    await this.set(key, html, { ttl: 3600, compress: true })
  }

  async getCachedHTML(businessInput: string): Promise<string | null> {
    const key = `generated:${this.hashString(businessInput)}`
    return this.get(key)
  }

  async clearCachedHTML(businessInput: string): Promise<void> {
    const key = `generated:${this.hashString(businessInput)}`
    await this.delete(key)
  }

  private hashString(str: string): string {
    let hash = 0
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i)
      hash = ((hash << 5) - hash) + char
      hash = hash & hash // Convert to 32-bit integer
    }
    return hash.toString(36)
  }
}

export class ImageCache extends CacheManager {
  constructor() {
    super('images', 86400) // 24 hours default TTL
  }

  async cacheOptimizedImage(
    originalUrl: string, 
    optimizedData: any
  ): Promise<void> {
    const key = `optimized:${this.hashUrl(originalUrl)}`
    await this.set(key, optimizedData, { ttl: 86400 })
  }

  async getCachedOptimizedImage(originalUrl: string): Promise<any | null> {
    const key = `optimized:${this.hashUrl(originalUrl)}`
    return this.get(key)
  }

  private hashUrl(url: string): string {
    return btoa(url).replace(/[^a-zA-Z0-9]/g, '').substring(0, 32)
  }
}

// Singleton instances
export const cacheManager = new CacheManager()
export const websiteCache = new WebsiteCache()
export const imageCache = new ImageCache()

// Cache decorators
export function Cached(options: CacheOptions = {}) {
  return function (
    target: any,
    propertyName: string,
    descriptor: PropertyDescriptor
  ) {
    const method = descriptor.value

    descriptor.value = async function (...args: any[]) {
      const key = `${target.constructor.name}:${propertyName}:${JSON.stringify(args)}`
      
      const cached = await cacheManager.get(key)
      if (cached !== null) {
        return cached
      }

      const result = await method.apply(this, args)
      await cacheManager.set(key, result, options)
      
      return result
    }

    return descriptor
  }
}

// Performance monitoring
export class PerformanceMonitor {
  private metrics: Map<string, number[]> = new Map()

  startTimer(label: string): () => number {
    const start = performance.now()
    
    return () => {
      const duration = performance.now() - start
      this.recordMetric(label, duration)
      return duration
    }
  }

  recordMetric(label: string, value: number): void {
    if (!this.metrics.has(label)) {
      this.metrics.set(label, [])
    }
    
    const values = this.metrics.get(label)!
    values.push(value)
    
    // Keep only last 100 measurements
    if (values.length > 100) {
      values.shift()
    }
  }

  getMetrics(label: string): {
    count: number
    average: number
    min: number
    max: number
    p95: number
  } | null {
    const values = this.metrics.get(label)
    if (!values || values.length === 0) {
      return null
    }

    const sorted = [...values].sort((a, b) => a - b)
    const count = values.length
    const sum = values.reduce((a, b) => a + b, 0)
    
    return {
      count,
      average: sum / count,
      min: sorted[0],
      max: sorted[sorted.length - 1],
      p95: sorted[Math.floor(sorted.length * 0.95)]
    }
  }

  getAllMetrics(): { [label: string]: any } {
    const result: { [label: string]: any } = {}
    
    for (const [label] of this.metrics) {
      result[label] = this.getMetrics(label)
    }
    
    return result
  }
}

export const performanceMonitor = new PerformanceMonitor()
