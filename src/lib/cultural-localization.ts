import type {
  KenyanRegion,
  BusinessType,
  CulturalContext,
  CulturalElement,
  BusinessPractices,
  PaymentMethod,
  ColorScheme
} from '@/types'
import { getCulturalTemplate, getAllCulturalTemplates } from './database'

/**
 * Cultural Localization Service
 * Provides comprehensive Kenyan business terminology and local language support
 * for region-specific styling, business practices, and cultural elements
 */
export class CulturalLocalizationService {
  private culturalCache: Map<string, any> = new Map()

  // Comprehensive Kenyan business terminology database
  private kenyanBusinessTerms = {
    // Business types in major Kenyan languages
    businessTypes: {
      'restaurant': {
        swahili: 'mkahawa',
        kikuyu: 'nyumba ya chakula',
        luo: 'ot chiemo',
        luhya: 'enyumba ya chakula',
        kamba: 'nyumba ya kyakula'
      },
      'shop': {
        swahili: 'duka',
        kikuyu: 'duka',
        luo: 'duka',
        luhya: 'duka',
        kamba: 'duka'
      },
      'salon': {
        swahili: 'saluni',
        kikuyu: 'handũ ha gũthondeka',
        luo: 'kar chwecho',
        luhya: 'esaluni',
        kamba: 'saluni'
      },
      'hotel': {
        swahili: 'hoteli',
        kikuyu: 'nyumba ya ageni',
        luo: 'ot welo',
        luhya: 'hoteli',
        kamba: 'hoteli'
      },
      'clinic': {
        swahili: 'kliniki',
        kikuyu: 'nyumba ya ndagua',
        luo: 'kar thieth',
        luhya: 'ekiliniki',
        kamba: 'kiliniki'
      }
    },

    // Common business phrases in local languages
    commonPhrases: {
      'welcome': {
        swahili: 'karibu',
        kikuyu: 'wega wa gũũka',
        luo: 'oyaore',
        luhya: 'mukhwanilile',
        kamba: 'mwaisye'
      },
      'thank_you': {
        swahili: 'asante',
        kikuyu: 'nĩ ngũgocera',
        luo: 'erokamano',
        luhya: 'webale',
        kamba: 'nesa'
      },
      'quality_service': {
        swahili: 'huduma ya ubora',
        kikuyu: 'thoguo wa njega',
        luo: 'tich maber',
        luhya: 'obukoni obulahi',
        kamba: 'wĩthio wa mbesa'
      }
    },

    // Regional greetings with time awareness
    regionalGreetings: {
      COASTAL: {
        morning: 'Hujambo asubuhi',
        afternoon: 'Hujambo mchana',
        evening: 'Hujambo jioni',
        general: 'Hujambo'
      },
      HIGHLAND: {
        morning: 'Mũrũgamĩrĩre rũciinĩ',
        afternoon: 'Mũrũgamĩrĩre mũthenya',
        evening: 'Mũrũgamĩrĩre hwaĩ-inĩ',
        general: 'Mũrũgamĩrĩre'
      },
      WESTERN: {
        morning: 'Oyaore gokinyi',
        afternoon: 'Oyaore odiechieng',
        evening: 'Oyaore oimore',
        general: 'Oyaore'
      },
      NORTHERN: {
        morning: 'Salam subax wanaagsan',
        afternoon: 'Salam galab wanaagsan',
        evening: 'Salam fiid wanaagsan',
        general: 'Salam'
      },
      CENTRAL: {
        morning: 'Mũrũgamĩrĩre rũciinĩ',
        afternoon: 'Mũrũgamĩrĩre mũthenya',
        evening: 'Mũrũgamĩrĩre hwaĩ-inĩ',
        general: 'Mũrũgamĩrĩre'
      },
      EASTERN: {
        morning: 'Mwaisye ũtukũ',
        afternoon: 'Mwaisye mũthenya',
        evening: 'Mwaisye kĩroko',
        general: 'Mwaisye'
      }
    }
  }

  /**
   * Get regional style configuration based on Kenyan region
   */
  getRegionalStyle(region: KenyanRegion): RegionalStyleConfig {
    const cacheKey = `regional-style-${region}`
    
    if (this.culturalCache.has(cacheKey)) {
      return this.culturalCache.get(cacheKey)
    }

    const regionalStyle = this.generateRegionalStyle(region)
    this.culturalCache.set(cacheKey, regionalStyle)
    
    return regionalStyle
  }

  /**
   * Apply cultural elements to a design specification
   */
  async applyCulturalElements(region: KenyanRegion, businessType: BusinessType, baseDesign: any): Promise<CulturalizedDesign> {
    try {
      const template = await getCulturalTemplate(region, businessType)
      
      if (template && template.templateData) {
        const templateData = template.templateData as any
        
        return {
          ...baseDesign,
          colorScheme: this.mergeCulturalColors(baseDesign.colorScheme, templateData.colorScheme),
          culturalElements: templateData.culturalElements || [],
          businessPractices: templateData.businessPractices || this.getDefaultBusinessPractices(region),
          regionalTheme: this.getRegionalTheme(region),
          localizedTerminology: this.getLocalizedBusinessTerminology(region, businessType)
        }
      }
    } catch (error) {
      console.error('Error applying cultural elements:', error)
    }

    return {
      ...baseDesign,
      colorScheme: this.getRegionalColorScheme(region),
      culturalElements: this.getDefaultCulturalElements(region),
      businessPractices: this.getDefaultBusinessPractices(region),
      regionalTheme: this.getRegionalTheme(region),
      localizedTerminology: this.getLocalizedBusinessTerminology(region, businessType)
    }
  }

  /**
   * Localize content with Kenyan business context (English only)
   */
  localizeContent(content: string, context: CulturalContext): string {
    // Return content in English only - no local language translations
    // Cultural context is maintained through business practices and regional preferences
    return content
  }

  /**
   * Get business terminology for a specific region and business type (English only)
   */
  getLocalizedBusinessTerminology(region: KenyanRegion, businessType: BusinessType): LocalizedTerminology {
    const businessTypeKey = businessType.toLowerCase()

    return {
      businessType: businessTypeKey,
      greetings: {
        general: 'Welcome',
        morning: 'Good morning',
        afternoon: 'Good afternoon',
        evening: 'Good evening'
      },
      commonPhrases: {
        welcome: 'Welcome',
        thankYou: 'Thank you',
        qualityService: 'Quality service'
      }
    }
  }

  /**
   * Get the primary local language for a region
   */
  private getRegionalPrimaryLanguage(region: KenyanRegion): string {
    const regionalLanguages = {
      COASTAL: 'swahili',
      HIGHLAND: 'kikuyu',
      WESTERN: 'luo',
      NORTHERN: 'swahili',
      CENTRAL: 'kikuyu',
      EASTERN: 'kamba'
    }
    
    return regionalLanguages[region] || 'swahili'
  }

  /**
   * Get local business practices for a specific region and business type
   */
  async getLocalBusinessPractices(region: KenyanRegion, businessType: BusinessType): Promise<BusinessPractices> {
    try {
      const template = await getCulturalTemplate(region, businessType)
      
      if (template && template.templateData) {
        const templateData = template.templateData as any
        return templateData.businessPractices || this.getDefaultBusinessPractices(region)
      }
    } catch (error) {
      console.error('Error getting local business practices:', error)
    }

    return this.getDefaultBusinessPractices(region)
  }

  /**
   * Generate regional style configuration
   */
  private generateRegionalStyle(region: KenyanRegion): RegionalStyleConfig {
    const baseStyles = {
      COASTAL: {
        primaryColors: ['#0891b2', '#06b6d4', '#0284c7'],
        secondaryColors: ['#f97316', '#fb923c', '#fdba74'],
        accentColors: ['#06b6d4', '#67e8f9', '#a5f3fc'],
        culturalMotifs: ['dhow boats', 'coconut palms', 'coral patterns', 'swahili architecture']
      },
      HIGHLAND: {
        primaryColors: ['#16a34a', '#22c55e', '#4ade80'],
        secondaryColors: ['#dc2626', '#ef4444', '#f87171'],
        accentColors: ['#3b82f6', '#60a5fa', '#93c5fd'],
        culturalMotifs: ['coffee plants', 'acacia trees', 'kikuyu patterns', 'nairobi skyline']
      },
      WESTERN: {
        primaryColors: ['#65a30d', '#84cc16', '#a3e635'],
        secondaryColors: ['#f59e0b', '#fbbf24', '#fcd34d'],
        accentColors: ['#16a34a', '#22c55e', '#4ade80'],
        culturalMotifs: ['maize plants', 'traditional huts', 'luo patterns', 'lake victoria']
      },
      NORTHERN: {
        primaryColors: ['#f97316', '#fb923c', '#fdba74'],
        secondaryColors: ['#dc2626', '#ef4444', '#f87171'],
        accentColors: ['#eab308', '#facc15', '#fde047'],
        culturalMotifs: ['acacia trees', 'camels', 'traditional beadwork', 'desert landscapes']
      },
      CENTRAL: {
        primaryColors: ['#6366f1', '#8b5cf6', '#a855f7'],
        secondaryColors: ['#ec4899', '#f472b6', '#f9a8d4'],
        accentColors: ['#3b82f6', '#60a5fa', '#93c5fd'],
        culturalMotifs: ['government buildings', 'central highlands', 'mixed cultural elements']
      },
      EASTERN: {
        primaryColors: ['#dc2626', '#ef4444', '#f87171'],
        secondaryColors: ['#f97316', '#fb923c', '#fdba74'],
        accentColors: ['#eab308', '#facc15', '#fde047'],
        culturalMotifs: ['traditional crafts', 'baobab trees', 'kamba culture', 'semi-arid landscapes']
      }
    }

    return baseStyles[region] || baseStyles.CENTRAL
  }

  private getRegionalColorScheme(region: KenyanRegion): ColorScheme {
    const regionalStyle = this.getRegionalStyle(region)
    
    return {
      primary: regionalStyle.primaryColors[0],
      secondary: regionalStyle.secondaryColors[0],
      accent: regionalStyle.accentColors[0],
      background: '#ffffff',
      text: '#1f2937',
      muted: '#6b7280',
      success: '#10b981',
      warning: '#f59e0b',
      error: '#ef4444'
    }
  }

  private getDefaultCulturalElements(region: KenyanRegion): CulturalElement[] {
    const regionalStyle = this.getRegionalStyle(region)
    
    return [
      {
        type: 'COLOR',
        value: regionalStyle.primaryColors[0],
        description: `Primary color representing ${region.toLowerCase()} region`
      },
      {
        type: 'PATTERN',
        value: regionalStyle.culturalMotifs[0],
        description: `Traditional ${region.toLowerCase()} cultural motif`
      }
    ]
  }

  private getDefaultBusinessPractices(region: KenyanRegion): BusinessPractices {
    const basePaymentMethods: PaymentMethod[] = [
      { type: 'MPESA', displayName: 'M-Pesa', isActive: true },
      { type: 'CASH', displayName: 'Cash Payment', isActive: true }
    ]

    const regionalPaymentMethods: Record<KenyanRegion, PaymentMethod[]> = {
      COASTAL: [...basePaymentMethods, { type: 'AIRTEL_MONEY' as const, displayName: 'Airtel Money', isActive: true }],
      HIGHLAND: [...basePaymentMethods, { type: 'CARD' as const, displayName: 'Card Payment', isActive: true }],
      WESTERN: [...basePaymentMethods, { type: 'AIRTEL_MONEY' as const, displayName: 'Airtel Money', isActive: true }],
      NORTHERN: [...basePaymentMethods],
      CENTRAL: [...basePaymentMethods, { type: 'CARD' as const, displayName: 'Card Payment', isActive: true }],
      EASTERN: [...basePaymentMethods, { type: 'AIRTEL_MONEY' as const, displayName: 'Airtel Money', isActive: true }]
    }

    return {
      paymentMethods: regionalPaymentMethods[region] || basePaymentMethods,
      communicationPreferences: ['WhatsApp', 'Phone calls'],
      businessHours: 'Mon-Sat: 8AM-6PM',
      culturalConsiderations: this.getRegionalCulturalConsiderations(region)
    }
  }

  private getRegionalCulturalConsiderations(region: KenyanRegion): string[] {
    const considerations = {
      COASTAL: ['Respect for Islamic customs', 'Swahili culture integration', 'Coastal hospitality'],
      HIGHLAND: ['Professional environment', 'Modern business practices', 'Diverse backgrounds'],
      WESTERN: ['Community approach', 'Agricultural considerations', 'Family involvement'],
      NORTHERN: ['Pastoral values', 'Traditional leadership', 'Community consensus'],
      CENTRAL: ['Administrative focus', 'Formal procedures', 'Educational emphasis'],
      EASTERN: ['Traditional crafts', 'Community cooperation', 'Extended networks']
    }

    return considerations[region] || considerations.CENTRAL
  }

  private getRegionalTheme(region: KenyanRegion): RegionalTheme {
    const themes = {
      COASTAL: { name: 'Coastal Breeze', description: 'Ocean-inspired design', keywords: ['ocean', 'swahili'] },
      HIGHLAND: { name: 'Highland Professional', description: 'Modern business design', keywords: ['professional', 'modern'] },
      WESTERN: { name: 'Agricultural Harmony', description: 'Community-focused design', keywords: ['community', 'agriculture'] },
      NORTHERN: { name: 'Desert Resilience', description: 'Bold pastoral design', keywords: ['resilient', 'pastoral'] },
      CENTRAL: { name: 'Central Balance', description: 'Administrative excellence', keywords: ['balanced', 'administrative'] },
      EASTERN: { name: 'Traditional Craft', description: 'Heritage celebration', keywords: ['traditional', 'crafts'] }
    }

    return themes[region] || themes.CENTRAL
  }

  private mergeCulturalColors(baseColors: ColorScheme, culturalColors?: any): ColorScheme {
    if (!culturalColors) return baseColors

    return {
      primary: culturalColors.primary || baseColors.primary,
      secondary: culturalColors.secondary || baseColors.secondary,
      accent: culturalColors.accent || baseColors.accent,
      background: culturalColors.background || baseColors.background,
      text: culturalColors.text || baseColors.text,
      muted: culturalColors.muted || baseColors.muted,
      success: culturalColors.success || baseColors.success,
      warning: culturalColors.warning || baseColors.warning,
      error: culturalColors.error || baseColors.error
    }
  }

  /**
   * Add comprehensive Swahili elements to content
   */
  private addSwahiliElements(content: string): string {
    const swahiliPhrases = {
      'Welcome': 'Karibu', 'Service': 'Huduma', 'Quality': 'Ubora',
      'Business': 'Biashara', 'Shop': 'Duka', 'Restaurant': 'Mkahawa',
      'Best': 'Bora zaidi', 'Fresh': 'Mbichi', 'Expert': 'Mtaalamu'
    }

    let localizedContent = content
    Object.entries(swahiliPhrases).forEach(([english, swahili]) => {
      const regex = new RegExp(`\\b${english}\\b`, 'gi')
      localizedContent = localizedContent.replace(regex, `${english} (${swahili})`)
    })

    return localizedContent
  }

  /**
   * Apply regional terminology with local language support
   */
  private applyRegionalTerminology(content: string, region: KenyanRegion): string {
    const regionalTerms = {
      COASTAL: { 'restaurant': 'restaurant (mkahawa)', 'service': 'service (huduma)' },
      HIGHLAND: { 'professional': 'expert (mtaalamu)', 'quality': 'premium (ubora)' },
      WESTERN: { 'community': 'community (jamii)', 'farming': 'farming (kilimo)' },
      NORTHERN: { 'traditional': 'traditional (jadi)', 'community': 'community (jamii)' },
      CENTRAL: { 'government': 'government (serikali)', 'official': 'official (rasmi)' },
      EASTERN: { 'craft': 'craft (ufundi)', 'traditional': 'traditional (jadi)' }
    }

    let localizedContent = content
    const terms = regionalTerms[region] || {}
    
    Object.entries(terms).forEach(([english, local]) => {
      const regex = new RegExp(`\\b${english}\\b`, 'gi')
      localizedContent = localizedContent.replace(regex, local)
    })

    return localizedContent
  }

  /**
   * Apply local business terminology based on region
   */
  private applyLocalBusinessTerminology(content: string, region: KenyanRegion): string {
    const regionalLanguage = this.getRegionalPrimaryLanguage(region)
    let localizedContent = content

    Object.entries(this.kenyanBusinessTerms.businessTypes).forEach(([english, translations]) => {
      const translationsRecord = translations as Record<string, string>
      const localTerm = translationsRecord[regionalLanguage]
      if (localTerm) {
        const regex = new RegExp(`\\b${english}\\b`, 'gi')
        localizedContent = localizedContent.replace(regex, `${english} (${localTerm})`)
      }
    })

    return localizedContent
  }

  /**
   * Add cultural phrases with time-aware greetings
   */
  private addCulturalPhrases(content: string, region: KenyanRegion): string {
    const currentHour = new Date().getHours()
    let timeOfDay = 'general'
    
    if (currentHour >= 5 && currentHour < 12) timeOfDay = 'morning'
    else if (currentHour >= 12 && currentHour < 17) timeOfDay = 'afternoon'
    else if (currentHour >= 17 && currentHour < 21) timeOfDay = 'evening'

    const greetings = this.kenyanBusinessTerms.regionalGreetings[region] || this.kenyanBusinessTerms.regionalGreetings.CENTRAL
    const greetingsRecord = greetings as Record<string, string>
    const greeting = greetingsRecord[timeOfDay] || greetingsRecord.general
    
    if (content.toLowerCase().includes('welcome') || content.toLowerCase().includes('we are')) {
      return `${greeting}! ${content}`
    }

    return content
  }

  clearCache(): void {
    this.culturalCache.clear()
  }

  async getAllCulturalData(): Promise<CulturalTemplate[]> {
    try {
      const templates = await getAllCulturalTemplates()
      return templates.map(template => ({
        ...template,
        region: template.region as KenyanRegion,
        businessType: template.businessType as BusinessType
      }))
    } catch (error) {
      console.error('Error getting all cultural data:', error)
      return []
    }
  }
}

// Type definitions
export interface RegionalStyleConfig {
  primaryColors: string[]
  secondaryColors: string[]
  accentColors: string[]
  culturalMotifs: string[]
}

export interface CulturalizedDesign {
  colorScheme: ColorScheme
  culturalElements: CulturalElement[]
  businessPractices: BusinessPractices
  regionalTheme: RegionalTheme
  localizedTerminology: LocalizedTerminology
}

export interface RegionalTheme {
  name: string
  description: string
  keywords: string[]
}

export interface LocalizedTerminology {
  businessType: string
  greetings: {
    morning: string
    afternoon: string
    evening: string
    general: string
  }
  commonPhrases: {
    welcome: string
    thankYou: string
    qualityService: string
  }
}

export interface CulturalTemplate {
  id: string
  region: KenyanRegion
  businessType: BusinessType
  templateData: any
  createdAt: Date
}

// Export singleton instance
export const culturalLocalizationService = new CulturalLocalizationService()