import { type ClassValue, clsx } from 'clsx'
import { twMerge } from 'tailwind-merge'

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

export function generateSubdomain(businessName: string): string {
  return businessName
    .toLowerCase()
    .replace(/[^a-z0-9]/g, '-')
    .replace(/-+/g, '-')
    .replace(/^-|-$/g, '')
    .substring(0, 30) + '-' + Math.random().toString(36).substring(2, 8)
}

export function formatKenyanPhone(phone: string): string {
  // Remove all non-digits
  const digits = phone.replace(/\D/g, '')
  
  // Handle different formats
  if (digits.startsWith('254')) {
    return `+${digits}`
  } else if (digits.startsWith('0')) {
    return `+254${digits.substring(1)}`
  } else if (digits.length === 9) {
    return `+254${digits}`
  }
  
  return phone
}

export function formatKenyanAddress(address: string, county?: string): string {
  const parts = [address]
  if (county && !address.toLowerCase().includes(county.toLowerCase())) {
    parts.push(county)
  }
  parts.push('Kenya')
  return parts.join(', ')
}

export function getRegionFromCounty(county: string): import('@/types').KenyanRegion {
  const countyRegionMap: Record<string, import('@/types').KenyanRegion> = {
    // Coastal
    'mombasa': 'COASTAL',
    'kwale': 'COASTAL',
    'kilifi': 'COASTAL',
    'tana river': 'COASTAL',
    'lamu': 'COASTAL',
    'taita-taveta': 'COASTAL',
    
    // Highland/Central
    'nairobi': 'HIGHLAND',
    'kiambu': 'HIGHLAND',
    'murang\'a': 'HIGHLAND',
    'nyeri': 'HIGHLAND',
    'kirinyaga': 'HIGHLAND',
    'nyandarua': 'HIGHLAND',
    'laikipia': 'HIGHLAND',
    'nakuru': 'HIGHLAND',
    'kajiado': 'HIGHLAND',
    
    // Western
    'kakamega': 'WESTERN',
    'vihiga': 'WESTERN',
    'bungoma': 'WESTERN',
    'busia': 'WESTERN',
    'siaya': 'WESTERN',
    'kisumu': 'WESTERN',
    'homa bay': 'WESTERN',
    'migori': 'WESTERN',
    'kisii': 'WESTERN',
    'nyamira': 'WESTERN',
    
    // Northern
    'turkana': 'NORTHERN',
    'west pokot': 'NORTHERN',
    'samburu': 'NORTHERN',
    'trans nzoia': 'NORTHERN',
    'uasin gishu': 'NORTHERN',
    'elgeyo-marakwet': 'NORTHERN',
    'nandi': 'NORTHERN',
    'baringo': 'NORTHERN',
    'marsabit': 'NORTHERN',
    'isiolo': 'NORTHERN',
    'meru': 'NORTHERN',
    'tharaka-nithi': 'NORTHERN',
    'embu': 'NORTHERN',
    
    // Eastern
    'machakos': 'EASTERN',
    'makueni': 'EASTERN',
    'kitui': 'EASTERN',
    'garissa': 'EASTERN',
    'wajir': 'EASTERN',
    'mandera': 'EASTERN',
  }
  
  return countyRegionMap[county.toLowerCase()] || 'CENTRAL'
}