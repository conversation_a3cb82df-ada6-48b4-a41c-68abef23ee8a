/**
 * Performance Optimization Service for PagesLab
 * Integrates image optimization, caching, lazy loading, and performance monitoring
 */

import { imageOptimizer, lazyImageLoader } from './image-optimization'
import { websiteCache, imageCache, performanceMonitor } from './caching'

export interface PerformanceConfig {
  enableImageOptimization: boolean
  enableLazyLoading: boolean
  enableCaching: boolean
  enableCompression: boolean
  enableCDN: boolean
  cacheStrategy: 'aggressive' | 'moderate' | 'conservative'
}

export interface PerformanceMetrics {
  pageLoadTime: number
  firstContentfulPaint: number
  largestContentfulPaint: number
  cumulativeLayoutShift: number
  firstInputDelay: number
  totalBlockingTime: number
  imageOptimizationSavings: number
  cacheHitRate: number
}

export class PerformanceOptimizer {
  private config: PerformanceConfig
  private observer: PerformanceObserver | null = null

  constructor(config: Partial<PerformanceConfig> = {}) {
    this.config = {
      enableImageOptimization: true,
      enableLazyLoading: true,
      enableCaching: true,
      enableCompression: true,
      enableCDN: false,
      cacheStrategy: 'moderate',
      ...config
    }

    this.initializePerformanceMonitoring()
  }

  /**
   * Optimize a complete website for performance
   */
  async optimizeWebsite(websiteData: any): Promise<any> {
    const timer = performanceMonitor.startTimer('website-optimization')

    try {
      let optimizedData = { ...websiteData }

      // Handle nested website structure
      const website = websiteData.website || websiteData
      
      if (!website) {
        throw new Error('No website data found to optimize')
      }

      // Optimize HTML
      if (this.config.enableCompression && website.html) {
        const optimizedHTML = await this.optimizeHTML(website.html)
        if (websiteData.website) {
          optimizedData.website = { ...website, html: optimizedHTML }
        } else {
          optimizedData.html = optimizedHTML
        }
      }

      // Optimize CSS
      if (this.config.enableCompression && website.css) {
        const optimizedCSS = await this.optimizeCSS(website.css)
        if (websiteData.website) {
          optimizedData.website = { ...optimizedData.website, css: optimizedCSS }
        } else {
          optimizedData.css = optimizedCSS
        }
      }

      // Add performance optimizations to HTML
      const currentHTML = optimizedData.website?.html || optimizedData.html
      if (currentHTML) {
        const enhancedHTML = this.addPerformanceOptimizations(currentHTML)
        if (optimizedData.website) {
          optimizedData.website.html = enhancedHTML
        } else {
          optimizedData.html = enhancedHTML
        }
      }

      // Cache the optimized website
      if (this.config.enableCaching) {
        await websiteCache.cacheWebsite(
          websiteData.id || 'temp',
          optimizedData
        )
      }

      return optimizedData
    } finally {
      timer()
    }
  }

  /**
   * Optimize HTML content
   */
  private async optimizeHTML(html: string): Promise<string> {
    let optimized = html

    // Minify HTML
    optimized = this.minifyHTML(optimized)

    // Add preload hints for critical resources
    optimized = this.addPreloadHints(optimized)

    // Add lazy loading attributes to images
    if (this.config.enableLazyLoading) {
      optimized = this.addLazyLoadingAttributes(optimized)
    }

    // Add performance monitoring script
    optimized = this.addPerformanceMonitoring(optimized)

    return optimized
  }

  /**
   * Optimize CSS content
   */
  private async optimizeCSS(css: string): Promise<string> {
    let optimized = css

    // Minify CSS
    optimized = this.minifyCSS(optimized)

    // Add critical CSS optimizations
    optimized = this.addCriticalCSSOptimizations(optimized)

    return optimized
  }

  /**
   * Minify HTML
   */
  private minifyHTML(html: string): string {
    return html
      .replace(/\s+/g, ' ') // Collapse whitespace
      .replace(/>\s+</g, '><') // Remove whitespace between tags
      .replace(/<!--[\s\S]*?-->/g, '') // Remove comments
      .trim()
  }

  /**
   * Minify CSS
   */
  private minifyCSS(css: string): string {
    return css
      .replace(/\/\*[\s\S]*?\*\//g, '') // Remove comments
      .replace(/\s+/g, ' ') // Collapse whitespace
      .replace(/;\s*}/g, '}') // Remove unnecessary semicolons
      .replace(/\s*{\s*/g, '{') // Clean up braces
      .replace(/\s*}\s*/g, '}')
      .replace(/\s*;\s*/g, ';') // Clean up semicolons
      .replace(/\s*:\s*/g, ':') // Clean up colons
      .trim()
  }

  /**
   * Add preload hints for critical resources
   */
  private addPreloadHints(html: string): string {
    const preloadHints = `
    <!-- Preload critical resources -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link rel="dns-prefetch" href="//www.google-analytics.com">
    <link rel="dns-prefetch" href="//fonts.googleapis.com">
    `

    return html.replace('<head>', `<head>${preloadHints}`)
  }

  /**
   * Add lazy loading attributes to images
   */
  private addLazyLoadingAttributes(html: string): string {
    return html.replace(
      /<img([^>]*?)src="([^"]*)"([^>]*?)>/gi,
      '<img$1data-src="$2" src="data:image/svg+xml,%3Csvg xmlns=\'http://www.w3.org/2000/svg\' viewBox=\'0 0 1 1\'%3E%3C/svg%3E" loading="lazy" class="lazy"$3>'
    )
  }

  /**
   * Add performance monitoring script
   */
  private addPerformanceMonitoring(html: string): string {
    const monitoringScript = `
    <script>
      // Performance monitoring
      if ('performance' in window) {
        window.addEventListener('load', () => {
          setTimeout(() => {
            const perfData = performance.getEntriesByType('navigation')[0];
            const paintData = performance.getEntriesByType('paint');
            
            const metrics = {
              loadTime: perfData.loadEventEnd - perfData.loadEventStart,
              domContentLoaded: perfData.domContentLoadedEventEnd - perfData.domContentLoadedEventStart,
              firstPaint: paintData.find(p => p.name === 'first-paint')?.startTime || 0,
              firstContentfulPaint: paintData.find(p => p.name === 'first-contentful-paint')?.startTime || 0
            };
            
            // Send metrics to analytics (implement as needed)
            console.log('Performance Metrics:', metrics);
          }, 0);
        });
      }
    </script>
    `

    return html.replace('</body>', `${monitoringScript}</body>`)
  }

  /**
   * Add critical CSS optimizations
   */
  private addCriticalCSSOptimizations(css: string): string {
    const optimizations = `
    /* Performance optimizations */
    * {
      box-sizing: border-box;
    }
    
    img {
      max-width: 100%;
      height: auto;
    }
    
    .lazy {
      opacity: 0;
      transition: opacity 0.3s;
    }
    
    .lazy.loaded {
      opacity: 1;
    }
    
    /* Reduce layout shifts */
    .hero-image {
      aspect-ratio: 16/9;
      object-fit: cover;
    }
    
    /* Optimize font loading */
    @font-face {
      font-display: swap;
    }
    `

    return css + optimizations
  }

  /**
   * Add general performance optimizations to HTML
   */
  private addPerformanceOptimizations(html: string): string {
    let optimized = html

    // Add viewport meta tag if missing
    if (!html.includes('viewport')) {
      optimized = optimized.replace(
        '<head>',
        '<head>\n    <meta name="viewport" content="width=device-width, initial-scale=1.0">'
      )
    }

    // Add charset meta tag if missing
    if (!html.includes('charset')) {
      optimized = optimized.replace(
        '<head>',
        '<head>\n    <meta charset="UTF-8">'
      )
    }

    // Add performance hints
    const performanceHints = `
    <!-- Performance optimizations -->
    <meta name="theme-color" content="#2563eb">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="default">
    `

    optimized = optimized.replace('<head>', `<head>${performanceHints}`)

    return optimized
  }

  /**
   * Initialize performance monitoring
   */
  private initializePerformanceMonitoring(): void {
    if (typeof window === 'undefined' || !('PerformanceObserver' in window)) {
      return
    }

    try {
      // Monitor Core Web Vitals
      this.observer = new PerformanceObserver((list) => {
        for (const entry of list.getEntries()) {
          switch (entry.entryType) {
            case 'largest-contentful-paint':
              performanceMonitor.recordMetric('lcp', entry.startTime)
              break
            case 'first-input':
              performanceMonitor.recordMetric('fid', (entry as any).processingStart - entry.startTime)
              break
            case 'layout-shift':
              if (!(entry as any).hadRecentInput) {
                performanceMonitor.recordMetric('cls', (entry as any).value)
              }
              break
          }
        }
      })

      this.observer.observe({ entryTypes: ['largest-contentful-paint', 'first-input', 'layout-shift'] })
    } catch (error) {
      console.warn('Performance monitoring initialization failed:', error)
    }
  }

  /**
   * Get current performance metrics
   */
  getPerformanceMetrics(): PerformanceMetrics {
    const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming
    const paint = performance.getEntriesByType('paint')

    return {
      pageLoadTime: navigation ? navigation.loadEventEnd - navigation.loadEventStart : 0,
      firstContentfulPaint: paint.find(p => p.name === 'first-contentful-paint')?.startTime || 0,
      largestContentfulPaint: performanceMonitor.getMetrics('lcp')?.average || 0,
      cumulativeLayoutShift: performanceMonitor.getMetrics('cls')?.average || 0,
      firstInputDelay: performanceMonitor.getMetrics('fid')?.average || 0,
      totalBlockingTime: 0, // Would need more complex calculation
      imageOptimizationSavings: 0, // Would be calculated from actual optimizations
      cacheHitRate: 0 // Would be calculated from cache statistics
    }
  }

  /**
   * Generate performance report
   */
  generatePerformanceReport(): {
    score: number
    metrics: PerformanceMetrics
    recommendations: string[]
  } {
    const metrics = this.getPerformanceMetrics()
    const recommendations: string[] = []
    let score = 100

    // Analyze metrics and generate recommendations
    if (metrics.firstContentfulPaint > 2500) {
      recommendations.push('First Contentful Paint is slow. Consider optimizing critical resources.')
      score -= 10
    }

    if (metrics.largestContentfulPaint > 4000) {
      recommendations.push('Largest Contentful Paint is slow. Optimize images and fonts.')
      score -= 15
    }

    if (metrics.cumulativeLayoutShift > 0.25) {
      recommendations.push('Layout shifts detected. Add size attributes to images and reserve space for dynamic content.')
      score -= 10
    }

    if (metrics.firstInputDelay > 300) {
      recommendations.push('First Input Delay is high. Reduce JavaScript execution time.')
      score -= 10
    }

    return {
      score: Math.max(0, score),
      metrics,
      recommendations
    }
  }

  /**
   * Cleanup resources
   */
  destroy(): void {
    if (this.observer) {
      this.observer.disconnect()
      this.observer = null
    }
  }
}

// Singleton instance
export const performanceOptimizer = new PerformanceOptimizer()

// Re-export cache instances for convenience
export { websiteCache, imageCache, performanceMonitor }

// Utility functions
export const optimizeWebsitePerformance = async (websiteData: any) => {
  return performanceOptimizer.optimizeWebsite(websiteData)
}

export const getPerformanceMetrics = () => {
  return performanceOptimizer.getPerformanceMetrics()
}

export const generatePerformanceReport = () => {
  return performanceOptimizer.generatePerformanceReport()
}

// Performance testing utilities
export class PerformanceTester {
  async runPerformanceTest(url: string): Promise<{
    loadTime: number
    firstPaint: number
    domContentLoaded: number
    resourceCount: number
    totalSize: number
  }> {
    const startTime = performance.now()
    
    try {
      // In a real implementation, this would use Puppeteer or similar
      // For now, we'll simulate the test
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      const endTime = performance.now()
      
      return {
        loadTime: endTime - startTime,
        firstPaint: 800,
        domContentLoaded: 1200,
        resourceCount: 15,
        totalSize: 2048000 // 2MB
      }
    } catch (error) {
      throw new Error(`Performance test failed: ${error}`)
    }
  }

  async runLighthouseAudit(url: string): Promise<{
    performance: number
    accessibility: number
    bestPractices: number
    seo: number
    pwa: number
  }> {
    // Mock Lighthouse audit results
    // In production, this would integrate with actual Lighthouse
    return {
      performance: 85,
      accessibility: 92,
      bestPractices: 88,
      seo: 95,
      pwa: 70
    }
  }
}

export const performanceTester = new PerformanceTester()
