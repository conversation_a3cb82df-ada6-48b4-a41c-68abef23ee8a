import { createContext, useContext, useEffect, useState } from 'react'

export interface User {
  id: string
  email: string
  name: string
  createdAt: string
  websites: string[]
}

export interface AuthContextType {
  user: User | null
  isLoading: boolean
  isAuthenticated: boolean
  login: (email: string, password: string) => Promise<{ success: boolean; error?: string }>
  logout: () => Promise<void>
  refreshUser: () => Promise<void>
}

export const AuthContext = createContext<AuthContextType | null>(null)

export function useAuth() {
  const context = useContext(AuthContext)
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider')
  }
  return context
}

// Auth API functions
export const authAPI = {
  async login(email: string, password: string) {
    try {
      const response = await fetch('/api/auth/login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ email, password }),
      })

      const data = await response.json()

      if (!response.ok) {
        return { success: false, error: data.error || 'Login failed' }
      }

      return { success: true, user: data.user, token: data.token }
    } catch (error) {
      console.error('Login error:', error)
      return { success: false, error: 'Network error. Please try again.' }
    }
  },

  async logout() {
    try {
      await fetch('/api/auth/logout', {
        method: 'POST',
      })
    } catch (error) {
      console.error('Logout error:', error)
    }
  },

  async getCurrentUser() {
    try {
      const response = await fetch('/api/auth/me')
      
      if (!response.ok) {
        return { success: false, error: 'Not authenticated' }
      }

      const data = await response.json()
      return { success: true, user: data.user }
    } catch (error) {
      console.error('Get current user error:', error)
      return { success: false, error: 'Network error' }
    }
  },

  async createUser(email: string, password: string, name: string) {
    try {
      const response = await fetch('/api/auth/login', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ email, password, name }),
      })

      const data = await response.json()

      if (!response.ok) {
        return { success: false, error: data.error || 'Registration failed' }
      }

      return { success: true, user: data.user }
    } catch (error) {
      console.error('Registration error:', error)
      return { success: false, error: 'Network error. Please try again.' }
    }
  }
}

// Website management API
export const websiteAPI = {
  async getWebsites() {
    try {
      console.log('📡 Fetching websites from API...')
      const token = storage.getToken()
      const headers: Record<string, string> = {}

      if (token) {
        headers['Authorization'] = `Bearer ${token}`
      }

      const response = await fetch('/api/websites', { headers })

      console.log('📡 API Response status:', response.status, response.statusText)

      if (!response.ok) {
        const errorText = await response.text()
        console.error('❌ API Error response:', errorText)
        throw new Error(`Failed to fetch websites: ${response.status} ${response.statusText}`)
      }

      const data = await response.json()
      console.log('✅ Websites fetched successfully:', {
        count: data.websites?.length || 0,
        websites: data.websites?.map((w: any) => ({ id: w.id, name: w.name })) || []
      })
      return { success: true, websites: data.websites }
    } catch (error) {
      console.error('❌ Get websites error:', error)
      return { success: false, error: 'Failed to load websites' }
    }
  },

  async createWebsite(websiteData: any) {
    try {
      const token = storage.getToken()
      const headers: Record<string, string> = {
        'Content-Type': 'application/json',
      }

      if (token) {
        headers['Authorization'] = `Bearer ${token}`
      }

      const response = await fetch('/api/websites', {
        method: 'POST',
        headers,
        body: JSON.stringify(websiteData),
      })

      const data = await response.json()

      if (!response.ok) {
        return { success: false, error: data.error || 'Failed to create website' }
      }

      return { success: true, website: data.website }
    } catch (error) {
      console.error('Create website error:', error)
      return { success: false, error: 'Network error. Please try again.' }
    }
  },

  async updateWebsite(id: string, updateData: any) {
    try {
      const token = storage.getToken()
      const headers: Record<string, string> = {
        'Content-Type': 'application/json',
      }

      if (token) {
        headers['Authorization'] = `Bearer ${token}`
      }

      const response = await fetch('/api/websites', {
        method: 'PUT',
        headers,
        body: JSON.stringify({ id, ...updateData }),
      })

      const data = await response.json()

      if (!response.ok) {
        return { success: false, error: data.error || 'Failed to update website' }
      }

      return { success: true, website: data.website }
    } catch (error) {
      console.error('Update website error:', error)
      return { success: false, error: 'Network error. Please try again.' }
    }
  },

  async deleteWebsite(id: string) {
    try {
      const token = storage.getToken()
      const headers: Record<string, string> = {}

      if (token) {
        headers['Authorization'] = `Bearer ${token}`
      }

      const response = await fetch(`/api/websites?id=${id}`, {
        method: 'DELETE',
        headers,
      })

      const data = await response.json()

      if (!response.ok) {
        return { success: false, error: data.error || 'Failed to delete website' }
      }

      return { success: true }
    } catch (error) {
      console.error('Delete website error:', error)
      return { success: false, error: 'Network error. Please try again.' }
    }
  },

  async publishWebsite(id: string) {
    return this.updateWebsite(id, { isPublished: true })
  },

  async unpublishWebsite(id: string) {
    return this.updateWebsite(id, { isPublished: false })
  }
}

// Local storage utilities
export const storage = {
  getToken(): string | null {
    if (typeof window === 'undefined') return null
    return localStorage.getItem('auth-token')
  },

  setToken(token: string): void {
    if (typeof window === 'undefined') return
    localStorage.setItem('auth-token', token)
  },

  removeToken(): void {
    if (typeof window === 'undefined') return
    localStorage.removeItem('auth-token')
  },

  getUser(): User | null {
    if (typeof window === 'undefined') return null
    const userStr = localStorage.getItem('auth-user')
    return userStr ? JSON.parse(userStr) : null
  },

  setUser(user: User): void {
    if (typeof window === 'undefined') return
    localStorage.setItem('auth-user', JSON.stringify(user))
  },

  removeUser(): void {
    if (typeof window === 'undefined') return
    localStorage.removeItem('auth-user')
  },

  clear(): void {
    this.removeToken()
    this.removeUser()
  }
}

// Auth hook implementation
export function useAuthState(): AuthContextType {
  const [user, setUser] = useState<User | null>(null)
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    // Check for existing authentication on mount
    const initAuth = async () => {
      const storedUser = storage.getUser()
      if (storedUser) {
        // Verify the stored user is still valid
        const result = await authAPI.getCurrentUser()
        if (result.success) {
          setUser(result.user)
        } else {
          storage.clear()
        }
      }
      setIsLoading(false)
    }

    initAuth()
  }, [])

  const login = async (email: string, password: string) => {
    setIsLoading(true)
    try {
      const result = await authAPI.login(email, password)
      
      if (result.success && result.user) {
        setUser(result.user)
        storage.setUser(result.user)
        if (result.token) {
          storage.setToken(result.token)
        }
        return { success: true }
      } else {
        return { success: false, error: result.error }
      }
    } finally {
      setIsLoading(false)
    }
  }

  const logout = async () => {
    setIsLoading(true)
    try {
      await authAPI.logout()
      setUser(null)
      storage.clear()
    } finally {
      setIsLoading(false)
    }
  }

  const refreshUser = async () => {
    const result = await authAPI.getCurrentUser()
    if (result.success) {
      setUser(result.user)
      storage.setUser(result.user)
    } else {
      setUser(null)
      storage.clear()
    }
  }

  return {
    user,
    isLoading,
    isAuthenticated: !!user,
    login,
    logout,
    refreshUser
  }
}
