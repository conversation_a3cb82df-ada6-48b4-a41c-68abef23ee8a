import type {
  BusinessProfile,
  CulturalContext,
  KenyanRegion,
  BusinessType,
  ColorScheme,
  GeneratedContent,
  TypographySpec,
  LayoutStructure,
  CulturalElement,
  ResponsiveBreakpoints
} from '@/types'
import { culturalLocalizationService } from './cultural-localization'

/**
 * Design Generation Engine
 * Creates color schemes, layouts, and CSS with Kenyan cultural colors
 * and responsive design rules for mobile-first approach
 */
export class DesignGenerationService {
  private designCache: Map<string, any> = new Map()

  /**
   * Generate complete design specification for a business
   */
  async generateDesignSpec(
    businessProfile: BusinessProfile,
    culturalContext: CulturalContext,
    generatedContent: GeneratedContent
  ): Promise<DesignSpec> {
    const cacheKey = this.generateCacheKey(businessProfile, culturalContext)
    
    if (this.designCache.has(cacheKey)) {
      return this.designCache.get(cacheKey)
    }

    // Get cultural styling from localization service
    const regionalStyle = culturalLocalizationService.getRegionalStyle(culturalContext.region)
    
    // Generate color scheme with Kenyan cultural colors
    const colorScheme = this.generateColorScheme(culturalContext.region, businessProfile.type, regionalStyle)

    // Generate layout based on business type and content
    const layout = this.generateLayoutStructure(businessProfile.type, generatedContent)

    // Generate typography settings
    const typography = this.generateTypographySpec(culturalContext.region, businessProfile.type)

    // Generate cultural elements
    const culturalElements = this.generateCulturalElements(culturalContext.region, regionalStyle)

    // Generate responsive breakpoints
    const responsiveBreakpoints = this.generateResponsiveBreakpoints()

    const designSpec: DesignSpec = {
      colorScheme,
      typography,
      layout,
      culturalElements,
      responsiveBreakpoints,
      culturalMotifs: culturalElements.map(el => el.value),
      metadata: {
        generatedAt: new Date(),
        region: culturalContext.region,
        businessType: businessProfile.type,
        version: '2.0'
      }
    }

    this.designCache.set(cacheKey, designSpec)
    return designSpec
  }

  /**
   * Generate premium color scheme with sophisticated gradients and modern aesthetics
   */
  generateColorScheme(region: KenyanRegion, businessType: BusinessType, regionalStyle: any): ColorScheme {
    const baseColors = {
      primary: regionalStyle.primaryColors[0],
      secondary: regionalStyle.secondaryColors[0],
      accent: regionalStyle.accentColors[0]
    }

    // Business type specific color adjustments with premium gradients
    const businessColorAdjustments = this.getBusinessTypeColorAdjustments(businessType)

    // Generate sophisticated gradient combinations
    const premiumGradients = this.generatePremiumGradients(businessType, baseColors)

    return {
      primary: businessColorAdjustments.primary || baseColors.primary,
      secondary: businessColorAdjustments.secondary || baseColors.secondary,
      accent: businessColorAdjustments.accent || baseColors.accent,
      background: '#ffffff',
      text: '#1f2937',
      muted: '#6b7280',
      success: '#10b981',
      warning: '#f59e0b',
      error: '#ef4444',
      // Premium gradient combinations for modern aesthetics - FIXED FOR CONTRAST
      gradients: {
        hero: `linear-gradient(135deg, ${businessColorAdjustments.primary || baseColors.primary}, ${this.darkenColor(businessColorAdjustments.primary || baseColors.primary, 20)})`,
        accent: `linear-gradient(135deg, ${businessColorAdjustments.accent || baseColors.accent}, ${this.darkenColor(businessColorAdjustments.accent || baseColors.accent, 15)})`,
        subtle: 'linear-gradient(135deg, rgba(255,255,255,0.9), rgba(248,250,252,0.8))',
        dark: 'linear-gradient(135deg, rgba(0,0,0,0.8), rgba(0,0,0,0.6))',
        animated: {
          hero: `linear-gradient(-45deg, ${businessColorAdjustments.primary || baseColors.primary}, ${businessColorAdjustments.secondary || baseColors.secondary}, ${businessColorAdjustments.accent || baseColors.accent}, ${businessColorAdjustments.primary || baseColors.primary})`,
          floating: 'linear-gradient(135deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0.2) 100%)'
        },
        glass: {
          light: 'linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%)',
          dark: 'linear-gradient(135deg, rgba(0, 0, 0, 0.1) 0%, rgba(0, 0, 0, 0.05) 100%)'
        }
      },
      // Glass-morphism colors - FIXED FOR BETTER CONTRAST
      glassMorphism: {
        background: 'rgba(255, 255, 255, 0.85)',
        border: 'rgba(255, 255, 255, 0.3)',
        shadow: 'rgba(31, 38, 135, 0.15)'
      },
      // Advanced color variations
      variations: {
        primaryLight: this.lightenColor(businessColorAdjustments.primary || baseColors.primary, 20),
        primaryDark: this.darkenColor(businessColorAdjustments.primary || baseColors.primary, 20),
        secondaryLight: this.lightenColor(businessColorAdjustments.secondary || baseColors.secondary, 20),
        secondaryDark: this.darkenColor(businessColorAdjustments.secondary || baseColors.secondary, 20)
      }
    }
  }

  /**
   * Generate layout structure based on business type and content
   */
  generateLayoutStructure(businessType: BusinessType, content: GeneratedContent): LayoutStructure {
    return {
      sections: this.generateLayoutSections(businessType, content),
      navigation: this.generateNavigationSpec(businessType),
      footer: this.generateFooterSpec(businessType)
    }
  }

  /**
   * Generate layout configuration based on business type and content (legacy)
   */
  generateLayout(businessType: BusinessType, content: GeneratedContent): LayoutConfig {
    const baseLayout = this.getBaseLayout()
    const businessSpecificLayout = this.getBusinessTypeLayout(businessType)
    
    return {
      ...baseLayout,
      ...businessSpecificLayout,
      sections: this.generateSectionLayout(businessType, content),
      grid: this.generateGridSystem(businessType),
      spacing: this.generateSpacingSystem(),
      components: this.generateComponentLayout(businessType)
    }
  }

  /**
   * Generate premium typography specification with modern aesthetics
   */
  generateTypographySpec(region: KenyanRegion, businessType: BusinessType): TypographySpec {
    const headingFont = this.getRegionalHeadingFont(region)
    const bodyFont = this.getRegionalBodyFont(region)

    return {
      headingFont,
      bodyFont,
      headingSizes: {
        h1: 'clamp(2.5rem, 5vw, 4rem)', // Responsive typography
        h2: 'clamp(2rem, 4vw, 3rem)',
        h3: 'clamp(1.5rem, 3vw, 2.25rem)',
        h4: 'clamp(1.25rem, 2.5vw, 1.75rem)',
        h5: 'clamp(1.125rem, 2vw, 1.5rem)',
        h6: 'clamp(1rem, 1.5vw, 1.25rem)'
      },
      bodySize: 'clamp(1rem, 1.2vw, 1.125rem)',
      lineHeight: '1.6',
      // Premium typography features
      fontWeights: {
        light: '300',
        normal: '400',
        medium: '500',
        semibold: '600',
        bold: '700',
        extrabold: '800'
      },
      letterSpacing: {
        tight: '-0.025em',
        normal: '0',
        wide: '0.025em',
        wider: '0.05em'
      },
      // Modern text effects
      textEffects: {
        gradient: true,
        shadow: true,
        glow: true
      }
    }
  }

  /**
   * Generate typography settings with cultural considerations (legacy)
   */
  generateTypography(region: KenyanRegion, businessType: BusinessType): TypographyConfig {
    // Use default typography with regional considerations
    const headingFont = this.getRegionalHeadingFont(region)
    const bodyFont = this.getRegionalBodyFont(region)

    return {
      fontFamily: {
        heading: headingFont,
        body: bodyFont,
        mono: 'ui-monospace, SFMono-Regular, "SF Mono", Consolas, "Liberation Mono", Menlo, monospace'
      },
      fontSize: {
        xs: '0.75rem',
        sm: '0.875rem',
        base: '1rem',
        lg: '1.125rem',
        xl: '1.25rem',
        '2xl': '1.5rem',
        '3xl': '1.875rem',
        '4xl': '2.25rem',
        '5xl': '3rem',
        '6xl': '3.75rem'
      },
      fontWeight: {
        light: '300',
        normal: '400',
        medium: '500',
        semibold: '600',
        bold: '700',
        extrabold: '800'
      },
      lineHeight: {
        tight: '1.25',
        snug: '1.375',
        normal: '1.5',
        relaxed: '1.625',
        loose: '2'
      },
      letterSpacing: {
        tight: '-0.025em',
        normal: '0em',
        wide: '0.025em'
      },
      emphasis: this.getRegionalEmphasis(region)
    }
  }

  /**
   * Generate responsive design rules for mobile-first approach
   */
  generateResponsiveRules(businessType: BusinessType): ResponsiveRules {
    return {
      breakpoints: {
        sm: '640px',
        md: '768px',
        lg: '1024px',
        xl: '1280px',
        '2xl': '1536px'
      },
      mobileFirst: true,
      touchOptimized: true,
      minTouchTarget: '44px',
      maxContentWidth: '1200px',
      containerPadding: {
        mobile: '1rem',
        tablet: '2rem',
        desktop: '3rem'
      },
      gridColumns: {
        mobile: 1,
        tablet: 2,
        desktop: this.getBusinessTypeColumns(businessType)
      },
      imageOptimization: {
        lazy: true,
        responsive: true,
        formats: ['webp', 'jpg'],
        sizes: {
          mobile: '100vw',
          tablet: '50vw',
          desktop: '33vw'
        }
      }
    }
  }

  /**
   * Generate CSS classes and styles
   */
  generateCSSClasses(colorScheme: ColorScheme, typography: TypographyConfig, layout: LayoutConfig): CSSClasses {
    return {
      // Layout classes
      container: this.generateContainerCSS(layout),
      grid: this.generateGridCSS(layout.grid || this.generateDefaultGrid()),
      section: this.generateSectionCSS(layout.sections || []),
      
      // Typography classes
      heading: this.generateHeadingCSS(typography, colorScheme),
      body: this.generateBodyCSS(typography, colorScheme),
      
      // Component classes
      button: this.generateButtonCSS(colorScheme),
      card: this.generateCardCSS(colorScheme),
      navigation: this.generateNavigationCSS(colorScheme),
      
      // Utility classes
      spacing: this.generateSpacingCSS(layout.spacing || this.generateSpacingSystem()),
      colors: this.generateColorCSS(colorScheme),
      responsive: this.generateResponsiveCSS()
    }
  }

  /**
   * Get business type specific color adjustments
   */
  private getBusinessTypeColorAdjustments(businessType: BusinessType): Partial<ColorScheme> {
    const adjustments: Partial<Record<BusinessType, Partial<ColorScheme>>> = {
      RESTAURANT: {
        primary: '#dc2626', // Warm red for appetite
        accent: '#f59e0b'   // Golden yellow
      },
      SHOP: {
        primary: '#059669', // Trust green
        accent: '#3b82f6'   // Professional blue
      },
      SALON: {
        primary: '#ec4899', // Beauty pink
        accent: '#8b5cf6'   // Elegant purple
      },
      HOTEL: {
        primary: '#0891b2', // Hospitality blue
        accent: '#10b981'   // Welcoming green
      },
      CLINIC: {
        primary: '#0284c7', // Medical blue
        accent: '#10b981'   // Health green
      },
      TECH_SERVICES: {
        primary: '#6366f1', // Tech purple
        accent: '#06b6d4'   // Innovation cyan
      },
      GARAGE: {
        primary: '#374151', // Industrial gray
        accent: '#f59e0b'   // Warning orange
      },
      SCHOOL: {
        primary: '#1d4ed8', // Educational blue
        accent: '#10b981'   // Growth green
      },
      CHURCH: {
        primary: '#7c3aed', // Spiritual purple
        accent: '#fbbf24'   // Divine gold
      },
      CONSULTANCY: {
        primary: '#1f2937', // Professional dark
        accent: '#3b82f6'   // Trust blue
      },
      AGRICULTURE: {
        primary: '#16a34a', // Nature green
        accent: '#eab308'   // Harvest yellow
      },
      TRANSPORT: {
        primary: '#dc2626', // Movement red
        accent: '#1d4ed8'   // Route blue
      },
      CONSTRUCTION: {
        primary: '#ea580c', // Construction orange
        accent: '#374151'   // Steel gray
      },
      ENTERTAINMENT: {
        primary: '#ec4899', // Fun pink
        accent: '#8b5cf6'   // Creative purple
      },
      OTHER: {
        primary: '#6b7280', // Neutral gray
        accent: '#3b82f6'   // Generic blue
      }
    }

    return adjustments[businessType] || {}
  }

  /**
   * Get base layout configuration
   */
  private getBaseLayout(): Partial<LayoutConfig> {
    return {
      maxWidth: '1200px',
      padding: {
        mobile: '1rem',
        tablet: '2rem',
        desktop: '3rem'
      },
      margin: 'auto',
      minHeight: '100vh'
    }
  }

  /**
   * Get business type specific layout
   */
  private getBusinessTypeLayout(businessType: BusinessType): Partial<LayoutConfig> {
    const layouts: Partial<Record<BusinessType, Partial<LayoutConfig>>> = {
      RESTAURANT: {
        headerHeight: '80px',
        heroHeight: '60vh',
        sectionSpacing: '4rem'
      },
      SHOP: {
        headerHeight: '70px',
        heroHeight: '50vh',
        sectionSpacing: '3rem'
      },
      SALON: {
        headerHeight: '75px',
        heroHeight: '55vh',
        sectionSpacing: '3.5rem'
      },
      HOTEL: {
        headerHeight: '85px',
        heroHeight: '70vh',
        sectionSpacing: '4.5rem'
      },
      CLINIC: {
        headerHeight: '70px',
        heroHeight: '45vh',
        sectionSpacing: '3rem'
      },
      TECH_SERVICES: {
        headerHeight: '75px',
        heroHeight: '50vh',
        sectionSpacing: '3.5rem'
      },
      GARAGE: {
        headerHeight: '70px',
        heroHeight: '45vh',
        sectionSpacing: '3rem'
      },
      SCHOOL: {
        headerHeight: '75px',
        heroHeight: '50vh',
        sectionSpacing: '3.5rem'
      },
      CHURCH: {
        headerHeight: '80px',
        heroHeight: '60vh',
        sectionSpacing: '4rem'
      },
      CONSULTANCY: {
        headerHeight: '70px',
        heroHeight: '45vh',
        sectionSpacing: '3rem'
      },
      AGRICULTURE: {
        headerHeight: '75px',
        heroHeight: '55vh',
        sectionSpacing: '3.5rem'
      },
      TRANSPORT: {
        headerHeight: '70px',
        heroHeight: '50vh',
        sectionSpacing: '3rem'
      },
      CONSTRUCTION: {
        headerHeight: '75px',
        heroHeight: '50vh',
        sectionSpacing: '3.5rem'
      },
      ENTERTAINMENT: {
        headerHeight: '80px',
        heroHeight: '65vh',
        sectionSpacing: '4rem'
      },
      OTHER: {
        headerHeight: '70px',
        heroHeight: '50vh',
        sectionSpacing: '3rem'
      }
    }

    return layouts[businessType] || layouts.SHOP || {}
  }

  /**
   * Generate section layout based on content
   */
  private generateSectionLayout(businessType: BusinessType, content: GeneratedContent): SectionLayout[] {
    const baseSections: SectionLayout[] = [
      {
        name: 'header',
        type: 'navigation',
        order: 1,
        fullWidth: true,
        sticky: true
      },
      {
        name: 'hero',
        type: 'hero',
        order: 2,
        fullWidth: true,
        backgroundImage: true
      },
      {
        name: 'about',
        type: 'content',
        order: 3,
        columns: 2,
        padding: 'large'
      },
      {
        name: 'services',
        type: 'grid',
        order: 4,
        columns: this.getServiceColumns(businessType, content.services.length),
        padding: 'large'
      },
      {
        name: 'contact',
        type: 'contact',
        order: 5,
        columns: 2,
        padding: 'large'
      },
      {
        name: 'footer',
        type: 'footer',
        order: 6,
        fullWidth: true,
        backgroundColor: 'muted'
      }
    ]

    return baseSections
  }

  /**
   * Generate grid system
   */
  private generateGridSystem(businessType: BusinessType): GridConfig {
    return {
      columns: 12,
      gap: '1.5rem',
      autoFit: true,
      minColumnWidth: '250px',
      maxColumns: this.getBusinessTypeColumns(businessType)
    }
  }

  /**
   * Generate spacing system
   */
  private generateSpacingSystem(): SpacingConfig {
    return {
      xs: '0.25rem',
      sm: '0.5rem',
      md: '1rem',
      lg: '1.5rem',
      xl: '2rem',
      '2xl': '3rem',
      '3xl': '4rem',
      '4xl': '6rem',
      '5xl': '8rem'
    }
  }

  /**
   * Generate component layout
   */
  private generateComponentLayout(businessType: BusinessType): ComponentLayout {
    return {
      button: {
        height: '44px',
        padding: '0.75rem 1.5rem',
        borderRadius: '0.5rem',
        fontSize: '1rem'
      },
      card: {
        padding: '1.5rem',
        borderRadius: '0.75rem',
        shadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)'
      },
      input: {
        height: '44px',
        padding: '0.75rem',
        borderRadius: '0.5rem',
        fontSize: '1rem'
      },
      navigation: {
        height: this.getBusinessTypeLayout(businessType).headerHeight || '75px',
        padding: '0 1rem'
      }
    }
  }

  /**
   * Get number of columns for business type
   */
  private getBusinessTypeColumns(businessType: BusinessType): number {
    const columns: Partial<Record<BusinessType, number>> = {
      RESTAURANT: 3,
      SHOP: 4,
      SALON: 3,
      HOTEL: 3,
      CLINIC: 2,
      TECH_SERVICES: 3,
      GARAGE: 3,
      SCHOOL: 4,
      CHURCH: 2,
      CONSULTANCY: 3,
      AGRICULTURE: 3,
      TRANSPORT: 3,
      CONSTRUCTION: 3,
      ENTERTAINMENT: 4,
      OTHER: 3
    }

    return columns[businessType] || 3
  }

  /**
   * Get service columns based on business type and service count
   */
  private getServiceColumns(businessType: BusinessType, serviceCount: number): number {
    const maxColumns = this.getBusinessTypeColumns(businessType)
    return Math.min(serviceCount, maxColumns)
  }

  /**
   * Generate CSS for different components
   */
  private generateContainerCSS(layout: LayoutConfig): string {
    return `
      max-width: ${layout.maxWidth};
      margin: ${layout.margin};
      padding: ${layout.padding?.mobile};
      min-height: ${layout.minHeight};
      
      @media (min-width: 768px) {
        padding: ${layout.padding?.tablet};
      }
      
      @media (min-width: 1024px) {
        padding: ${layout.padding?.desktop};
      }
    `
  }

  private generateGridCSS(grid: GridConfig): string {
    return `
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(${grid.minColumnWidth}, 1fr));
      gap: ${grid.gap};
      max-width: 100%;
    `
  }

  private generateSectionCSS(sections: SectionLayout[]): Record<string, string> {
    const css: Record<string, string> = {}
    
    sections.forEach(section => {
      css[section.name] = `
        order: ${section.order};
        ${section.fullWidth ? 'width: 100%;' : ''}
        ${section.sticky ? 'position: sticky; top: 0; z-index: 50;' : ''}
        ${section.columns ? `grid-template-columns: repeat(${section.columns}, 1fr);` : ''}
        ${section.padding ? `padding: var(--spacing-${section.padding});` : ''}
        ${section.backgroundColor ? `background-color: var(--color-${section.backgroundColor});` : ''}
      `
    })
    
    return css
  }

  private generateHeadingCSS(typography: TypographyConfig, colorScheme: ColorScheme): string {
    return `
      font-family: ${typography.fontFamily.heading};
      font-weight: ${typography.fontWeight.bold};
      line-height: ${typography.lineHeight.tight};
      color: ${colorScheme.text};
      margin-bottom: 1rem;
    `
  }

  private generateBodyCSS(typography: TypographyConfig, colorScheme: ColorScheme): string {
    return `
      font-family: ${typography.fontFamily.body};
      font-weight: ${typography.fontWeight.normal};
      line-height: ${typography.lineHeight.normal};
      color: ${colorScheme.text};
      font-size: ${typography.fontSize.base};
    `
  }

  private generateButtonCSS(colorScheme: ColorScheme): string {
    return `
      background-color: ${colorScheme.primary};
      color: white;
      border: none;
      border-radius: 0.5rem;
      padding: 0.75rem 1.5rem;
      font-weight: 600;
      cursor: pointer;
      transition: all 0.2s;
      min-height: 44px;
      
      &:hover {
        background-color: ${this.darkenColor(colorScheme.primary, 10)};
        transform: translateY(-1px);
      }
      
      &:active {
        transform: translateY(0);
      }
    `
  }

  private generateCardCSS(colorScheme: ColorScheme): string {
    return `
      background-color: ${colorScheme.background};
      border-radius: 0.75rem;
      padding: 1.5rem;
      box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
      border: 1px solid ${colorScheme.muted}20;
      transition: all 0.2s;
      
      &:hover {
        box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
        transform: translateY(-2px);
      }
    `
  }

  private generateNavigationCSS(colorScheme: ColorScheme): string {
    return `
      background-color: ${colorScheme.background};
      border-bottom: 1px solid ${colorScheme.muted}20;
      backdrop-filter: blur(10px);
      padding: 0 1rem;
      display: flex;
      align-items: center;
      justify-content: space-between;
    `
  }

  private generateSpacingCSS(spacing: SpacingConfig): Record<string, string> {
    const css: Record<string, string> = {}
    
    Object.entries(spacing).forEach(([key, value]) => {
      css[`spacing-${key}`] = value
    })
    
    return css
  }

  private generateColorCSS(colorScheme: ColorScheme): Record<string, string> {
    return {
      'color-primary': colorScheme.primary,
      'color-secondary': colorScheme.secondary,
      'color-accent': colorScheme.accent,
      'color-background': colorScheme.background,
      'color-text': colorScheme.text,
      'color-muted': colorScheme.muted,
      'color-success': colorScheme.success,
      'color-warning': colorScheme.warning,
      'color-error': colorScheme.error
    }
  }

  private generateResponsiveCSS(): Record<string, string> {
    return {
      'mobile-only': '@media (max-width: 767px)',
      'tablet-up': '@media (min-width: 768px)',
      'desktop-up': '@media (min-width: 1024px)',
      'large-up': '@media (min-width: 1280px)'
    }
  }

  private getRegionalHeadingFont(region: KenyanRegion): string {
    const regionalFonts = {
      COASTAL: 'Inter, system-ui, sans-serif',
      HIGHLAND: 'Roboto, system-ui, sans-serif',
      WESTERN: 'Open Sans, system-ui, sans-serif',
      NORTHERN: 'Lato, system-ui, sans-serif',
      CENTRAL: 'Poppins, system-ui, sans-serif',
      EASTERN: 'Source Sans Pro, system-ui, sans-serif'
    }
    return regionalFonts[region] || 'Inter, system-ui, sans-serif'
  }

  private getRegionalBodyFont(region: KenyanRegion): string {
    const regionalFonts = {
      COASTAL: 'Inter, system-ui, sans-serif',
      HIGHLAND: 'Roboto, system-ui, sans-serif',
      WESTERN: 'Open Sans, system-ui, sans-serif',
      NORTHERN: 'Lato, system-ui, sans-serif',
      CENTRAL: 'Poppins, system-ui, sans-serif',
      EASTERN: 'Source Sans Pro, system-ui, sans-serif'
    }
    return regionalFonts[region] || 'Inter, system-ui, sans-serif'
  }

  private getRegionalEmphasis(region: KenyanRegion): string {
    const regionalEmphasis = {
      COASTAL: 'relaxed',
      HIGHLAND: 'professional',
      WESTERN: 'friendly',
      NORTHERN: 'bold',
      CENTRAL: 'warm',
      EASTERN: 'modern'
    }
    return regionalEmphasis[region] || 'warm'
  }

  private generateDefaultGrid(): GridConfig {
    return {
      columns: 12,
      gap: '1.5rem',
      autoFit: true,
      minColumnWidth: '250px',
      maxColumns: 4
    }
  }

  /**
   * Utility function to darken a color
   */
  private darkenColor(color: string, percent: number): string {
    // Simple color darkening - in production, use a proper color manipulation library
    const num = parseInt(color.replace("#", ""), 16)
    const amt = Math.round(2.55 * percent)
    const R = (num >> 16) - amt
    const G = (num >> 8 & 0x00FF) - amt
    const B = (num & 0x0000FF) - amt
    
    return "#" + (0x1000000 + (R < 255 ? R < 1 ? 0 : R : 255) * 0x10000 +
      (G < 255 ? G < 1 ? 0 : G : 255) * 0x100 +
      (B < 255 ? B < 1 ? 0 : B : 255)).toString(16).slice(1)
  }

  /**
   * Generate premium gradient combinations for modern aesthetics
   */
  private generatePremiumGradients(businessType: BusinessType, baseColors: any): any {
    const gradientSets = {
      restaurant: {
        hero: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
        accent: 'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)',
        subtle: 'linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%)',
        dark: 'linear-gradient(135deg, #434343 0%, #000000 100%)'
      },
      salon: {
        hero: 'linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%)',
        accent: 'linear-gradient(135deg, #a8edea 0%, #fed6e3 100%)',
        subtle: 'linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%)',
        dark: 'linear-gradient(135deg, #2c3e50 0%, #34495e 100%)'
      },
      shop: {
        hero: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
        accent: 'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)',
        subtle: 'linear-gradient(135deg, #a8edea 0%, #fed6e3 100%)',
        dark: 'linear-gradient(135deg, #2c3e50 0%, #4a6741 100%)'
      },
      default: {
        hero: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
        accent: 'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)',
        subtle: 'linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%)',
        dark: 'linear-gradient(135deg, #434343 0%, #000000 100%)'
      }
    }

    const selectedGradients = gradientSets[businessType.toLowerCase() as keyof typeof gradientSets] || gradientSets.default

    return {
      ...selectedGradients,
      // Animated gradients for premium effect
      animated: {
        hero: `linear-gradient(-45deg, ${baseColors.primary}, ${baseColors.secondary}, ${baseColors.accent}, ${baseColors.primary})`,
        floating: 'linear-gradient(135deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0.2) 100%)'
      },
      // Glass-morphism gradients
      glass: {
        light: 'linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%)',
        dark: 'linear-gradient(135deg, rgba(0, 0, 0, 0.1) 0%, rgba(0, 0, 0, 0.05) 100%)'
      }
    }
  }

  /**
   * Lighten a color by a percentage
   */
  private lightenColor(color: string, percent: number): string {
    // Simple color lightening - in production, use a proper color manipulation library
    const hex = color.replace('#', '')
    const num = parseInt(hex, 16)
    const amt = Math.round(2.55 * percent)
    const R = (num >> 16) + amt
    const G = (num >> 8 & 0x00FF) + amt
    const B = (num & 0x0000FF) + amt
    return '#' + (0x1000000 + (R < 255 ? R < 1 ? 0 : R : 255) * 0x10000 +
      (G < 255 ? G < 1 ? 0 : G : 255) * 0x100 +
      (B < 255 ? B < 1 ? 0 : B : 255)).toString(16).slice(1)
  }



  /**
   * Generate cache key
   */
  private generateCacheKey(businessProfile: BusinessProfile, culturalContext: CulturalContext): string {
    return `${businessProfile.type}-${culturalContext.region}-${businessProfile.name}`
  }

  /**
   * Clear design cache
   */
  clearCache(): void {
    this.designCache.clear()
  }

  /**
   * Get cache size
   */
  getCacheSize(): number {
    return this.designCache.size
  }

  /**
   * Generate layout sections for the website
   */
  private generateLayoutSections(businessType: BusinessType, content: GeneratedContent): any[] {
    const baseSections = [
      { type: 'hero', order: 1, visible: true },
      { type: 'about', order: 2, visible: true },
      { type: 'services', order: 3, visible: true },
      { type: 'contact', order: 4, visible: true }
    ]

    // Add business-specific sections
    if (businessType === 'RESTAURANT') {
      baseSections.splice(3, 0, { type: 'menu', order: 3, visible: true })
    } else if (businessType === 'HOTEL') {
      baseSections.splice(3, 0, { type: 'rooms', order: 3, visible: true })
    }

    return baseSections
  }

  /**
   * Generate navigation specification
   */
  private generateNavigationSpec(businessType: BusinessType): any {
    return {
      type: 'horizontal',
      position: 'top',
      sticky: true,
      items: [
        { label: 'Home', href: '#home' },
        { label: 'About', href: '#about' },
        { label: 'Services', href: '#services' },
        { label: 'Contact', href: '#contact' }
      ]
    }
  }

  /**
   * Generate footer specification
   */
  private generateFooterSpec(businessType: BusinessType): any {
    return {
      type: 'simple',
      sections: [
        { type: 'contact', visible: true },
        { type: 'social', visible: true },
        { type: 'copyright', visible: true }
      ]
    }
  }

  /**
   * Generate cultural elements for the design
   */
  private generateCulturalElements(region: KenyanRegion, regionalStyle: any): CulturalElement[] {
    const elements: CulturalElement[] = []

    // Add color elements
    elements.push({
      type: 'COLOR',
      value: regionalStyle.primaryColors[0],
      description: `Primary color representing ${region} region`
    })

    // Add pattern elements
    if (regionalStyle.culturalMotifs && regionalStyle.culturalMotifs.length > 0) {
      elements.push({
        type: 'PATTERN',
        value: regionalStyle.culturalMotifs[0],
        description: `Cultural motif from ${region} region`
      })
    }

    return elements
  }

  /**
   * Generate responsive breakpoints
   */
  private generateResponsiveBreakpoints(): ResponsiveBreakpoints {
    return {
      mobile: '320px',
      tablet: '768px',
      desktop: '1024px',
      largeDesktop: '1440px'
    }
  }
}

// Type definitions for design generation
export interface DesignSpec {
  colorScheme: ColorScheme
  layout: LayoutStructure
  typography: TypographySpec
  culturalElements: CulturalElement[]
  responsiveBreakpoints: ResponsiveBreakpoints
  culturalMotifs: string[]
  metadata: {
    generatedAt: Date
    region: KenyanRegion
    businessType: BusinessType
    version: string
  }
}

export interface LayoutConfig {
  maxWidth?: string
  padding?: {
    mobile: string
    tablet: string
    desktop: string
  }
  margin?: string
  minHeight?: string
  headerHeight?: string
  heroHeight?: string
  sectionSpacing?: string
  sections?: SectionLayout[]
  grid?: GridConfig
  spacing?: SpacingConfig
  components?: ComponentLayout
}

export interface SectionLayout {
  name: string
  type: 'navigation' | 'hero' | 'content' | 'grid' | 'contact' | 'footer'
  order: number
  fullWidth?: boolean
  sticky?: boolean
  columns?: number
  padding?: 'small' | 'medium' | 'large'
  backgroundColor?: string
  backgroundImage?: boolean
}

export interface GridConfig {
  columns: number
  gap: string
  autoFit: boolean
  minColumnWidth: string
  maxColumns: number
}

export interface SpacingConfig {
  xs: string
  sm: string
  md: string
  lg: string
  xl: string
  '2xl': string
  '3xl': string
  '4xl': string
  '5xl': string
}

export interface ComponentLayout {
  button: {
    height: string
    padding: string
    borderRadius: string
    fontSize: string
  }
  card: {
    padding: string
    borderRadius: string
    shadow: string
  }
  input: {
    height: string
    padding: string
    borderRadius: string
    fontSize: string
  }
  navigation: {
    height: string
    padding: string
  }
}

export interface TypographyConfig {
  fontFamily: {
    heading: string
    body: string
    mono: string
  }
  fontSize: {
    xs: string
    sm: string
    base: string
    lg: string
    xl: string
    '2xl': string
    '3xl': string
    '4xl': string
    '5xl': string
    '6xl': string
  }
  fontWeight: {
    light: string
    normal: string
    medium: string
    semibold: string
    bold: string
    extrabold: string
  }
  lineHeight: {
    tight: string
    snug: string
    normal: string
    relaxed: string
    loose: string
  }
  letterSpacing: {
    tight: string
    normal: string
    wide: string
  }
  emphasis: string
}

export interface ResponsiveRules {
  breakpoints: {
    sm: string
    md: string
    lg: string
    xl: string
    '2xl': string
  }
  mobileFirst: boolean
  touchOptimized: boolean
  minTouchTarget: string
  maxContentWidth: string
  containerPadding: {
    mobile: string
    tablet: string
    desktop: string
  }
  gridColumns: {
    mobile: number
    tablet: number
    desktop: number
  }
  imageOptimization: {
    lazy: boolean
    responsive: boolean
    formats: string[]
    sizes: {
      mobile: string
      tablet: string
      desktop: string
    }
  }
}

export interface CSSClasses {
  container: string
  grid: string
  section: Record<string, string>
  heading: string
  body: string
  button: string
  card: string
  navigation: string
  spacing: Record<string, string>
  colors: Record<string, string>
  responsive: Record<string, string>
}

// Export singleton instance
export const designGenerationService = new DesignGenerationService()