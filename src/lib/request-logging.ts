/**
 * Request Logging and Monitoring System
 * Comprehensive logging for API requests and responses
 */

import { NextRequest, NextResponse } from 'next/server'

export interface LogEntry {
  id: string
  timestamp: string
  method: string
  url: string
  path: string
  query: Record<string, string>
  headers: Record<string, string>
  userAgent?: string
  ip?: string
  userId?: string
  requestBody?: any
  responseStatus: number
  responseTime: number
  responseSize: number
  error?: string
  tags: string[]
}

export interface LogConfig {
  includeRequestBody?: boolean
  includeResponseBody?: boolean
  includeHeaders?: boolean
  sensitiveHeaders?: string[]
  maxBodySize?: number // Max size to log in bytes
  tags?: string[]
}

class RequestLogger {
  private logs: LogEntry[] = []
  private maxLogs: number = 10000
  private sensitiveHeaders = [
    'authorization',
    'cookie',
    'x-api-key',
    'x-auth-token'
  ]

  /**
   * Generate unique log ID
   */
  private generateId(): string {
    return `log_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }

  /**
   * Extract client IP address
   */
  private extractIP(request: NextRequest): string {
    const forwarded = request.headers.get('x-forwarded-for')
    const realIp = request.headers.get('x-real-ip')
    const cfConnectingIp = request.headers.get('cf-connecting-ip')
    
    return forwarded?.split(',')[0] || realIp || cfConnectingIp || 'unknown'
  }

  /**
   * Extract user ID from request
   */
  private extractUserId(request: NextRequest): string | undefined {
    try {
      const token = request.cookies.get('auth-token')?.value || 
                    request.headers.get('Authorization')?.replace('Bearer ', '')
      
      if (token) {
        const jwt = require('jsonwebtoken')
        const decoded = jwt.verify(token, process.env.JWT_SECRET || 'fallback-secret-key') as { userId: string }
        return decoded.userId
      }
    } catch {
      // Ignore token parsing errors
    }
    
    return undefined
  }

  /**
   * Sanitize headers by removing sensitive information
   */
  private sanitizeHeaders(headers: Headers, config: LogConfig): Record<string, string> {
    const sanitized: Record<string, string> = {}
    const sensitiveHeaders = [...this.sensitiveHeaders, ...(config.sensitiveHeaders || [])]
    
    headers.forEach((value, key) => {
      if (sensitiveHeaders.includes(key.toLowerCase())) {
        sanitized[key] = '[REDACTED]'
      } else {
        sanitized[key] = value
      }
    })
    
    return sanitized
  }

  /**
   * Sanitize request body
   */
  private sanitizeBody(body: any, maxSize: number = 10000): any {
    if (!body) return undefined
    
    const bodyString = typeof body === 'string' ? body : JSON.stringify(body)
    
    if (bodyString.length > maxSize) {
      return `[TRUNCATED - ${bodyString.length} bytes]`
    }
    
    // Remove sensitive fields
    if (typeof body === 'object') {
      const sanitized = { ...body }
      const sensitiveFields = ['password', 'token', 'secret', 'key', 'auth']
      
      for (const field of sensitiveFields) {
        if (field in sanitized) {
          sanitized[field] = '[REDACTED]'
        }
      }
      
      return sanitized
    }
    
    return body
  }

  /**
   * Log request start
   */
  logRequest(request: NextRequest, config: LogConfig = {}): string {
    const logId = this.generateId()
    const url = new URL(request.url)
    
    const logEntry: Partial<LogEntry> = {
      id: logId,
      timestamp: new Date().toISOString(),
      method: request.method,
      url: request.url,
      path: url.pathname,
      query: Object.fromEntries(url.searchParams.entries()),
      userAgent: request.headers.get('user-agent') || undefined,
      ip: this.extractIP(request),
      userId: this.extractUserId(request),
      tags: config.tags || []
    }

    // Include headers if requested
    if (config.includeHeaders) {
      logEntry.headers = this.sanitizeHeaders(request.headers, config)
    }

    // Store partial log entry (will be completed when response is logged)
    this.logs.push(logEntry as LogEntry)
    
    // Cleanup old logs if necessary
    if (this.logs.length > this.maxLogs) {
      this.logs = this.logs.slice(-this.maxLogs)
    }

    return logId
  }

  /**
   * Log request completion
   */
  logResponse(
    logId: string, 
    response: NextResponse, 
    startTime: number,
    config: LogConfig = {},
    error?: string
  ): void {
    const logEntry = this.logs.find(log => log.id === logId)
    if (!logEntry) return

    const responseTime = Date.now() - startTime
    const responseSize = response.headers.get('content-length') 
      ? parseInt(response.headers.get('content-length')!) 
      : 0

    // Complete the log entry
    logEntry.responseStatus = response.status
    logEntry.responseTime = responseTime
    logEntry.responseSize = responseSize
    logEntry.error = error

    // Add performance tags
    if (responseTime > 5000) {
      logEntry.tags.push('slow-response')
    }
    if (response.status >= 400) {
      logEntry.tags.push('error-response')
    }
    if (response.status >= 500) {
      logEntry.tags.push('server-error')
    }
  }

  /**
   * Get logs with filtering
   */
  getLogs(filters: {
    method?: string
    path?: string
    status?: number
    userId?: string
    tags?: string[]
    startTime?: string
    endTime?: string
    limit?: number
  } = {}): LogEntry[] {
    let filteredLogs = [...this.logs]

    // Apply filters
    if (filters.method) {
      filteredLogs = filteredLogs.filter(log => log.method === filters.method)
    }
    
    if (filters.path) {
      filteredLogs = filteredLogs.filter(log => log.path.includes(filters.path!))
    }
    
    if (filters.status) {
      filteredLogs = filteredLogs.filter(log => log.responseStatus === filters.status)
    }
    
    if (filters.userId) {
      filteredLogs = filteredLogs.filter(log => log.userId === filters.userId)
    }
    
    if (filters.tags && filters.tags.length > 0) {
      filteredLogs = filteredLogs.filter(log => 
        filters.tags!.some(tag => log.tags.includes(tag))
      )
    }
    
    if (filters.startTime) {
      filteredLogs = filteredLogs.filter(log => log.timestamp >= filters.startTime!)
    }
    
    if (filters.endTime) {
      filteredLogs = filteredLogs.filter(log => log.timestamp <= filters.endTime!)
    }

    // Sort by timestamp (newest first)
    filteredLogs.sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime())

    // Apply limit
    if (filters.limit) {
      filteredLogs = filteredLogs.slice(0, filters.limit)
    }

    return filteredLogs
  }

  /**
   * Get request statistics
   */
  getStats(timeRange: 'hour' | 'day' | 'week' = 'hour'): {
    totalRequests: number
    successRate: number
    averageResponseTime: number
    errorRate: number
    topEndpoints: Array<{ path: string; count: number }>
    statusCodes: Record<number, number>
    methodDistribution: Record<string, number>
  } {
    const now = new Date()
    const timeRangeMs = {
      hour: 60 * 60 * 1000,
      day: 24 * 60 * 60 * 1000,
      week: 7 * 24 * 60 * 60 * 1000
    }[timeRange]

    const cutoff = new Date(now.getTime() - timeRangeMs)
    const recentLogs = this.logs.filter(log => new Date(log.timestamp) >= cutoff)

    const totalRequests = recentLogs.length
    const successfulRequests = recentLogs.filter(log => log.responseStatus < 400).length
    const successRate = totalRequests > 0 ? successfulRequests / totalRequests : 0
    const errorRate = 1 - successRate

    const totalResponseTime = recentLogs.reduce((sum, log) => sum + log.responseTime, 0)
    const averageResponseTime = totalRequests > 0 ? totalResponseTime / totalRequests : 0

    // Top endpoints
    const endpointCounts: Record<string, number> = {}
    recentLogs.forEach(log => {
      endpointCounts[log.path] = (endpointCounts[log.path] || 0) + 1
    })
    const topEndpoints = Object.entries(endpointCounts)
      .sort(([, a], [, b]) => b - a)
      .slice(0, 10)
      .map(([path, count]) => ({ path, count }))

    // Status code distribution
    const statusCodes: Record<number, number> = {}
    recentLogs.forEach(log => {
      statusCodes[log.responseStatus] = (statusCodes[log.responseStatus] || 0) + 1
    })

    // Method distribution
    const methodDistribution: Record<string, number> = {}
    recentLogs.forEach(log => {
      methodDistribution[log.method] = (methodDistribution[log.method] || 0) + 1
    })

    return {
      totalRequests,
      successRate,
      averageResponseTime,
      errorRate,
      topEndpoints,
      statusCodes,
      methodDistribution
    }
  }

  /**
   * Clear logs
   */
  clearLogs(): void {
    this.logs = []
  }

  /**
   * Export logs
   */
  exportLogs(format: 'json' | 'csv' = 'json'): string {
    if (format === 'csv') {
      const headers = [
        'timestamp', 'method', 'path', 'status', 'responseTime', 'ip', 'userId', 'error'
      ]
      
      const csvRows = [
        headers.join(','),
        ...this.logs.map(log => [
          log.timestamp,
          log.method,
          log.path,
          log.responseStatus,
          log.responseTime,
          log.ip || '',
          log.userId || '',
          log.error || ''
        ].join(','))
      ]
      
      return csvRows.join('\n')
    }
    
    return JSON.stringify(this.logs, null, 2)
  }
}

// Singleton logger instance
const requestLogger = new RequestLogger()

/**
 * Request logging middleware
 */
export function createLoggingMiddleware(config: LogConfig = {}) {
  return async (request: NextRequest): Promise<{ logId: string; startTime: number }> => {
    const logId = requestLogger.logRequest(request, config)
    const startTime = Date.now()
    
    return { logId, startTime }
  }
}

/**
 * Log response completion
 */
export function logResponse(
  logId: string,
  response: NextResponse,
  startTime: number,
  config: LogConfig = {},
  error?: string
): void {
  requestLogger.logResponse(logId, response, startTime, config, error)
}

/**
 * Get request logs
 */
export function getLogs(filters?: any) {
  return requestLogger.getLogs(filters)
}

/**
 * Get request statistics
 */
export function getRequestStats(timeRange?: 'hour' | 'day' | 'week') {
  return requestLogger.getStats(timeRange)
}

/**
 * Export logs
 */
export function exportLogs(format?: 'json' | 'csv') {
  return requestLogger.exportLogs(format)
}

/**
 * Clear all logs
 */
export function clearLogs() {
  requestLogger.clearLogs()
}

export { requestLogger }
