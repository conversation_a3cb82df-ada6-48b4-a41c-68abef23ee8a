import { 
  createUser, 
  getUserByEmail, 
  createWebsite,
  getCulturalTemplate,
  checkDatabaseConnection,
  cleanupDatabase
} from '../database'
import type { BusinessProfile, GeneratedContent, DesignSpec } from '@/types'

// Mock Prisma for testing
jest.mock('../db', () => ({
  prisma: {
    user: {
      create: jest.fn(),
      findUnique: jest.fn(),
    },
    website: {
      create: jest.fn(),
      findUnique: jest.fn(),
    },
    culturalTemplate: {
      findFirst: jest.fn(),
      deleteMany: jest.fn(),
    },
    $queryRaw: jest.fn(),
    generationAnalytics: {
      deleteMany: jest.fn(),
    },
  }
}))

const mockPrisma = require('../db').prisma

describe('Database Operations', () => {
  beforeEach(() => {
    jest.clearAllMocks()
  })

  describe('User Operations', () => {
    describe('createUser', () => {
      it('should create a user with default starter subscription', async () => {
        const mockUser = {
          id: 'user-1',
          email: '<EMAIL>',
          subscriptionTier: 'starter',
          createdAt: new Date(),
          updatedAt: new Date()
        }

        mockPrisma.user.create.mockResolvedValue(mockUser)

        const result = await createUser('<EMAIL>')

        expect(mockPrisma.user.create).toHaveBeenCalledWith({
          data: {
            email: '<EMAIL>',
            subscriptionTier: 'starter'
          }
        })
        expect(result).toEqual(mockUser)
      })

      it('should create a user with premium subscription', async () => {
        const mockUser = {
          id: 'user-1',
          email: '<EMAIL>',
          subscriptionTier: 'premium',
          createdAt: new Date(),
          updatedAt: new Date()
        }

        mockPrisma.user.create.mockResolvedValue(mockUser)

        const result = await createUser('<EMAIL>', 'premium')

        expect(mockPrisma.user.create).toHaveBeenCalledWith({
          data: {
            email: '<EMAIL>',
            subscriptionTier: 'premium'
          }
        })
        expect(result).toEqual(mockUser)
      })
    })

    describe('getUserByEmail', () => {
      it('should get user by email with websites', async () => {
        const mockUser = {
          id: 'user-1',
          email: '<EMAIL>',
          subscriptionTier: 'starter',
          websites: [],
          createdAt: new Date(),
          updatedAt: new Date()
        }

        mockPrisma.user.findUnique.mockResolvedValue(mockUser)

        const result = await getUserByEmail('<EMAIL>')

        expect(mockPrisma.user.findUnique).toHaveBeenCalledWith({
          where: { email: '<EMAIL>' },
          include: {
            websites: {
              orderBy: { createdAt: 'desc' }
            }
          }
        })
        expect(result).toEqual(mockUser)
      })

      it('should return null for non-existent user', async () => {
        mockPrisma.user.findUnique.mockResolvedValue(null)

        const result = await getUserByEmail('<EMAIL>')

        expect(result).toBeNull()
      })
    })
  })

  describe('Website Operations', () => {
    describe('createWebsite', () => {
      it('should create a website with all required data', async () => {
        const mockBusinessProfile: BusinessProfile = {
          name: 'Test Salon',
          type: 'SALON',
          location: {
            county: 'Nairobi',
            area: 'Westlands',
            region: 'HIGHLAND'
          },
          services: ['Hair styling'],
          description: 'Professional salon services',
          targetAudience: 'Women aged 18-45',
          contactInfo: {
            phone: '+254712345678',
            address: 'Westlands, Nairobi'
          },
          culturalContext: {
            region: 'HIGHLAND',
            language: 'ENGLISH',
            businessPractices: {
              paymentMethods: [{
                type: 'MPESA',
                displayName: 'M-Pesa',
                isActive: true
              }],
              communicationPreferences: ['WhatsApp'],
              businessHours: 'Mon-Sat: 8AM-7PM',
              culturalConsiderations: []
            },
            culturalElements: []
          }
        }

        const mockGeneratedContent: GeneratedContent = {
          headline: 'Welcome to Test Salon',
          subheadline: 'Professional beauty services',
          aboutSection: 'We provide quality salon services',
          services: [{
            name: 'Hair Styling',
            description: 'Professional hair styling services'
          }],
          callToActions: [{
            text: 'Book Now',
            type: 'PRIMARY',
            action: 'whatsapp:+254712345678'
          }],
          contactSection: {
            title: 'Contact Us',
            description: 'Get in touch',
            methods: [{
              type: 'WHATSAPP',
              value: '+254712345678',
              label: 'WhatsApp',
              icon: 'whatsapp'
            }]
          }
        }

        const mockDesignSpec: DesignSpec = {
          colorScheme: {
            primary: '#6B46C1',
            secondary: '#EC4899',
            accent: '#3B82F6',
            background: '#FFFFFF',
            text: '#000000',
            muted: '#64748B'
          },
          typography: {
            headingFont: 'Inter',
            bodyFont: 'Inter',
            headingSizes: {
              h1: '3rem',
              h2: '2rem',
              h3: '1.5rem'
            },
            bodySize: '1rem',
            lineHeight: '1.5'
          },
          layout: {
            sections: [{
              id: 'hero',
              type: 'HERO',
              order: 0,
              content: {},
              styling: {
                backgroundColor: '#FFFFFF',
                textColor: '#000000',
                padding: '2rem',
                margin: '0',
                alignment: 'CENTER'
              }
            }],
            navigation: {
              type: 'HORIZONTAL',
              items: [{
                label: 'Home',
                href: '#home'
              }],
              styling: {
                backgroundColor: '#FFFFFF',
                textColor: '#000000',
                hoverColor: '#6B46C1',
                position: 'FIXED'
              }
            },
            footer: {
              content: {
                businessInfo: 'Professional salon services',
                contactInfo: {
                  phone: '+254712345678',
                  address: 'Westlands, Nairobi'
                },
                copyright: '© 2024 Test Salon'
              },
              styling: {
                backgroundColor: '#000000',
                textColor: '#FFFFFF',
                layout: 'SIMPLE'
              }
            }
          },
          culturalElements: [],
          responsiveBreakpoints: {
            mobile: '768px',
            tablet: '1024px',
            desktop: '1280px',
            largeDesktop: '1536px'
          }
        }

        const websiteData = {
          userId: 'user-1',
          name: 'Test Salon Website',
          businessProfile: mockBusinessProfile,
          generatedContent: mockGeneratedContent,
          designSpec: mockDesignSpec,
          htmlOutput: '<html>Test</html>',
          cssOutput: 'body { margin: 0; }',
          subdomain: 'test-salon-abc123'
        }

        const mockWebsite = {
          id: 'website-1',
          ...websiteData,
          isPublished: false,
          createdAt: new Date(),
          updatedAt: new Date()
        }

        mockPrisma.website.create.mockResolvedValue(mockWebsite)

        const result = await createWebsite(websiteData)

        expect(mockPrisma.website.create).toHaveBeenCalledWith({
          data: {
            ...websiteData,
            businessProfile: mockBusinessProfile,
            generatedContent: mockGeneratedContent,
            designSpec: mockDesignSpec
          }
        })
        expect(result).toEqual(mockWebsite)
      })
    })
  })

  describe('Cultural Template Operations', () => {
    describe('getCulturalTemplate', () => {
      it('should get cultural template by region and business type', async () => {
        const mockTemplate = {
          id: 'template-1',
          region: 'HIGHLAND',
          businessType: 'SALON',
          templateData: {
            colorScheme: {
              primary: '#6B46C1'
            }
          },
          createdAt: new Date()
        }

        mockPrisma.culturalTemplate.findFirst.mockResolvedValue(mockTemplate)

        const result = await getCulturalTemplate('HIGHLAND', 'SALON')

        expect(mockPrisma.culturalTemplate.findFirst).toHaveBeenCalledWith({
          where: {
            region: 'HIGHLAND',
            businessType: 'SALON'
          }
        })
        expect(result).toEqual(mockTemplate)
      })

      it('should return null if no template found', async () => {
        mockPrisma.culturalTemplate.findFirst.mockResolvedValue(null)

        const result = await getCulturalTemplate('HIGHLAND', 'SALON')

        expect(result).toBeNull()
      })
    })
  })

  describe('Database Health Check', () => {
    it('should return healthy status when database is accessible', async () => {
      mockPrisma.$queryRaw.mockResolvedValue([{ '?column?': 1 }])

      const result = await checkDatabaseConnection()

      expect(result).toEqual({
        status: 'healthy',
        message: 'Database connection successful'
      })
    })

    it('should return unhealthy status when database is not accessible', async () => {
      const error = new Error('Connection failed')
      mockPrisma.$queryRaw.mockRejectedValue(error)

      const result = await checkDatabaseConnection()

      expect(result).toEqual({
        status: 'unhealthy',
        message: 'Database connection failed: Connection failed'
      })
    })
  })
})