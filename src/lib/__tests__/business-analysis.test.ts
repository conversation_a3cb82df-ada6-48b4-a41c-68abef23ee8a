import { BusinessAnalysisEngine } from '../business-analysis'
import type { BusinessProfile } from '@/types'

// Mock OpenAI
jest.mock('openai', () => {
  return jest.fn().mockImplementation(() => ({
    chat: {
      completions: {
        create: jest.fn()
      }
    }
  }))
})

// Mock database functions
jest.mock('../database', () => ({
  getCulturalTemplate: jest.fn()
}))

const mockGetCulturalTemplate = require('../database').getCulturalTemplate

describe('BusinessAnalysisEngine', () => {
  let engine: BusinessAnalysisEngine
  let mockOpenAI: any

  beforeEach(() => {
    engine = new BusinessAnalysisEngine()
    const OpenAI = require('openai')
    mockOpenAI = new OpenAI()
    jest.clearAllMocks()
  })

  describe('extractEntities', () => {
    it('should extract salon business type from description', () => {
      const input = 'I run a salon in Westlands offering hair and nail services'
      const entities = engine.extractEntities(input)

      expect(entities.businessType).toBe('SALON')
      expect(entities.location?.area).toBe('Westlands')
      expect(entities.location?.county).toBe('Nairobi')
      expect(entities.location?.region).toBe('HIGHLAND')
      expect(entities.services).toContain('Hair')
      expect(entities.services).toContain('Nails')
    })

    it('should extract restaurant business type from description', () => {
      const input = 'We have a restaurant in Mombasa serving local food'
      const entities = engine.extractEntities(input)

      expect(entities.businessType).toBe('RESTAURANT')
      expect(entities.location?.area).toBe('Mombasa')
      expect(entities.location?.county).toBe('Mombasa')
      expect(entities.location?.region).toBe('COASTAL')
      expect(entities.services).toContain('Food')
    })

    it('should extract tech services business type', () => {
      const input = 'I provide computer and IT services in Nairobi'
      const entities = engine.extractEntities(input)

      expect(entities.businessType).toBe('TECH_SERVICES')
      expect(entities.location?.area).toBe('Nairobi')
      expect(entities.services).toContain('Service')
    })

    it('should extract phone number from description', () => {
      const input = 'Call us at +254712345678 for appointments'
      const entities = engine.extractEntities(input)

      expect(entities.contactPhone).toBe('+254712345678')
    })

    it('should extract email from description', () => {
      const input = 'Contact <NAME_EMAIL> for more information'
      const entities = engine.extractEntities(input)

      expect(entities.contactEmail).toBe('<EMAIL>')
    })

    it('should handle multiple locations and pick the first one', () => {
      const input = 'We operate in Kisumu and Eldoret providing transport services'
      const entities = engine.extractEntities(input)

      expect(entities.businessType).toBe('TRANSPORT')
      expect(entities.location?.area).toBe('Kisumu')
      expect(entities.location?.region).toBe('WESTERN')
    })

    it('should provide default services when none detected', () => {
      const input = 'My business in Karen'
      const entities = engine.extractEntities(input)

      expect(entities.services).toEqual(['General services'])
    })

    it('should handle Swahili location names', () => {
      const input = 'Duka langu liko Kibera'
      const entities = engine.extractEntities(input)

      expect(entities.location?.area).toBe('Kibera')
      expect(entities.location?.county).toBe('Nairobi')
    })
  })

  describe('validateCompleteness', () => {
    const createMockProfile = (overrides: Partial<BusinessProfile> = {}): BusinessProfile => ({
      name: 'Test Business',
      type: 'SALON',
      location: {
        county: 'Nairobi',
        area: 'Westlands',
        region: 'HIGHLAND'
      },
      services: ['Hair styling'],
      description: 'Professional salon services',
      targetAudience: 'Local customers',
      contactInfo: {
        phone: '+254712345678',
        address: 'Westlands, Nairobi'
      },
      culturalContext: {
        region: 'HIGHLAND',
        language: 'ENGLISH',
        businessPractices: {
          paymentMethods: [{
            type: 'MPESA',
            displayName: 'M-Pesa',
            isActive: true
          }],
          communicationPreferences: ['WhatsApp'],
          businessHours: 'Mon-Sat: 8AM-6PM',
          culturalConsiderations: []
        },
        culturalElements: []
      },
      ...overrides
    })

    it('should validate complete business profile', () => {
      const profile = createMockProfile()
      const result = engine.validateCompleteness(profile)

      expect(result.isComplete).toBe(true)
      expect(result.missingFields).toHaveLength(0)
      expect(result.suggestions).toHaveLength(0)
    })

    it('should identify missing business name', () => {
      const profile = createMockProfile({ name: '' })
      const result = engine.validateCompleteness(profile)

      expect(result.isComplete).toBe(false)
      expect(result.missingFields).toContain('businessName')
      expect(result.suggestions).toContain('What is the name of your business?')
    })

    it('should identify missing business type', () => {
      const profile = createMockProfile({ type: 'OTHER' })
      const result = engine.validateCompleteness(profile)

      expect(result.isComplete).toBe(false)
      expect(result.missingFields).toContain('businessType')
      expect(result.suggestions).toContain('What type of business do you run?')
    })

    it('should identify missing location', () => {
      const profile = createMockProfile({ 
        location: { county: '', area: '', region: 'HIGHLAND' }
      })
      const result = engine.validateCompleteness(profile)

      expect(result.isComplete).toBe(false)
      expect(result.missingFields).toContain('location')
      expect(result.suggestions).toContain('Where is your business located?')
    })

    it('should identify missing services', () => {
      const profile = createMockProfile({ services: [] })
      const result = engine.validateCompleteness(profile)

      expect(result.isComplete).toBe(false)
      expect(result.missingFields).toContain('services')
      expect(result.suggestions).toContain('What services do you offer?')
    })

    it('should identify missing phone number', () => {
      const profile = createMockProfile({
        contactInfo: {
          phone: '+254700000000', // Default placeholder
          address: 'Westlands, Nairobi'
        }
      })
      const result = engine.validateCompleteness(profile)

      expect(result.isComplete).toBe(false)
      expect(result.missingFields).toContain('contactPhone')
      expect(result.suggestions).toContain('What is your business phone number?')
    })

    it('should handle multiple missing fields', () => {
      const profile = createMockProfile({
        name: '',
        type: 'OTHER',
        services: [],
        contactInfo: {
          phone: '+254700000000',
          address: 'Unknown'
        }
      })
      const result = engine.validateCompleteness(profile)

      expect(result.isComplete).toBe(false)
      expect(result.missingFields).toHaveLength(4)
      expect(result.suggestions).toHaveLength(4)
    })
  })

  describe('analyzeInput', () => {
    beforeEach(() => {
      // Mock successful cultural template response
      mockGetCulturalTemplate.mockResolvedValue({
        id: 'template-1',
        region: 'HIGHLAND',
        businessType: 'SALON',
        templateData: {
          businessPractices: {
            paymentMethods: [
              { type: 'MPESA', displayName: 'M-Pesa', isActive: true },
              { type: 'CASH', displayName: 'Cash', isActive: true }
            ],
            communicationPreferences: ['WhatsApp', 'Phone calls'],
            businessHours: 'Mon-Sat: 8AM-7PM',
            culturalConsiderations: ['Bridal packages available']
          },
          culturalElements: [
            {
              type: 'LANGUAGE',
              value: 'Salon ya urembo',
              description: 'Beauty salon in Swahili'
            }
          ]
        }
      })

      // Mock OpenAI response
      mockOpenAI.chat.completions.create.mockResolvedValue({
        choices: [{
          message: {
            content: JSON.stringify({
              businessName: 'Mama Njeri\'s Salon',
              businessType: 'SALON',
              location: {
                county: 'Nairobi',
                area: 'Westlands',
                region: 'HIGHLAND'
              },
              services: ['Hair styling', 'Manicure', 'Pedicure'],
              targetAudience: 'Women aged 18-45 in Westlands area',
              enhancedDescription: 'Professional beauty salon offering quality hair and nail services to women in Nairobi'
            })
          }
        }]
      })
    })

    it('should analyze complete business input successfully', async () => {
      const input = 'I run a salon in Westlands called Mama Njeri\'s Salon. We do hair and nails. Call +254712345678'
      
      const result = await engine.analyzeInput(input)

      // Since we're in test mode without OpenAI, it should use fallback logic
      expect(result.name).toBe('Westlands Salon') // Fallback name generation
      expect(result.type).toBe('SALON')
      expect(result.location.area).toBe('Westlands')
      expect(result.location.county).toBe('Nairobi')
      expect(result.location.region).toBe('HIGHLAND')
      expect(result.services).toEqual(['Hair', 'Nails']) // Extracted services from input
      expect(result.contactInfo.phone).toBe('+254712345678')
      expect(result.contactInfo.whatsapp).toBe('+254712345678')
      expect(result.culturalContext.region).toBe('HIGHLAND')
    })

    it('should handle OpenAI API failure gracefully', async () => {
      mockOpenAI.chat.completions.create.mockRejectedValue(new Error('API Error'))
      
      const input = 'I run a salon in Westlands'
      
      const result = await engine.analyzeInput(input)

      // Should fallback to enhanced extraction
      expect(result.name).toBe('Westlands Salon')
      expect(result.type).toBe('SALON')
      expect(result.location.area).toBe('Westlands')
    })

    it('should handle missing cultural template gracefully', async () => {
      mockGetCulturalTemplate.mockResolvedValue(null)
      
      const input = 'I run a salon in Westlands'
      
      const result = await engine.analyzeInput(input)

      // Should use default business practices
      expect(result.culturalContext.businessPractices.paymentMethods).toEqual([
        { type: 'MPESA', displayName: 'M-Pesa', isActive: true },
        { type: 'CASH', displayName: 'Cash Payment', isActive: true }
      ])
    })

    it('should handle invalid JSON from OpenAI', async () => {
      mockOpenAI.chat.completions.create.mockResolvedValue({
        choices: [{
          message: {
            content: 'Invalid JSON response'
          }
        }]
      })
      
      const input = 'I run a salon in Westlands'
      
      const result = await engine.analyzeInput(input)

      // Should fallback to basic extraction
      expect(result.type).toBe('SALON')
      expect(result.location.area).toBe('Westlands')
    })
  })

  describe('enhanceWithoutAI (fallback functionality)', () => {
    it('should generate appropriate business name from type and location', () => {
      const entities = {
        businessType: 'SALON' as const,
        location: {
          county: 'Nairobi',
          area: 'Westlands',
          region: 'HIGHLAND' as const
        },
        services: ['General services'],
        description: 'I run a salon in Westlands'
      }

      // Access private method through any casting for testing
      const result = (engine as any).enhanceWithoutAI('I run a salon in Westlands', entities)

      expect(result.businessName).toBe('Westlands Salon')
      expect(result.businessType).toBe('SALON')
      expect(result.location.area).toBe('Westlands')
    })

    it('should enhance services based on business type', () => {
      const entities = {
        businessType: 'RESTAURANT' as const,
        location: {
          county: 'Mombasa',
          area: 'Mombasa',
          region: 'COASTAL' as const
        },
        services: ['General services'],
        description: 'Restaurant in Mombasa'
      }

      const result = (engine as any).enhanceWithoutAI('Restaurant in Mombasa', entities)

      expect(result.services).toEqual(['Local dishes', 'Beverages', 'Takeaway'])
      expect(result.businessName).toBe('Mombasa Restaurant')
    })

    it('should handle tech services business type', () => {
      const entities = {
        businessType: 'TECH_SERVICES' as const,
        location: {
          county: 'Nairobi',
          area: 'Nairobi',
          region: 'HIGHLAND' as const
        },
        services: ['General services'],
        description: 'Tech services in Nairobi'
      }

      const result = (engine as any).enhanceWithoutAI('Tech services in Nairobi', entities)

      expect(result.services).toEqual(['Computer repair', 'IT support', 'Software services'])
      expect(result.businessName).toBe('Nairobi Tech Services')
    })

    it('should provide fallback defaults when no business type or location', () => {
      const entities = {
        services: ['General services'],
        description: 'My business'
      }

      const result = (engine as any).enhanceWithoutAI('My business', entities)

      expect(result.businessName).toBe('My Business')
      expect(result.businessType).toBe('OTHER')
      expect(result.location.county).toBe('Nairobi')
      expect(result.location.area).toBe('CBD')
      expect(result.location.region).toBe('HIGHLAND')
    })

    it('should preserve existing services if not generic', () => {
      const entities = {
        businessType: 'SALON' as const,
        location: {
          county: 'Nairobi',
          area: 'Westlands',
          region: 'HIGHLAND' as const
        },
        services: ['Hair styling', 'Manicure'],
        description: 'Salon with hair and nails'
      }

      const result = (engine as any).enhanceWithoutAI('Salon with hair and nails', entities)

      expect(result.services).toEqual(['Hair styling', 'Manicure'])
    })
  })

  describe('extractEntities - Additional Edge Cases', () => {
    it('should extract multiple business types and pick the first one', () => {
      const input = 'I run a salon and restaurant in Westlands'
      const entities = engine.extractEntities(input)

      // Since we sort by keyword length and check in order, "restaurant" (longer) comes before "salon"
      expect(entities.businessType).toBe('RESTAURANT') // Longer keyword match
    })

    it('should handle Kenyan phone number formats', () => {
      const testCases = [
        { input: 'Call 0712345678', expected: '0712345678' },
        { input: 'WhatsApp +254712345678', expected: '+254712345678' },
        { input: 'Phone: 254712345678', expected: '254712345678' }
      ]

      testCases.forEach(({ input, expected }) => {
        const entities = engine.extractEntities(input)
        expect(entities.contactPhone).toBe(expected)
      })
    })

    it('should extract multiple services from description', () => {
      const input = 'We offer hair styling, massage, and facial services'
      const entities = engine.extractEntities(input)

      expect(entities.services).toContain('Hair')
      expect(entities.services).toContain('Massage')
      expect(entities.services).toContain('Facial')
    })

    it('should handle coastal region locations', () => {
      const coastalLocations = [
        { input: 'Business in Mombasa', expected: { area: 'Mombasa', county: 'Mombasa', region: 'COASTAL' } },
        { input: 'Shop in Malindi', expected: { area: 'Malindi', county: 'Kilifi', region: 'COASTAL' } },
        { input: 'Hotel in Diani', expected: { area: 'Diani', county: 'Kwale', region: 'COASTAL' } }
      ]

      coastalLocations.forEach(({ input, expected }) => {
        const entities = engine.extractEntities(input)
        expect(entities.location?.area).toBe(expected.area)
        expect(entities.location?.county).toBe(expected.county)
        expect(entities.location?.region).toBe(expected.region)
      })
    })

    it('should handle western region locations', () => {
      const westernLocations = [
        { input: 'Business in Kisumu', expected: { area: 'Kisumu', county: 'Kisumu', region: 'WESTERN' } },
        { input: 'Shop in Kakamega', expected: { area: 'Kakamega', county: 'Kakamega', region: 'WESTERN' } }
      ]

      westernLocations.forEach(({ input, expected }) => {
        const entities = engine.extractEntities(input)
        expect(entities.location?.area).toBe(expected.area)
        expect(entities.location?.county).toBe(expected.county)
        expect(entities.location?.region).toBe(expected.region)
      })
    })

    it('should handle northern region locations', () => {
      const northernLocations = [
        { input: 'Transport in Eldoret', expected: { area: 'Eldoret', county: 'Uasin Gishu', region: 'NORTHERN' } },
        { input: 'Business in Meru', expected: { area: 'Meru', county: 'Meru', region: 'NORTHERN' } }
      ]

      northernLocations.forEach(({ input, expected }) => {
        const entities = engine.extractEntities(input)
        expect(entities.location?.area).toBe(expected.area)
        expect(entities.location?.county).toBe(expected.county)
        expect(entities.location?.region).toBe(expected.region)
      })
    })

    it('should handle case-insensitive business type detection', () => {
      const testCases = [
        { input: 'SALON in Nairobi', expected: 'SALON' },
        { input: 'Beauty shop in town', expected: 'SALON' },
        { input: 'RESTAURANT serving food', expected: 'RESTAURANT' },
        { input: 'Tech company providing IT services', expected: 'TECH_SERVICES' }
      ]

      testCases.forEach(({ input, expected }) => {
        const entities = engine.extractEntities(input)
        expect(entities.businessType).toBe(expected)
      })
    })

    it('should avoid duplicate services', () => {
      const input = 'We offer hair services, hair styling, and hair cutting'
      const entities = engine.extractEntities(input)

      // Should only have "Hair" once, not multiple hair-related services
      const hairServices = entities.services.filter(service => service.toLowerCase().includes('hair'))
      expect(hairServices.length).toBe(1)
    })
  })

  describe('analyzeInput - Fallback Mode (No OpenAI)', () => {
    beforeEach(() => {
      // Mock no cultural template found
      mockGetCulturalTemplate.mockResolvedValue(null)
      
      // Ensure OpenAI fails to simulate no API key scenario
      mockOpenAI.chat.completions.create.mockRejectedValue(new Error('OpenAI not available'))
    })

    it('should work without OpenAI for salon business', async () => {
      const input = 'I run a salon in Westlands offering hair and nail services'
      
      const result = await engine.analyzeInput(input)

      expect(result.name).toBe('Westlands Salon')
      expect(result.type).toBe('SALON')
      expect(result.location.area).toBe('Westlands')
      expect(result.location.county).toBe('Nairobi')
      expect(result.location.region).toBe('HIGHLAND')
      expect(result.services).toEqual(['Hair', 'Nails', 'Service'])
      expect(result.culturalContext.businessPractices.paymentMethods).toEqual([
        { type: 'MPESA', displayName: 'M-Pesa', isActive: true },
        { type: 'CASH', displayName: 'Cash Payment', isActive: true }
      ])
    })

    it('should work without OpenAI for restaurant business', async () => {
      const input = 'Restaurant in Mombasa serving local food'
      
      const result = await engine.analyzeInput(input)

      expect(result.name).toBe('Mombasa Restaurant')
      expect(result.type).toBe('RESTAURANT')
      expect(result.location.area).toBe('Mombasa')
      expect(result.location.region).toBe('COASTAL')
      expect(result.services).toEqual(['Food'])
    })

    it('should work without OpenAI for tech services', async () => {
      const input = 'Computer repair and IT support in Nairobi'
      
      const result = await engine.analyzeInput(input)

      expect(result.name).toBe('Nairobi Tech Services')
      expect(result.type).toBe('TECH_SERVICES')
      expect(result.services).toEqual(['Repair'])
    })

    it('should preserve contact information in fallback mode', async () => {
      const input = 'Salon in Westlands. Call +254712345678 <NAME_EMAIL>'
      
      const result = await engine.analyzeInput(input)

      expect(result.contactInfo.phone).toBe('+254712345678')
      expect(result.contactInfo.whatsapp).toBe('+254712345678')
      expect(result.contactInfo.email).toBe('<EMAIL>')
    })
  })

  describe('enhanceWithContext', () => {
    it('should return the same profile for now', async () => {
      const mockProfile: BusinessProfile = {
        name: 'Test Business',
        type: 'SALON',
        location: {
          county: 'Nairobi',
          area: 'Westlands',
          region: 'HIGHLAND'
        },
        services: ['Hair styling'],
        description: 'Professional salon',
        targetAudience: 'Local customers',
        contactInfo: {
          phone: '+254712345678',
          address: 'Westlands, Nairobi'
        },
        culturalContext: {
          region: 'HIGHLAND',
          language: 'ENGLISH',
          businessPractices: {
            paymentMethods: [],
            communicationPreferences: [],
            businessHours: '',
            culturalConsiderations: []
          },
          culturalElements: []
        }
      }

      const result = await engine.enhanceWithContext(mockProfile)
      expect(result).toEqual(mockProfile)
    })
  })
})