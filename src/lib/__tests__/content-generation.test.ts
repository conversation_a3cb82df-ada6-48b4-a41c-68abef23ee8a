import { ContentGenerationService, contentGenerationService } from '../content-generation'
import type { BusinessProfile, CulturalContext, KenyanRegion, BusinessType } from '@/types'

// Mock OpenAI
jest.mock('openai', () => {
  return {
    __esModule: true,
    default: jest.fn().mockImplementation(() => ({
      chat: {
        completions: {
          create: jest.fn()
        }
      }
    }))
  }
})

// Mock cultural localization service
jest.mock('../cultural-localization', () => ({
  culturalLocalizationService: {
    getLocalizedBusinessTerminology: jest.fn(),
    getRegionalStyle: jest.fn()
  }
}))

const mockCulturalService = require('../cultural-localization').culturalLocalizationService

describe('ContentGenerationService', () => {
  let service: ContentGenerationService
  let mockBusinessProfile: BusinessProfile
  let mockCulturalContext: CulturalContext

  beforeEach(() => {
    service = new ContentGenerationService()
    jest.clearAllMocks()

    mockBusinessProfile = {
      name: 'Mama Njeri Restaurant',
      type: 'RESTAURANT',
      location: {
        county: 'Nairobi',
        area: 'CBD',
        region: 'CENTRAL'
      },
      services: ['Traditional Kenyan Food', 'Catering', 'Takeaway'],
      description: 'Traditional Kenyan restaurant serving authentic local dishes',
      targetAudience: 'Local food lovers',
      contactInfo: {
        phone: '+254712345678',
        whatsapp: '+254712345678',
        email: '<EMAIL>',
        address: '123 Test Street, Nairobi'
      },
      culturalContext: mockCulturalContext
    }

    mockCulturalContext = {
      region: 'HIGHLAND',
      language: 'ENGLISH',
      businessPractices: {
        paymentMethods: [
          { type: 'MPESA', displayName: 'M-Pesa', isActive: true },
          { type: 'CASH', displayName: 'Cash', isActive: true }
        ],
        communicationPreferences: ['WhatsApp', 'Phone calls'],
        businessHours: 'Mon-Sat: 8AM-6PM',
        culturalConsiderations: ['Professional environment']
      },
      culturalElements: []
    }

    // Setup mocks
    mockCulturalService.getLocalizedBusinessTerminology.mockReturnValue({
      businessType: 'mkahawa',
      greetings: {
        morning: 'Mũrũgamĩrĩre rũciinĩ',
        afternoon: 'Mũrũgamĩrĩre mũthenya',
        evening: 'Mũrũgamĩrĩre hwaĩ-inĩ',
        general: 'Mũrũgamĩrĩre'
      },
      commonPhrases: {
        welcome: 'wega wa gũũka',
        thankYou: 'nĩ ngũgocera',
        qualityService: 'thoguo wa njega'
      }
    })

    mockCulturalService.getRegionalStyle.mockReturnValue({
      primaryColors: ['#16a34a', '#22c55e', '#4ade80'],
      culturalMotifs: ['coffee plants', 'acacia trees', 'kikuyu patterns', 'nairobi skyline']
    })
  })

  describe('Content Generation with OpenAI Integration', () => {
    it('should generate complete website content', async () => {
      const result = await service.generateWebsiteContent(mockBusinessProfile, mockCulturalContext)

      expect(result).toBeDefined()
      expect(result.headline).toBeDefined()
      expect(result.subheadline).toBeDefined()
      expect(result.aboutSection).toBeDefined()
      expect(result.services).toBeDefined()
      expect(result.callToActions).toBeDefined()
      expect(result.contactSection).toBeDefined()
    })

    it('should cache generated content', async () => {
      const result1 = await service.generateWebsiteContent(mockBusinessProfile, mockCulturalContext)
      const result2 = await service.generateWebsiteContent(mockBusinessProfile, mockCulturalContext)

      // Should return same cached instance (check structure instead of object reference due to timing)
      expect(result1.headline).toBe(result2.headline)
      expect(result1.aboutSection).toBe(result2.aboutSection)
      expect(result1.services.length).toBe(result2.services.length)
    })

    it('should clear cache when requested', async () => {
      const result1 = await service.generateWebsiteContent(mockBusinessProfile, mockCulturalContext)
      service.clearCache()
      const result2 = await service.generateWebsiteContent(mockBusinessProfile, mockCulturalContext)

      expect(result1).not.toBe(result2) // Should be different instances after cache clear
    })
  })

  describe('Headlines Generation', () => {
    it('should generate culturally-aware headlines', async () => {
      const headlines = await service.generateHeadlines(mockBusinessProfile, mockCulturalContext)

      expect(headlines).toBeDefined()
      expect(headlines.main).toContain('Mũrũgamĩrĩre') // Should include regional greeting
      expect(headlines.tagline).toContain('mkahawa') // Should include localized business type
      expect(headlines.about).toBeDefined()
      expect(headlines.services).toBeDefined()
      expect(headlines.contact).toBeDefined()
    })

    it('should generate headlines for different regions', async () => {
      const coastalContext = { ...mockCulturalContext, region: 'COASTAL' as KenyanRegion }
      
      mockCulturalService.getLocalizedBusinessTerminology.mockReturnValue({
        businessType: 'mkahawa',
        greetings: { general: 'Hujambo' },
        commonPhrases: { welcome: 'karibu' }
      })

      const headlines = await service.generateHeadlines(mockBusinessProfile, coastalContext)

      expect(headlines.main).toContain('Hujambo') // Should use coastal greeting
    })

    it('should handle different business types', async () => {
      const salonProfile = { ...mockBusinessProfile, businessType: 'SALON' as BusinessType }
      
      mockCulturalService.getLocalizedBusinessTerminology.mockReturnValue({
        businessType: 'saluni',
        greetings: { general: 'Mũrũgamĩrĩre' },
        commonPhrases: { welcome: 'wega wa gũũka' }
      })

      const headlines = await service.generateHeadlines(salonProfile, mockCulturalContext)

      expect(headlines.tagline).toContain('saluni') // Should use salon terminology
    })
  })

  describe('Descriptions Generation', () => {
    it('should generate culturally-appropriate descriptions', async () => {
      const descriptions = await service.generateDescriptions(mockBusinessProfile, mockCulturalContext)

      expect(descriptions).toBeDefined()
      expect(descriptions.hero).toContain('wega wa gũũka') // Should include welcome phrase
      expect(descriptions.about).toContain('mkahawa') // Should include business type
      expect(descriptions.whyChooseUs).toContain('M-Pesa') // Should mention local payment methods
    })

    it('should include business location in descriptions', async () => {
      const descriptions = await service.generateDescriptions(mockBusinessProfile, mockCulturalContext)

      expect(descriptions.hero).toContain('Nairobi')
      expect(descriptions.about).toContain('Nairobi')
    })

    it('should adapt descriptions for different regions', async () => {
      const westernContext = { ...mockCulturalContext, region: 'WESTERN' as KenyanRegion }
      
      mockCulturalService.getLocalizedBusinessTerminology.mockReturnValue({
        businessType: 'mkahawa',
        greetings: { general: 'Oyaore' },
        commonPhrases: { welcome: 'oyaore', qualityService: 'tich maber' }
      })

      const descriptions = await service.generateDescriptions(mockBusinessProfile, westernContext)

      expect(descriptions.hero).toContain('oyaore') // Should use western greeting
    })
  })

  describe('Services Generation', () => {
    it('should generate relevant services for restaurant', async () => {
      const services = await service.generateServices(mockBusinessProfile, mockCulturalContext)

      expect(services).toBeDefined()
      expect(services.length).toBeGreaterThan(0)
      expect(services[0]).toHaveProperty('name')
      expect(services[0]).toHaveProperty('description')
      
      // Should include restaurant-specific services
      const serviceNames = services.map(s => s.name.toLowerCase())
      expect(serviceNames.some(name => 
        name.includes('meal') || name.includes('food') || name.includes('catering')
      )).toBe(true)
    })

    it('should generate services for different business types', async () => {
      const shopProfile = { ...mockBusinessProfile, businessType: 'SHOP' as BusinessType }
      const services = await service.generateServices(shopProfile, mockCulturalContext)

      expect(services).toBeDefined()
      expect(services.length).toBeGreaterThan(0)
      
      // Should include shop-specific services
      const serviceNames = services.map(s => s.name.toLowerCase())
      expect(serviceNames.some(name => 
        name.includes('product') || name.includes('delivery') || name.includes('customer')
      )).toBe(true)
    })

    it('should limit services to reasonable number', async () => {
      const services = await service.generateServices(mockBusinessProfile, mockCulturalContext)

      expect(services.length).toBeLessThanOrEqual(5) // Should not exceed 5 services
      expect(services.length).toBeGreaterThanOrEqual(3) // Should have at least 3 services
    })
  })

  describe('Call-to-Actions Generation', () => {
    it('should generate region-specific call-to-actions', async () => {
      const ctas = service.generateCallToActions(mockBusinessProfile, mockCulturalContext)

      expect(ctas).toBeDefined()
      expect(ctas.primary).toBeDefined()
      expect(ctas.secondary).toBeDefined()
      expect(ctas.contact).toBeDefined()
      expect(ctas.whatsapp).toBeDefined()
      expect(ctas.location).toBeDefined()
    })

    it('should adapt CTAs for different regions', async () => {
      const coastalContext = { ...mockCulturalContext, region: 'COASTAL' as KenyanRegion }
      const westernContext = { ...mockCulturalContext, region: 'WESTERN' as KenyanRegion }

      const coastalCTAs = service.generateCallToActions(mockBusinessProfile, coastalContext)
      const westernCTAs = service.generateCallToActions(mockBusinessProfile, westernContext)

      // CTAs should be different for different regions
      expect(coastalCTAs.primary).not.toBe(westernCTAs.primary)
    })

    it('should customize CTAs for business types', async () => {
      const restaurantCTAs = service.generateCallToActions(mockBusinessProfile, mockCulturalContext)
      
      const hotelProfile = { ...mockBusinessProfile, businessType: 'HOTEL' as BusinessType }
      const hotelCTAs = service.generateCallToActions(hotelProfile, mockCulturalContext)

      // Restaurant and hotel should have different contact CTAs
      expect(restaurantCTAs.contact).not.toBe(hotelCTAs.contact)
    })

    it('should include WhatsApp integration', async () => {
      const ctas = service.generateCallToActions(mockBusinessProfile, mockCulturalContext)

      expect(ctas.whatsapp).toContain('WhatsApp')
    })
  })

  describe('Cultural Awareness', () => {
    it('should integrate with cultural localization service', async () => {
      await service.generateWebsiteContent(mockBusinessProfile, mockCulturalContext)

      expect(mockCulturalService.getLocalizedBusinessTerminology).toHaveBeenCalledWith(
        'HIGHLAND',
        'RESTAURANT'
      )
      expect(mockCulturalService.getRegionalStyle).toHaveBeenCalledWith('HIGHLAND')
    })

    it('should use localized terminology throughout content', async () => {
      const content = await service.generateWebsiteContent(mockBusinessProfile, mockCulturalContext)

      // Check that localized terms appear in generated content
      const allText = JSON.stringify(content).toLowerCase()
      expect(allText).toContain('mkahawa') // Localized business type
      expect(allText).toContain('mũrũgamĩrĩre') // Regional greeting
    })

    it('should adapt content for different cultural contexts', async () => {
      const coastalContext = { ...mockCulturalContext, region: 'COASTAL' as KenyanRegion }
      
      mockCulturalService.getLocalizedBusinessTerminology.mockReturnValue({
        businessType: 'mkahawa',
        greetings: { general: 'Hujambo' },
        commonPhrases: { welcome: 'karibu' }
      })

      const coastalContent = await service.generateWebsiteContent(mockBusinessProfile, coastalContext)
      const highlandContent = await service.generateWebsiteContent(mockBusinessProfile, mockCulturalContext)

      // Content should be different for different regions
      expect(JSON.stringify(coastalContent)).not.toBe(JSON.stringify(highlandContent))
    })
  })

  describe('Template Fallback System', () => {
    it('should provide template-based content when AI is unavailable', async () => {
      // Test without OpenAI
      const serviceWithoutAI = new ContentGenerationService()
      
      const content = await serviceWithoutAI.generateWebsiteContent(mockBusinessProfile, mockCulturalContext)

      expect(content).toBeDefined()
      expect(content.headline).toBeDefined()
      expect(content.subheadline).toBeDefined()
      expect(content.aboutSection).toBeDefined()
      expect(content.services).toBeDefined()
      expect(content.callToActions).toBeDefined()
      expect(content.contactSection).toBeDefined()
    })

    it('should generate consistent template content', async () => {
      const serviceWithoutAI = new ContentGenerationService()
      
      const content1 = await serviceWithoutAI.generateWebsiteContent(mockBusinessProfile, mockCulturalContext)
      const content2 = await serviceWithoutAI.generateWebsiteContent(mockBusinessProfile, mockCulturalContext)

      expect(content1.headline).toBe(content2.headline)
      expect(content1.aboutSection).toBe(content2.aboutSection)
    })
  })

  describe('Performance and Caching', () => {
    it('should check AI availability', () => {
      const isAvailable = service.isAIAvailable()
      expect(typeof isAvailable).toBe('boolean')
    })

    it('should generate cache keys consistently', async () => {
      const content1 = await service.generateWebsiteContent(mockBusinessProfile, mockCulturalContext)
      const content2 = await service.generateWebsiteContent(mockBusinessProfile, mockCulturalContext)

      // Should return same cached content (check structure instead of object reference due to timing)
      expect(content1.headline).toBe(content2.headline)
      expect(content1.aboutSection).toBe(content2.aboutSection)
      expect(content1.services.length).toBe(content2.services.length)
    })

    it('should handle different cache keys for different inputs', async () => {
      const differentProfile = { ...mockBusinessProfile, name: 'Different Restaurant' }
      
      const content1 = await service.generateWebsiteContent(mockBusinessProfile, mockCulturalContext)
      const content2 = await service.generateWebsiteContent(differentProfile, mockCulturalContext)

      expect(content1).not.toBe(content2) // Should be different cached instances
    })
  })

  describe('Error Handling', () => {
    it('should handle missing business information gracefully', async () => {
      const incompleteProfile = {
        name: 'Test Business',
        type: 'SHOP' as BusinessType,
        location: {
          county: 'Nairobi',
          area: 'CBD',
          region: 'CENTRAL' as KenyanRegion
        },
        services: ['General items'],
        description: 'A test shop',
        targetAudience: 'Everyone',
        contactInfo: {
          phone: '+254712345678',
          address: '123 Test Street'
        },
        culturalContext: mockCulturalContext
      }

      const content = await service.generateWebsiteContent(incompleteProfile, mockCulturalContext)

      expect(content).toBeDefined()
      expect(content.headline).toBeDefined()
      expect(content.aboutSection).toBeDefined()
    })

    it('should provide meaningful default content', async () => {
      const minimalProfile = {
        name: 'Test Shop',
        type: 'SHOP' as BusinessType,
        location: {
          county: 'Nairobi',
          area: 'CBD',
          region: 'CENTRAL' as KenyanRegion
        },
        services: ['General items'],
        description: 'A test shop',
        targetAudience: 'Everyone',
        contactInfo: {
          phone: '+254712345678',
          address: '123 Test Street'
        },
        culturalContext: mockCulturalContext
      }

      const content = await service.generateWebsiteContent(minimalProfile, mockCulturalContext)

      expect(content.headline).toBeTruthy()
      expect(content.aboutSection).toBeTruthy()
      expect(content.services.length).toBeGreaterThan(0)
    })
  })

  describe('Singleton Instance', () => {
    it('should export working singleton instance', () => {
      expect(contentGenerationService).toBeDefined()
      expect(typeof contentGenerationService.generateWebsiteContent).toBe('function')
      expect(typeof contentGenerationService.generateHeadlines).toBe('function')
      expect(typeof contentGenerationService.generateDescriptions).toBe('function')
      expect(typeof contentGenerationService.generateServices).toBe('function')
      expect(typeof contentGenerationService.generateCallToActions).toBe('function')
    })
  })
})