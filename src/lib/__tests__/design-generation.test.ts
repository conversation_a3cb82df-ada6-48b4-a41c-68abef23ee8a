import { DesignGenerationService, designGenerationService } from '../design-generation'
import type { BusinessProfile, CulturalContext, GeneratedContent } from '@/types'

// Mock dependencies
jest.mock('../cultural-localization', () => ({
  culturalLocalizationService: {
    getRegionalStyle: jest.fn()
  }
}))

const mockCulturalService = require('../cultural-localization').culturalLocalizationService

describe('DesignGenerationService', () => {
  let service: DesignGenerationService
  let mockBusinessProfile: BusinessProfile
  let mockCulturalContext: CulturalContext
  let mockGeneratedContent: GeneratedContent

  beforeEach(() => {
    service = new DesignGenerationService()
    
    mockBusinessProfile = {
      name: 'Mama Njeri Restaurant',
      type: 'RESTAURANT',
      description: 'Traditional Kenyan restaurant serving authentic local dishes',
      location: {
        county: 'Nairobi',
        town: 'Westlands',
        area: 'Sarit Centre'
      },
      contactInfo: {
        phone: '+254712345678',
        whatsapp: '254712345678',
        email: '<EMAIL>'
      }
    }

    mockCulturalContext = {
      region: 'CENTRAL',
      language: 'SWAHILI',
      businessPractices: {
        businessHours: '8:00 AM - 10:00 PM',
        paymentMethods: ['M-Pesa', 'Cash', 'Card'],
        culturalConsiderations: ['Family-friendly', 'Halal options']
      }
    }

    mockGeneratedContent = {
      headlines: {
        main: 'Authentic Kenyan Cuisine at Mama Njeri',
        tagline: 'Taste the Heart of Kenya',
        about: 'Our Story',
        services: 'Our Menu',
        contact: 'Visit Us Today'
      },
      descriptions: {
        hero: 'Experience the rich flavors of traditional Kenyan cooking',
        about: 'Family-owned restaurant serving authentic dishes since 1995',
        whyChooseUs: 'Fresh ingredients, traditional recipes, warm hospitality'
      },
      services: [
        {
          name: 'Traditional Dishes',
          description: 'Nyama choma, ugali, sukuma wiki and more',
          price: 'From KSh 300'
        },
        {
          name: 'Vegetarian Options',
          description: 'Fresh vegetables and plant-based meals',
          price: 'From KSh 250'
        }
      ],
      callToActions: {
        primary: 'Order Now',
        whatsapp: 'WhatsApp Us',
        phone: 'Call Us'
      }
    }

    // Setup mock for cultural localization service
    mockCulturalService.getRegionalStyle.mockReturnValue({
      primaryColors: ['#d97706', '#ea580c'],
      secondaryColors: ['#059669', '#0d9488'],
      accentColors: ['#dc2626', '#b91c1c'],
      culturalMotifs: ['Maasai patterns', 'Kikuyu symbols'],
      typography: {
        headingFont: 'Inter, sans-serif',
        bodyFont: 'Inter, sans-serif',
        emphasis: 'warm'
      }
    })
  })

  afterEach(() => {
    jest.clearAllMocks()
    service.clearCache()
  })

  describe('generateDesignSpec', () => {
    it('should generate complete design specification', async () => {
      const designSpec = await service.generateDesignSpec(
        mockBusinessProfile,
        mockCulturalContext,
        mockGeneratedContent
      )

      expect(designSpec).toBeDefined()
      expect(designSpec.colorScheme).toBeDefined()
      expect(designSpec.layout).toBeDefined()
      expect(designSpec.typography).toBeDefined()
      expect(designSpec.responsiveRules).toBeDefined()
      expect(designSpec.cssClasses).toBeDefined()
      expect(designSpec.culturalMotifs).toEqual(['Maasai patterns', 'Kikuyu symbols'])
      expect(designSpec.metadata.region).toBe('CENTRAL')
      expect(designSpec.metadata.businessType).toBe('RESTAURANT')
    })

    it('should cache design specifications', async () => {
      const designSpec1 = await service.generateDesignSpec(
        mockBusinessProfile,
        mockCulturalContext,
        mockGeneratedContent
      )
      
      const designSpec2 = await service.generateDesignSpec(
        mockBusinessProfile,
        mockCulturalContext,
        mockGeneratedContent
      )

      expect(designSpec1).toBe(designSpec2) // Same object reference due to caching
      // Note: getRegionalStyle is called twice per generation (once in generateDesignSpec, once in generateTypography)
      // So for cached result, it should still be called only twice total (not four times)
      expect(mockCulturalService.getRegionalStyle).toHaveBeenCalledTimes(2)
    })

    it('should generate different designs for different business types', async () => {
      const shopProfile = { ...mockBusinessProfile, type: 'SHOP' as const }
      
      const restaurantDesign = await service.generateDesignSpec(
        mockBusinessProfile,
        mockCulturalContext,
        mockGeneratedContent
      )
      
      const shopDesign = await service.generateDesignSpec(
        shopProfile,
        mockCulturalContext,
        mockGeneratedContent
      )

      expect(restaurantDesign.colorScheme.primary).not.toBe(shopDesign.colorScheme.primary)
      expect(restaurantDesign.layout.headerHeight).not.toBe(shopDesign.layout.headerHeight)
    })
  })

  describe('generateColorScheme', () => {
    it('should generate color scheme with Kenyan cultural colors', () => {
      const regionalStyle = {
        primaryColors: ['#d97706'],
        secondaryColors: ['#059669'],
        accentColors: ['#dc2626']
      }

      const colorScheme = service.generateColorScheme('CENTRAL', 'RESTAURANT', regionalStyle)

      expect(colorScheme.primary).toBe('#dc2626') // Restaurant specific red
      expect(colorScheme.accent).toBe('#f59e0b') // Restaurant specific golden
      expect(colorScheme.background).toBe('#ffffff')
      expect(colorScheme.text).toBe('#1f2937')
      expect(colorScheme.cultural).toBeDefined()
      expect(colorScheme.cultural.kenyanFlag).toBeDefined()
      expect(colorScheme.cultural.regional).toBeDefined()
    })

    it('should apply business type specific color adjustments', () => {
      const regionalStyle = {
        primaryColors: ['#000000'],
        secondaryColors: ['#000000'],
        accentColors: ['#000000']
      }

      const restaurantColors = service.generateColorScheme('CENTRAL', 'RESTAURANT', regionalStyle)
      const shopColors = service.generateColorScheme('CENTRAL', 'SHOP', regionalStyle)
      const salonColors = service.generateColorScheme('CENTRAL', 'SALON', regionalStyle)

      expect(restaurantColors.primary).toBe('#dc2626') // Warm red
      expect(shopColors.primary).toBe('#059669') // Trust green
      expect(salonColors.primary).toBe('#ec4899') // Beauty pink
    })
  })

  describe('generateLayout', () => {
    it('should generate layout configuration based on business type', () => {
      const layout = service.generateLayout('RESTAURANT', mockGeneratedContent)

      expect(layout.maxWidth).toBe('1200px')
      expect(layout.headerHeight).toBe('80px')
      expect(layout.heroHeight).toBe('60vh')
      expect(layout.sectionSpacing).toBe('4rem')
      expect(layout.sections).toBeDefined()
      expect(layout.grid).toBeDefined()
      expect(layout.spacing).toBeDefined()
    })

    it('should generate appropriate section layout', () => {
      const layout = service.generateLayout('RESTAURANT', mockGeneratedContent)

      expect(layout.sections).toHaveLength(6)
      expect(layout.sections![0].name).toBe('header')
      expect(layout.sections![1].name).toBe('hero')
      expect(layout.sections![2].name).toBe('about')
      expect(layout.sections![3].name).toBe('services')
      expect(layout.sections![4].name).toBe('contact')
      expect(layout.sections![5].name).toBe('footer')
    })

    it('should set appropriate service columns based on business type', () => {
      const restaurantLayout = service.generateLayout('RESTAURANT', mockGeneratedContent)
      const shopLayout = service.generateLayout('SHOP', mockGeneratedContent)

      expect(restaurantLayout.sections![3].columns).toBe(2) // 2 services, max 3 columns for restaurant
      expect(shopLayout.sections![3].columns).toBe(2) // 2 services, max 4 columns for shop
    })
  })

  describe('generateTypography', () => {
    it('should generate typography configuration with cultural considerations', () => {
      const typography = service.generateTypography('CENTRAL', 'RESTAURANT')

      expect(typography.fontFamily.heading).toBe('Inter, sans-serif')
      expect(typography.fontFamily.body).toBe('Inter, sans-serif')
      expect(typography.fontSize.base).toBe('1rem')
      expect(typography.fontWeight.normal).toBe('400')
      expect(typography.lineHeight.normal).toBe('1.5')
      expect(typography.emphasis).toBe('warm')
    })

    it('should include comprehensive font size scale', () => {
      const typography = service.generateTypography('CENTRAL', 'RESTAURANT')

      expect(typography.fontSize).toHaveProperty('xs')
      expect(typography.fontSize).toHaveProperty('sm')
      expect(typography.fontSize).toHaveProperty('base')
      expect(typography.fontSize).toHaveProperty('lg')
      expect(typography.fontSize).toHaveProperty('xl')
      expect(typography.fontSize).toHaveProperty('6xl')
    })
  })

  describe('generateResponsiveRules', () => {
    it('should generate mobile-first responsive rules', () => {
      const rules = service.generateResponsiveRules('RESTAURANT')

      expect(rules.mobileFirst).toBe(true)
      expect(rules.touchOptimized).toBe(true)
      expect(rules.minTouchTarget).toBe('44px')
      expect(rules.maxContentWidth).toBe('1200px')
      expect(rules.breakpoints).toBeDefined()
      expect(rules.containerPadding).toBeDefined()
      expect(rules.gridColumns).toBeDefined()
      expect(rules.imageOptimization).toBeDefined()
    })

    it('should include proper breakpoints', () => {
      const rules = service.generateResponsiveRules('RESTAURANT')

      expect(rules.breakpoints.sm).toBe('640px')
      expect(rules.breakpoints.md).toBe('768px')
      expect(rules.breakpoints.lg).toBe('1024px')
      expect(rules.breakpoints.xl).toBe('1280px')
      expect(rules.breakpoints['2xl']).toBe('1536px')
    })

    it('should configure image optimization', () => {
      const rules = service.generateResponsiveRules('RESTAURANT')

      expect(rules.imageOptimization.lazy).toBe(true)
      expect(rules.imageOptimization.responsive).toBe(true)
      expect(rules.imageOptimization.formats).toContain('webp')
      expect(rules.imageOptimization.formats).toContain('jpg')
    })
  })

  describe('generateCSSClasses', () => {
    it('should generate comprehensive CSS classes', () => {
      const colorScheme = service.generateColorScheme('CENTRAL', 'RESTAURANT', {
        primaryColors: ['#dc2626'],
        secondaryColors: ['#059669'],
        accentColors: ['#f59e0b']
      })
      const typography = service.generateTypography('CENTRAL', 'RESTAURANT')
      const layout = service.generateLayout('RESTAURANT', mockGeneratedContent)

      const cssClasses = service.generateCSSClasses(colorScheme, typography, layout)

      expect(cssClasses.container).toContain('max-width')
      expect(cssClasses.grid).toContain('display: grid')
      expect(cssClasses.heading).toContain('font-family')
      expect(cssClasses.body).toContain('font-family')
      expect(cssClasses.button).toContain('background-color')
      expect(cssClasses.card).toContain('border-radius')
      expect(cssClasses.navigation).toContain('display: flex')
      expect(cssClasses.spacing).toBeDefined()
      expect(cssClasses.colors).toBeDefined()
      expect(cssClasses.responsive).toBeDefined()
    })

    it('should generate proper color CSS variables', () => {
      const colorScheme = service.generateColorScheme('CENTRAL', 'RESTAURANT', {
        primaryColors: ['#dc2626'],
        secondaryColors: ['#059669'],
        accentColors: ['#f59e0b']
      })
      const typography = service.generateTypography('CENTRAL', 'RESTAURANT')
      const layout = service.generateLayout('RESTAURANT', mockGeneratedContent)

      const cssClasses = service.generateCSSClasses(colorScheme, typography, layout)

      expect(cssClasses.colors['color-primary']).toBe('#dc2626')
      expect(cssClasses.colors['color-secondary']).toBe('#059669')
      expect(cssClasses.colors['color-background']).toBe('#ffffff')
      expect(cssClasses.colors['color-text']).toBe('#1f2937')
    })

    it('should generate responsive CSS media queries', () => {
      const colorScheme = service.generateColorScheme('CENTRAL', 'RESTAURANT', {
        primaryColors: ['#dc2626'],
        secondaryColors: ['#059669'],
        accentColors: ['#f59e0b']
      })
      const typography = service.generateTypography('CENTRAL', 'RESTAURANT')
      const layout = service.generateLayout('RESTAURANT', mockGeneratedContent)

      const cssClasses = service.generateCSSClasses(colorScheme, typography, layout)

      expect(cssClasses.responsive['mobile-only']).toBe('@media (max-width: 767px)')
      expect(cssClasses.responsive['tablet-up']).toBe('@media (min-width: 768px)')
      expect(cssClasses.responsive['desktop-up']).toBe('@media (min-width: 1024px)')
    })
  })

  describe('business type specific configurations', () => {
    it('should generate different configurations for each business type', () => {
      const businessTypes = ['RESTAURANT', 'SHOP', 'SALON', 'HOTEL', 'CLINIC', 'TECH_SERVICES'] as const

      businessTypes.forEach(async (businessType) => {
        const profile = { ...mockBusinessProfile, type: businessType }
        const designSpec = await service.generateDesignSpec(profile, mockCulturalContext, mockGeneratedContent)

        expect(designSpec).toBeDefined()
        expect(designSpec.metadata.businessType).toBe(businessType)
      })
    })

    it('should set appropriate grid columns for different business types', () => {
      expect(service['getBusinessTypeColumns']('RESTAURANT')).toBe(3)
      expect(service['getBusinessTypeColumns']('SHOP')).toBe(4)
      expect(service['getBusinessTypeColumns']('SALON')).toBe(3)
      expect(service['getBusinessTypeColumns']('HOTEL')).toBe(3)
      expect(service['getBusinessTypeColumns']('CLINIC')).toBe(2)
      expect(service['getBusinessTypeColumns']('TECH_SERVICES')).toBe(3)
    })
  })

  describe('caching functionality', () => {
    it('should cache design specifications', async () => {
      expect(service.getCacheSize()).toBe(0)

      await service.generateDesignSpec(mockBusinessProfile, mockCulturalContext, mockGeneratedContent)
      expect(service.getCacheSize()).toBe(1)

      // Same parameters should use cache
      await service.generateDesignSpec(mockBusinessProfile, mockCulturalContext, mockGeneratedContent)
      expect(service.getCacheSize()).toBe(1)
    })

    it('should clear cache when requested', async () => {
      await service.generateDesignSpec(mockBusinessProfile, mockCulturalContext, mockGeneratedContent)
      expect(service.getCacheSize()).toBe(1)

      service.clearCache()
      expect(service.getCacheSize()).toBe(0)
    })

    it('should generate different cache keys for different inputs', async () => {
      const profile1 = mockBusinessProfile
      const profile2 = { ...mockBusinessProfile, name: 'Different Restaurant' }

      await service.generateDesignSpec(profile1, mockCulturalContext, mockGeneratedContent)
      await service.generateDesignSpec(profile2, mockCulturalContext, mockGeneratedContent)

      expect(service.getCacheSize()).toBe(2)
    })
  })

  describe('utility functions', () => {
    it('should darken colors correctly', () => {
      const originalColor = '#ff0000' // Red
      const darkenedColor = service['darkenColor'](originalColor, 20)
      
      expect(darkenedColor).toMatch(/^#[0-9a-f]{6}$/i)
      expect(darkenedColor).not.toBe(originalColor)
    })

    it('should generate proper cache keys', () => {
      const cacheKey = service['generateCacheKey'](mockBusinessProfile, mockCulturalContext)
      
      expect(cacheKey).toBe('RESTAURANT-CENTRAL-Mama Njeri Restaurant')
    })
  })

  describe('error handling', () => {
    it('should handle missing regional style gracefully', async () => {
      mockCulturalService.getRegionalStyle.mockReturnValue({
        primaryColors: [],
        secondaryColors: [],
        accentColors: [],
        culturalMotifs: [],
        typography: {
          headingFont: 'Inter, sans-serif',
          bodyFont: 'Inter, sans-serif',
          emphasis: 'neutral'
        }
      })

      const designSpec = await service.generateDesignSpec(
        mockBusinessProfile,
        mockCulturalContext,
        mockGeneratedContent
      )

      expect(designSpec).toBeDefined()
      expect(designSpec.colorScheme.primary).toBeDefined()
    })

    it('should handle empty services array', () => {
      const emptyContent = { ...mockGeneratedContent, services: [] }
      const layout = service.generateLayout('RESTAURANT', emptyContent)

      expect(layout.sections![3].columns).toBe(0)
    })
  })

  describe('singleton instance', () => {
    it('should export a singleton instance', () => {
      expect(designGenerationService).toBeInstanceOf(DesignGenerationService)
    })

    it('should maintain state across calls', async () => {
      await designGenerationService.generateDesignSpec(
        mockBusinessProfile,
        mockCulturalContext,
        mockGeneratedContent
      )

      expect(designGenerationService.getCacheSize()).toBe(1)
    })
  })
})