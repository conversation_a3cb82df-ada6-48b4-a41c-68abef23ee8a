import {
  validateBusinessProfile,
  validateGeneratedContent,
  validateDesignSpec,
  validateCreateWebsiteInput,
  BusinessProfileSchema,
  ContactInfoSchema,
  PaymentMethodSchema,
  CulturalElementSchema
} from '../validations'

describe('Data Model Validations', () => {
  describe('BusinessProfileSchema', () => {
    const validBusinessProfile = {
      name: '<PERSON>\'s Salon',
      type: 'SALON',
      location: {
        county: 'Nairobi',
        area: 'Westlands',
        region: 'HIGHLAND'
      },
      services: ['Hair styling', 'Manicure', 'Pedicure'],
      description: 'Professional beauty salon offering quality hair and nail services',
      targetAudience: 'Women aged 18-45 in Westlands area',
      contactInfo: {
        phone: '+254712345678',
        email: '<EMAIL>',
        address: 'Westlands Shopping Centre, Nairobi'
      },
      culturalContext: {
        region: 'HIGHLAND',
        language: 'ENGLISH',
        businessPractices: {
          paymentMethods: [
            {
              type: 'MPESA',
              displayName: 'M-Pesa',
              isActive: true
            }
          ],
          communicationPreferences: ['WhatsApp'],
          businessHours: 'Mon-Sat: 8AM-7PM',
          culturalConsiderations: ['Bridal packages']
        },
        culturalElements: []
      }
    }

    it('should validate a correct business profile', () => {
      const result = validateBusinessProfile(validBusinessProfile)
      expect(result.success).toBe(true)
    })

    it('should reject business profile with missing required fields', () => {
      const invalidProfile = { ...validBusinessProfile }
      delete invalidProfile.name
      
      const result = validateBusinessProfile(invalidProfile)
      expect(result.success).toBe(false)
      if (!result.success) {
        expect(result.error.issues[0].message).toBe('Required')
      }
    })

    it('should reject business profile with invalid business type', () => {
      const invalidProfile = {
        ...validBusinessProfile,
        type: 'INVALID_TYPE'
      }
      
      const result = validateBusinessProfile(invalidProfile)
      expect(result.success).toBe(false)
    })

    it('should reject business profile with empty services array', () => {
      const invalidProfile = {
        ...validBusinessProfile,
        services: []
      }
      
      const result = validateBusinessProfile(invalidProfile)
      expect(result.success).toBe(false)
      if (!result.success) {
        expect(result.error.issues[0].message).toContain('At least one service required')
      }
    })

    it('should reject business profile with description too short', () => {
      const invalidProfile = {
        ...validBusinessProfile,
        description: 'Short'
      }
      
      const result = validateBusinessProfile(invalidProfile)
      expect(result.success).toBe(false)
      if (!result.success) {
        expect(result.error.issues[0].message).toContain('Description must be at least 10 characters')
      }
    })
  })

  describe('ContactInfoSchema', () => {
    it('should validate correct Kenyan phone number formats', () => {
      const validContact1 = {
        phone: '+254712345678',
        address: 'Westlands, Nairobi'
      }
      
      const validContact2 = {
        phone: '0712345678',
        address: 'Westlands, Nairobi'
      }

      expect(ContactInfoSchema.safeParse(validContact1).success).toBe(true)
      expect(ContactInfoSchema.safeParse(validContact2).success).toBe(true)
    })

    it('should reject invalid phone number formats', () => {
      const invalidContact = {
        phone: '712345678', // Missing country code or leading zero
        address: 'Westlands, Nairobi'
      }
      
      const result = ContactInfoSchema.safeParse(invalidContact)
      expect(result.success).toBe(false)
    })

    it('should validate optional WhatsApp number', () => {
      const contactWithWhatsApp = {
        phone: '+254712345678',
        whatsapp: '+254712345678',
        address: 'Westlands, Nairobi'
      }
      
      const result = ContactInfoSchema.safeParse(contactWithWhatsApp)
      expect(result.success).toBe(true)
    })

    it('should reject invalid email format', () => {
      const invalidContact = {
        phone: '+254712345678',
        email: 'invalid-email',
        address: 'Westlands, Nairobi'
      }
      
      const result = ContactInfoSchema.safeParse(invalidContact)
      expect(result.success).toBe(false)
    })
  })

  describe('PaymentMethodSchema', () => {
    it('should validate M-Pesa payment method', () => {
      const mpesaMethod = {
        type: 'MPESA',
        displayName: 'M-Pesa',
        isActive: true
      }
      
      const result = PaymentMethodSchema.safeParse(mpesaMethod)
      expect(result.success).toBe(true)
    })

    it('should reject invalid payment method type', () => {
      const invalidMethod = {
        type: 'INVALID_PAYMENT',
        displayName: 'Invalid Payment',
        isActive: true
      }
      
      const result = PaymentMethodSchema.safeParse(invalidMethod)
      expect(result.success).toBe(false)
    })

    it('should default isActive to true', () => {
      const methodWithoutActive = {
        type: 'CASH',
        displayName: 'Cash Payment'
      }
      
      const result = PaymentMethodSchema.safeParse(methodWithoutActive)
      expect(result.success).toBe(true)
      if (result.success) {
        expect(result.data.isActive).toBe(true)
      }
    })
  })

  describe('CulturalElementSchema', () => {
    it('should validate cultural color element', () => {
      const colorElement = {
        type: 'COLOR',
        value: '#16a34a',
        description: 'Highland green representing fertile lands'
      }
      
      const result = CulturalElementSchema.safeParse(colorElement)
      expect(result.success).toBe(true)
    })

    it('should validate cultural language element', () => {
      const languageElement = {
        type: 'LANGUAGE',
        value: 'Karibu nyumbani',
        description: 'Welcome home in Swahili'
      }
      
      const result = CulturalElementSchema.safeParse(languageElement)
      expect(result.success).toBe(true)
    })

    it('should reject invalid cultural element type', () => {
      const invalidElement = {
        type: 'INVALID_TYPE',
        value: 'Some value',
        description: 'Some description'
      }
      
      const result = CulturalElementSchema.safeParse(invalidElement)
      expect(result.success).toBe(false)
    })
  })

  describe('GeneratedContent validation', () => {
    const validGeneratedContent = {
      headline: 'Welcome to Mama Njeri\'s Salon',
      subheadline: 'Professional beauty services in the heart of Westlands',
      aboutSection: 'We are a professional beauty salon offering quality hair and nail services to women in Nairobi. Our experienced stylists use the latest techniques and high-quality products.',
      services: [
        {
          name: 'Hair Styling',
          description: 'Professional hair cutting, styling, and treatment services',
          price: 'KES 1,500 - 3,000'
        }
      ],
      callToActions: [
        {
          text: 'Book Appointment',
          type: 'PRIMARY',
          action: 'whatsapp:+254712345678'
        }
      ],
      contactSection: {
        title: 'Get in Touch',
        description: 'Contact us to book your appointment',
        methods: [
          {
            type: 'WHATSAPP',
            value: '+254712345678',
            label: 'WhatsApp',
            icon: 'whatsapp'
          }
        ]
      }
    }

    it('should validate correct generated content', () => {
      const result = validateGeneratedContent(validGeneratedContent)
      expect(result.success).toBe(true)
    })

    it('should reject content with headline too short', () => {
      const invalidContent = {
        ...validGeneratedContent,
        headline: 'Hi'
      }
      
      const result = validateGeneratedContent(invalidContent)
      expect(result.success).toBe(false)
      if (!result.success) {
        expect(result.error.issues[0].message).toContain('Headline must be at least 5 characters')
      }
    })

    it('should reject content with empty services array', () => {
      const invalidContent = {
        ...validGeneratedContent,
        services: []
      }
      
      const result = validateGeneratedContent(invalidContent)
      expect(result.success).toBe(false)
      if (!result.success) {
        expect(result.error.issues[0].message).toContain('At least one service required')
      }
    })
  })

  describe('CreateWebsiteInput validation', () => {
    it('should validate correct website creation input', () => {
      const validInput = {
        businessDescription: 'I run a salon in Westlands offering hair and nail services',
        contactPhone: '+254712345678',
        contactEmail: '<EMAIL>',
        businessLocation: 'Westlands, Nairobi'
      }
      
      const result = validateCreateWebsiteInput(validInput)
      expect(result.success).toBe(true)
    })

    it('should reject input with description too short', () => {
      const invalidInput = {
        businessDescription: 'Salon',
        contactPhone: '+254712345678'
      }
      
      const result = validateCreateWebsiteInput(invalidInput)
      expect(result.success).toBe(false)
      if (!result.success) {
        expect(result.error.issues[0].message).toContain('Business description must be at least 10 characters')
      }
    })

    it('should reject input with invalid email', () => {
      const invalidInput = {
        businessDescription: 'I run a salon in Westlands offering hair and nail services',
        contactEmail: 'invalid-email'
      }
      
      const result = validateCreateWebsiteInput(invalidInput)
      expect(result.success).toBe(false)
    })

    it('should allow optional fields to be missing', () => {
      const minimalInput = {
        businessDescription: 'I run a salon in Westlands offering hair and nail services'
      }
      
      const result = validateCreateWebsiteInput(minimalInput)
      expect(result.success).toBe(true)
    })
  })

  describe('DesignSpec validation', () => {
    const validDesignSpec = {
      colorScheme: {
        primary: '#6B46C1',
        secondary: '#EC4899',
        accent: '#3B82F6',
        background: '#FFFFFF',
        text: '#000000',
        muted: '#64748B'
      },
      typography: {
        headingFont: 'Inter',
        bodyFont: 'Inter',
        headingSizes: {
          h1: '3rem',
          h2: '2rem',
          h3: '1.5rem'
        },
        bodySize: '1rem',
        lineHeight: '1.5'
      },
      layout: {
        sections: [
          {
            id: 'hero',
            type: 'HERO',
            order: 0,
            content: {},
            styling: {
              backgroundColor: '#FFFFFF',
              textColor: '#000000',
              padding: '2rem',
              margin: '0',
              alignment: 'CENTER'
            }
          }
        ],
        navigation: {
          type: 'HORIZONTAL',
          items: [
            {
              label: 'Home',
              href: '#home'
            }
          ],
          styling: {
            backgroundColor: '#FFFFFF',
            textColor: '#000000',
            hoverColor: '#6B46C1',
            position: 'FIXED'
          }
        },
        footer: {
          content: {
            businessInfo: 'Professional salon services',
            contactInfo: {
              phone: '+254712345678',
              address: 'Westlands, Nairobi'
            },
            copyright: '© 2024 Mama Njeri\'s Salon'
          },
          styling: {
            backgroundColor: '#000000',
            textColor: '#FFFFFF',
            layout: 'SIMPLE'
          }
        }
      },
      culturalElements: [],
      responsiveBreakpoints: {
        mobile: '768px',
        tablet: '1024px',
        desktop: '1280px',
        largeDesktop: '1536px'
      }
    }

    it('should validate correct design spec', () => {
      const result = validateDesignSpec(validDesignSpec)
      expect(result.success).toBe(true)
    })

    it('should reject design spec with invalid hex colors', () => {
      const invalidDesignSpec = {
        ...validDesignSpec,
        colorScheme: {
          ...validDesignSpec.colorScheme,
          primary: 'invalid-color'
        }
      }
      
      const result = validateDesignSpec(invalidDesignSpec)
      expect(result.success).toBe(false)
    })

    it('should reject design spec with empty sections array', () => {
      const invalidDesignSpec = {
        ...validDesignSpec,
        layout: {
          ...validDesignSpec.layout,
          sections: []
        }
      }
      
      const result = validateDesignSpec(invalidDesignSpec)
      expect(result.success).toBe(false)
    })
  })
})