import { generateSubdomain, formatKenyanPhone, formatKenyanAddress, getRegionFromCounty } from '../utils'

describe('Utils', () => {
  describe('generateSubdomain', () => {
    it('should generate a valid subdomain from business name', () => {
      const subdomain = generateSubdomain('<PERSON>\'s Salon')
      expect(subdomain).toMatch(/^mama-njeri-s-salon-[a-z0-9]{6}$/)
    })

    it('should handle special characters', () => {
      const subdomain = generateSubdomain('Best Restaurant & Bar!')
      expect(subdomain).toMatch(/^best-restaurant-bar-[a-z0-9]{6}$/)
    })
  })

  describe('formatKenyanPhone', () => {
    it('should format Kenyan phone numbers correctly', () => {
      expect(formatKenyanPhone('0712345678')).toBe('+254712345678')
      expect(formatKenyanPhone('254712345678')).toBe('+254712345678')
      expect(format<PERSON>enyanPhone('712345678')).toBe('+254712345678')
    })
  })

  describe('formatKenyanAddress', () => {
    it('should format Kenyan addresses correctly', () => {
      expect(formatKenyanAddress('Westlands', 'Nairobi')).toBe('Westlands, Nairobi, Kenya')
      expect(formatKenyanAddress('Westlands, Nairobi')).toBe('Westlands, Nairobi, Kenya')
    })
  })

  describe('getRegionFromCounty', () => {
    it('should return correct regions for counties', () => {
      expect(getRegionFromCounty('Nairobi')).toBe('HIGHLAND')
      expect(getRegionFromCounty('Mombasa')).toBe('COASTAL')
      expect(getRegionFromCounty('Kisumu')).toBe('WESTERN')
      expect(getRegionFromCounty('Turkana')).toBe('NORTHERN')
    })

    it('should return CENTRAL for unknown counties', () => {
      expect(getRegionFromCounty('Unknown County')).toBe('CENTRAL')
    })
  })
})