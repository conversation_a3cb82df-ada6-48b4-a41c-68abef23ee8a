import { CulturalLocalizationService, culturalLocalizationService } from '../cultural-localization'
import type { KenyanRegion, BusinessType, CulturalContext } from '@/types'

// Mock database functions
jest.mock('../database', () => ({
  getCulturalTemplate: jest.fn(),
  getAllCulturalTemplates: jest.fn()
}))

const mockGetCulturalTemplate = require('../database').getCulturalTemplate
const mockGetAllCulturalTemplates = require('../database').getAllCulturalTemplates

describe('CulturalLocalizationService', () => {
  let service: CulturalLocalizationService

  beforeEach(() => {
    service = culturalLocalizationService
    jest.clearAllMocks()
  })

  describe('Kenyan Business Terminology and Local Language Support', () => {
    it('should provide localized business terminology for coastal restaurant', () => {
      const result = service.getLocalizedBusinessTerminology('COASTAL', 'RESTAURANT')
      
      expect(result.businessType).toBe('mkahawa') // Swahili for restaurant
      expect(result.greetings.general).toBe('Hujambo')
      expect(result.commonPhrases.welcome).toBe('karibu')
    })

    it('should provide localized business terminology for highland salon', () => {
      const result = service.getLocalizedBusinessTerminology('HIGHLAND', 'SALON')
      
      expect(result.businessType).toBe('handũ ha gũthondeka') // Kikuyu for salon
      expect(result.greetings.general).toBe('Mũrũgamĩrĩre')
      expect(result.commonPhrases.welcome).toBe('wega wa gũũka')
    })

    it('should provide localized business terminology for western shop', () => {
      const result = service.getLocalizedBusinessTerminology('WESTERN', 'SHOP')
      
      expect(result.businessType).toBe('duka') // Luo for shop
      expect(result.greetings.general).toBe('Oyaore')
      expect(result.commonPhrases.welcome).toBe('oyaore')
    })

    it('should provide localized business terminology for eastern clinic', () => {
      const result = service.getLocalizedBusinessTerminology('EASTERN', 'CLINIC')
      
      expect(result.businessType).toBe('kiliniki') // Kamba for clinic
      expect(result.greetings.general).toBe('Mwaisye')
      expect(result.commonPhrases.welcome).toBe('mwaisye')
    })

    it('should localize content with Swahili elements', () => {
      const context: CulturalContext = {
        region: 'COASTAL',
        language: 'SWAHILI',
        businessPractices: {
          paymentMethods: [],
          communicationPreferences: [],
          businessHours: '',
          culturalConsiderations: []
        },
        culturalElements: []
      }
      
      const content = 'Welcome to our quality restaurant service'
      const result = service.localizeContent(content, context)
      
      expect(result).toContain('Welcome (Karibu)')
      expect(result).toContain('quality (Ubora)')
      expect(result).toContain('restaurant (Mkahawa)')
      expect(result).toContain('service (Huduma)')
    })

    it('should apply regional terminology for different regions', () => {
      const coastalContext: CulturalContext = {
        region: 'COASTAL',
        language: 'ENGLISH',
        businessPractices: { paymentMethods: [], communicationPreferences: [], businessHours: '', culturalConsiderations: [] },
        culturalElements: []
      }
      
      const highlandContext: CulturalContext = {
        region: 'HIGHLAND',
        language: 'ENGLISH',
        businessPractices: { paymentMethods: [], communicationPreferences: [], businessHours: '', culturalConsiderations: [] },
        culturalElements: []
      }
      
      const coastalResult = service.localizeContent('Our restaurant provides professional service', coastalContext)
      const highlandResult = service.localizeContent('Our restaurant provides professional service', highlandContext)
      
      expect(coastalResult).toContain('restaurant (mkahawa)')
      expect(coastalResult).toContain('service (huduma)')
      expect(highlandResult).toContain('expert (mtaalamu)')
    })

    it('should add time-aware cultural greetings', () => {
      const context: CulturalContext = {
        region: 'COASTAL',
        language: 'ENGLISH',
        businessPractices: { paymentMethods: [], communicationPreferences: [], businessHours: '', culturalConsiderations: [] },
        culturalElements: []
      }
      
      const content = 'Welcome to our business'
      const result = service.localizeContent(content, context)
      
      expect(result).toMatch(/^(Hujambo|Hujambo asubuhi|Hujambo mchana|Hujambo jioni)!/)
    })

    it('should apply local business terminology based on region', () => {
      const coastalContext: CulturalContext = {
        region: 'COASTAL',
        language: 'ENGLISH',
        businessPractices: { paymentMethods: [], communicationPreferences: [], businessHours: '', culturalConsiderations: [] },
        culturalElements: []
      }
      
      const content = 'Our restaurant and shop provide excellent service'
      const result = service.localizeContent(content, coastalContext)
      
      expect(result).toContain('restaurant (mkahawa)')
      expect(result).toContain('shop (duka)')
    })
  })

  describe('Regional Style Configuration', () => {
    it('should return coastal style configuration', () => {
      const coastalStyle = service.getRegionalStyle('COASTAL')
      
      expect(coastalStyle.primaryColors).toContain('#0891b2')
      expect(coastalStyle.culturalMotifs).toContain('dhow boats')
    })

    it('should return highland style configuration', () => {
      const highlandStyle = service.getRegionalStyle('HIGHLAND')
      
      expect(highlandStyle.primaryColors).toContain('#16a34a')
      expect(highlandStyle.culturalMotifs).toContain('coffee plants')
    })

    it('should cache regional styles', () => {
      const style1 = service.getRegionalStyle('COASTAL')
      const style2 = service.getRegionalStyle('COASTAL')
      
      expect(style1).toBe(style2) // Should return same object reference (cached)
    })
  })

  describe('Cultural Elements Integration', () => {
    it('should apply cultural elements with localized terminology', async () => {
      mockGetCulturalTemplate.mockResolvedValue(null)

      const baseDesign = {
        colorScheme: {
          primary: '#000000',
          secondary: '#ffffff',
          accent: '#cccccc',
          background: '#ffffff',
          text: '#000000',
          muted: '#666666'
        }
      }

      const result = await service.applyCulturalElements('COASTAL', 'RESTAURANT', baseDesign)

      expect(result.colorScheme.primary).toBe('#0891b2') // Coastal primary color
      expect(result.localizedTerminology).toBeDefined()
      expect(result.localizedTerminology.businessType).toBe('mkahawa') // Swahili for restaurant
      expect(result.regionalTheme.name).toBe('Coastal Breeze')
    })
  })

  describe('Business Practices', () => {
    it('should return region-specific default practices', async () => {
      mockGetCulturalTemplate.mockResolvedValue(null)

      const coastalPractices = await service.getLocalBusinessPractices('COASTAL', 'SHOP')
      const highlandPractices = await service.getLocalBusinessPractices('HIGHLAND', 'SHOP')

      // Coastal should include Airtel Money
      expect(coastalPractices.paymentMethods).toContain(
        expect.objectContaining({ type: 'AIRTEL_MONEY' })
      )

      // Highland should include Card payments
      expect(highlandPractices.paymentMethods).toContain(
        expect.objectContaining({ type: 'CARD' })
      )
    })
  })

  describe('Singleton Instance', () => {
    it('should export working singleton instance', () => {
      expect(culturalLocalizationService).toBeDefined()
      expect(typeof culturalLocalizationService.getRegionalStyle).toBe('function')
      expect(typeof culturalLocalizationService.localizeContent).toBe('function')
    })
  })
})