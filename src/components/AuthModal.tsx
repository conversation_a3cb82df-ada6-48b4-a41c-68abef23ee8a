'use client'

import { useState } from 'react'
import { X, Mail, Lock, User, Eye, EyeOff, AlertCircle } from 'lucide-react'
import { useAuth, authAPI } from '@/lib/auth'

interface AuthModalProps {
  isOpen: boolean
  onClose: () => void
  initialMode?: 'login' | 'signup'
  language?: 'en' | 'sw'
}

export default function AuthModal({ 
  isOpen, 
  onClose, 
  initialMode = 'login',
  language = 'en' 
}: AuthModalProps) {
  const [mode, setMode] = useState<'login' | 'signup'>(initialMode)
  const [email, setEmail] = useState('')
  const [password, setPassword] = useState('')
  const [name, setName] = useState('')
  const [showPassword, setShowPassword] = useState(false)
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState('')

  const { login } = useAuth()

  if (!isOpen) return null

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setError('')
    setIsLoading(true)

    try {
      if (mode === 'login') {
        const result = await login(email, password)
        if (result.success) {
          onClose()
          // Reset form
          setEmail('')
          setPassword('')
          setName('')
        } else {
          setError(result.error || 'Login failed')
        }
      } else {
        // Signup
        const result = await authAPI.createUser(email, password, name)
        if (result.success) {
          // Auto-login after successful signup
          const loginResult = await login(email, password)
          if (loginResult.success) {
            onClose()
            // Reset form
            setEmail('')
            setPassword('')
            setName('')
          } else {
            setError('Account created but login failed. Please try logging in.')
          }
        } else {
          setError(result.error || 'Registration failed')
        }
      }
    } finally {
      setIsLoading(false)
    }
  }

  const toggleMode = () => {
    setMode(mode === 'login' ? 'signup' : 'login')
    setError('')
  }

  const texts = {
    en: {
      login: 'Sign In',
      signup: 'Create Account',
      email: 'Email Address',
      password: 'Password',
      name: 'Full Name',
      loginButton: 'Sign In',
      signupButton: 'Create Account',
      switchToSignup: "Don't have an account? Sign up",
      switchToLogin: 'Already have an account? Sign in',
      emailPlaceholder: 'Enter your email',
      passwordPlaceholder: 'Enter your password',
      namePlaceholder: 'Enter your full name',
      loginTitle: 'Welcome back to PagesLab',
      signupTitle: 'Join PagesLab today',
      loginSubtitle: 'Sign in to manage your websites and create new ones',
      signupSubtitle: 'Create beautiful Kenyan business websites with AI'
    },
    sw: {
      login: 'Ingia',
      signup: 'Tengeneza Akaunti',
      email: 'Barua Pepe',
      password: 'Nywila',
      name: 'Jina Kamili',
      loginButton: 'Ingia',
      signupButton: 'Tengeneza Akaunti',
      switchToSignup: 'Huna akaunti? Jiunge',
      switchToLogin: 'Una akaunti? Ingia',
      emailPlaceholder: 'Ingiza barua pepe yako',
      passwordPlaceholder: 'Ingiza nywila yako',
      namePlaceholder: 'Ingiza jina lako kamili',
      loginTitle: 'Karibu tena PagesLab',
      signupTitle: 'Jiunge na PagesLab leo',
      loginSubtitle: 'Ingia kusimamia tovuti zako na kutengeneza mpya',
      signupSubtitle: 'Tengeneza tovuti nzuri za biashara za Kikenyeji kwa AI'
    }
  }

  const t = texts[language]

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center p-4 bg-black bg-opacity-50">
      <div className="bg-white rounded-2xl shadow-2xl w-full max-w-md overflow-hidden">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <h2 className="text-2xl font-bold text-gray-900">
            {mode === 'login' ? t.loginTitle : t.signupTitle}
          </h2>
          <button
            onClick={onClose}
            className="p-2 text-gray-400 hover:text-gray-600 rounded-lg hover:bg-gray-100"
          >
            <X className="w-5 h-5" />
          </button>
        </div>

        {/* Content */}
        <div className="p-6">
          <p className="text-gray-600 mb-6">
            {mode === 'login' ? t.loginSubtitle : t.signupSubtitle}
          </p>

          {error && (
            <div className="mb-4 p-3 bg-red-50 border border-red-200 rounded-lg flex items-start space-x-2">
              <AlertCircle className="w-5 h-5 text-red-500 flex-shrink-0 mt-0.5" />
              <p className="text-sm text-red-700">{error}</p>
            </div>
          )}

          <form onSubmit={handleSubmit} className="space-y-4">
            {mode === 'signup' && (
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  {t.name}
                </label>
                <div className="relative">
                  <User className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
                  <input
                    type="text"
                    value={name}
                    onChange={(e) => setName(e.target.value)}
                    placeholder={t.namePlaceholder}
                    className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    required
                  />
                </div>
              </div>
            )}

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                {t.email}
              </label>
              <div className="relative">
                <Mail className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
                <input
                  type="email"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  placeholder={t.emailPlaceholder}
                  className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  required
                />
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                {t.password}
              </label>
              <div className="relative">
                <Lock className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
                <input
                  type={showPassword ? 'text' : 'password'}
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  placeholder={t.passwordPlaceholder}
                  className="w-full pl-10 pr-12 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  required
                  minLength={6}
                />
                <button
                  type="button"
                  onClick={() => setShowPassword(!showPassword)}
                  className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
                >
                  {showPassword ? <EyeOff className="w-5 h-5" /> : <Eye className="w-5 h-5" />}
                </button>
              </div>
            </div>

            <button
              type="submit"
              disabled={isLoading}
              className="w-full bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white font-semibold py-3 px-4 rounded-lg transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {isLoading ? (
                <div className="flex items-center justify-center space-x-2">
                  <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white"></div>
                  <span>Loading...</span>
                </div>
              ) : (
                mode === 'login' ? t.loginButton : t.signupButton
              )}
            </button>
          </form>

          <div className="mt-6 text-center">
            <button
              onClick={toggleMode}
              className="text-blue-600 hover:text-blue-700 font-medium"
            >
              {mode === 'login' ? t.switchToSignup : t.switchToLogin}
            </button>
          </div>

          {/* Demo credentials */}
          {mode === 'login' && (
            <div className="mt-4 p-3 bg-blue-50 border border-blue-200 rounded-lg">
              <p className="text-sm text-blue-800 font-medium mb-1">Demo Account:</p>
              <p className="text-xs text-blue-700">Email: <EMAIL></p>
              <p className="text-xs text-blue-700">Password: password</p>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}
