'use client'

import { useState, useEffect } from 'react'
import { 
  Edit3, 
  Image as ImageIcon, 
  Palette, 
  Type, 
  Layout, 
  Smartphone, 
  Eye, 
  Save, 
  Undo2, 
  Redo2, 
  Download, 
  Share2,
  Settings,
  RefreshCw,
  CheckCircle,
  AlertCircle,
  AlertTriangle,
  Sparkles,
  MessageCircle,
  Camera,
  Globe
} from 'lucide-react'
import ImageManager from './ImageManager'
import SecureIframeRenderer from './SecureIframeRenderer'
import ProductionEditor from './ProductionEditor'

interface EditingDashboardProps {
  websiteData: any
  onSave: (updatedData: any) => void
  onClose: () => void
  language?: 'en' | 'sw'
}

export default function EditingDashboard({
  websiteData,
  onSave,
  onClose,
  language = 'en'
}: EditingDashboardProps) {
  const [activeTab, setActiveTab] = useState<'chat' | 'production' | 'images' | 'style' | 'layout' | 'preview'>('production')
  const [editInstructions, setEditInstructions] = useState('')
  const [isEditing, setIsEditing] = useState(false)
  const [showDestructiveWarning, setShowDestructiveWarning] = useState(false)
  const [editHistory, setEditHistory] = useState<any[]>([websiteData])
  const [historyIndex, setHistoryIndex] = useState(0)
  const [previewMode, setPreviewMode] = useState<'desktop' | 'tablet' | 'mobile'>('desktop')
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false)
  const [lastSaved, setLastSaved] = useState<Date | null>(null)

  // Debug websiteData structure
  useEffect(() => {
    console.log('🔍 EditingDashboard: websiteData structure:', {
      hasWebsiteData: !!websiteData,
      hasWebsite: !!websiteData?.website,
      hasHtml: !!websiteData?.website?.html,
      htmlLength: websiteData?.website?.html?.length || 0,
      htmlPreview: websiteData?.website?.html?.substring(0, 100) || 'No HTML',
      dataKeys: websiteData ? Object.keys(websiteData) : [],
      websiteKeys: websiteData?.website ? Object.keys(websiteData.website) : []
    })
  }, [websiteData])

  // Check for potentially destructive instructions
  const checkDestructiveInstructions = (instructions: string): boolean => {
    const destructiveKeywords = [
      'delete everything', 'remove all', 'start over', 'completely new',
      'replace entire', 'delete website', 'remove website', 'clear all',
      'start fresh', 'rebuild', 'recreate', 'delete all content'
    ]

    const lowerInstructions = instructions.toLowerCase()
    return destructiveKeywords.some(keyword => lowerInstructions.includes(keyword))
  }

  // Monitor edit instructions for destructive patterns
  useEffect(() => {
    if (editInstructions.trim()) {
      setShowDestructiveWarning(checkDestructiveInstructions(editInstructions))
    } else {
      setShowDestructiveWarning(false)
    }
  }, [editInstructions])

  // Auto-save functionality
  useEffect(() => {
    if (hasUnsavedChanges) {
      const autoSaveTimer = setTimeout(() => {
        handleSave()
      }, 30000) // Auto-save after 30 seconds of inactivity

      return () => clearTimeout(autoSaveTimer)
    }
  }, [hasUnsavedChanges])

  const handleChatEdit = async () => {
    if (!editInstructions.trim()) return

    // Create backup before editing
    const backupData = JSON.parse(JSON.stringify(websiteData))

    setIsEditing(true)
    try {
      console.log('🔄 Starting chat edit with instruction:', editInstructions)
      console.log('📄 Current website HTML length:', websiteData?.website?.html?.length || 0)

      const response = await fetch('/api/edit-website-chat', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          currentWebsite: websiteData?.website?.html || '',
          editInstructions: editInstructions,
          conversationHistory: websiteData.conversationHistory || []
        })
      })

      if (!response.ok) {
        const errorText = await response.text()
        throw new Error(`Edit request failed: ${response.status} - ${errorText}`)
      }

      const result = await response.json()

      if (result.success) {
        console.log('✅ Edit successful, new HTML length:', result.data.website.html.length)

        const updatedData = {
          ...websiteData,
          website: result.data.website,
          conversationHistory: result.data.conversationHistory || [],
          lastEdit: result.data.editSummary
        }

        // Add to history
        const newHistory = editHistory.slice(0, historyIndex + 1)
        newHistory.push(updatedData)
        setEditHistory(newHistory)
        setHistoryIndex(newHistory.length - 1)

        setHasUnsavedChanges(true)
        setEditInstructions('')

        // Update parent component
        onSave(updatedData)

        // Show success message with option to revert
        console.log('🎉 Edit applied successfully. Use undo button if needed.')
      } else {
        throw new Error(result.error || 'Edit failed')
      }
    } catch (error) {
      console.error('❌ Edit failed:', error)

      // Show user-friendly error message
      alert(`Edit failed: ${error instanceof Error ? error.message : 'Unknown error'}. Your website has not been changed.`)

      // Ensure we're still on the backup data
      if (backupData) {
        onSave(backupData)
      }
    } finally {
      setIsEditing(false)
    }
  }

  const handleImageReplace = async (imageId: string, newImageUrl: string) => {
    if (!websiteData?.website?.html) {
      console.error('No website HTML available for image replacement')
      return
    }

    // Implementation similar to the main page
    const parser = new DOMParser()
    const doc = parser.parseFromString(websiteData.website.html, 'text/html')
    const images = doc.querySelectorAll('img')

    const imageIndex = parseInt(imageId.replace('img-', ''))
    if (images[imageIndex]) {
      images[imageIndex].src = newImageUrl

      const updatedHtml = doc.documentElement.outerHTML
      const updatedData = {
        ...websiteData,
        website: {
          ...websiteData.website,
          html: updatedHtml
        }
      }

      // Add to history
      const newHistory = editHistory.slice(0, historyIndex + 1)
      newHistory.push(updatedData)
      setEditHistory(newHistory)
      setHistoryIndex(newHistory.length - 1)

      setHasUnsavedChanges(true)
      onSave(updatedData)
    }
  }

  const handleImageRemove = async (imageId: string) => {
    const parser = new DOMParser()
    const doc = parser.parseFromString(websiteData.website.html, 'text/html')
    const images = doc.querySelectorAll('img')
    
    const imageIndex = parseInt(imageId.replace('img-', ''))
    if (images[imageIndex]) {
      images[imageIndex].remove()
      
      const updatedHtml = doc.documentElement.outerHTML
      const updatedData = {
        ...websiteData,
        website: {
          ...websiteData.website,
          html: updatedHtml
        }
      }

      // Add to history
      const newHistory = editHistory.slice(0, historyIndex + 1)
      newHistory.push(updatedData)
      setEditHistory(newHistory)
      setHistoryIndex(newHistory.length - 1)

      setHasUnsavedChanges(true)
      onSave(updatedData)
    }
  }

  const handleUndo = () => {
    if (historyIndex > 0) {
      setHistoryIndex(historyIndex - 1)
      onSave(editHistory[historyIndex - 1])
      setHasUnsavedChanges(true)
    }
  }

  const handleRedo = () => {
    if (historyIndex < editHistory.length - 1) {
      setHistoryIndex(historyIndex + 1)
      onSave(editHistory[historyIndex + 1])
      setHasUnsavedChanges(true)
    }
  }

  const handleSave = () => {
    setHasUnsavedChanges(false)
    setLastSaved(new Date())
    // In a real app, this would save to the server
    console.log('Website saved successfully')
  }

  const handleDownload = () => {
    const blob = new Blob([websiteData.website.html], { type: 'text/html' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `${websiteData.businessProfile?.name || 'website'}.html`
    a.click()
    URL.revokeObjectURL(url)
  }

  const tabs = [
    { id: 'production', label: language === 'sw' ? 'Mhariri Mahiri' : 'Smart Editor', icon: Settings },
    { id: 'chat', label: language === 'sw' ? 'Mazungumzo' : 'Chat Edit', icon: MessageCircle },
    { id: 'images', label: language === 'sw' ? 'Picha' : 'Images', icon: ImageIcon },
    { id: 'style', label: language === 'sw' ? 'Mtindo' : 'Style', icon: Palette },
    { id: 'layout', label: language === 'sw' ? 'Mpangilio' : 'Layout', icon: Layout },
    { id: 'preview', label: language === 'sw' ? 'Onyesho' : 'Preview', icon: Eye }
  ]

  const quickEditSuggestions = [
    { category: 'Colors', suggestions: ['Make colors more vibrant', 'Use African-inspired colors', 'Change to professional colors'] },
    { category: 'Content', suggestions: ['Add testimonials section', 'Include pricing table', 'Add team section'] },
    { category: 'Layout', suggestions: ['Improve mobile layout', 'Make header bigger', 'Add more spacing'] },
    { category: 'Contact', suggestions: ['Add WhatsApp button', 'Make contact prominent', 'Include social media'] }
  ]

  return (
    <div className="h-full flex flex-col bg-gray-50">
      {/* Header */}
      <div className="bg-white border-b border-gray-200 px-6 py-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <h1 className="text-xl font-semibold text-gray-900">
              {language === 'sw' ? 'Dashibodi ya Kuhariri' : 'Editing Dashboard'}
            </h1>
            <div className="flex items-center space-x-2 text-sm text-gray-500">
              {hasUnsavedChanges && (
                <div className="flex items-center space-x-1 text-orange-600">
                  <AlertCircle className="w-4 h-4" />
                  <span>{language === 'sw' ? 'Mabadiliko hayajahifadhiwa' : 'Unsaved changes'}</span>
                </div>
              )}
              {lastSaved && (
                <div className="flex items-center space-x-1 text-green-600">
                  <CheckCircle className="w-4 h-4" />
                  <span>
                    {language === 'sw' ? 'Imehifadhiwa' : 'Saved'} {lastSaved.toLocaleTimeString()}
                  </span>
                </div>
              )}
            </div>
          </div>

          <div className="flex items-center space-x-3">
            {/* History Controls */}
            <div className="flex items-center space-x-1">
              <button
                onClick={handleUndo}
                disabled={historyIndex <= 0}
                className="p-2 text-gray-500 hover:text-gray-700 disabled:opacity-50 disabled:cursor-not-allowed"
                title={language === 'sw' ? 'Rudisha' : 'Undo'}
              >
                <Undo2 className="w-4 h-4" />
              </button>
              <button
                onClick={handleRedo}
                disabled={historyIndex >= editHistory.length - 1}
                className="p-2 text-gray-500 hover:text-gray-700 disabled:opacity-50 disabled:cursor-not-allowed"
                title={language === 'sw' ? 'Rudia' : 'Redo'}
              >
                <Redo2 className="w-4 h-4" />
              </button>
            </div>

            {/* Action Buttons */}
            <button
              onClick={handleSave}
              disabled={!hasUnsavedChanges}
              className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2"
            >
              <Save className="w-4 h-4" />
              <span>{language === 'sw' ? 'Hifadhi' : 'Save'}</span>
            </button>

            <button
              onClick={handleDownload}
              className="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 flex items-center space-x-2"
            >
              <Download className="w-4 h-4" />
              <span>{language === 'sw' ? 'Pakua' : 'Download'}</span>
            </button>

            <button
              onClick={onClose}
              className="px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700"
            >
              {language === 'sw' ? 'Funga' : 'Close'}
            </button>
          </div>
        </div>
      </div>

      {/* Tab Navigation */}
      <div className="bg-white border-b border-gray-200">
        <div className="px-6">
          <nav className="flex space-x-8">
            {tabs.map((tab) => {
              const Icon = tab.icon
              return (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id as any)}
                  className={`py-4 px-1 border-b-2 font-medium text-sm flex items-center space-x-2 ${
                    activeTab === tab.id
                      ? 'border-blue-500 text-blue-600'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  }`}
                >
                  <Icon className="w-4 h-4" />
                  <span>{tab.label}</span>
                </button>
              )
            })}
          </nav>
        </div>
      </div>

      {/* Content Area */}
      <div className="flex-1 flex overflow-hidden">
        {/* Left Panel - Editing Tools */}
        <div className="w-1/3 bg-white border-r border-gray-200 overflow-y-auto">
          <div className="p-6">
            {activeTab === 'production' && (
              <ProductionEditor
                websiteData={websiteData}
                onSave={onSave}
                language={language}
              />
            )}

            {activeTab === 'chat' && (
              <div className="space-y-6">
                <div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-4">
                    {language === 'sw' ? 'Hariri kwa Mazungumzo' : 'Chat-Based Editing'}
                  </h3>
                  
                  {/* Quick Suggestions */}
                  <div className="mb-6">
                    <h4 className="font-medium text-gray-700 mb-3">
                      {language === 'sw' ? 'Mapendekezo ya Haraka' : 'Quick Suggestions'}
                    </h4>
                    <div className="grid grid-cols-2 gap-3">
                      {quickEditSuggestions.map((category) => (
                        <div key={category.category} className="space-y-2">
                          <h5 className="text-sm font-medium text-gray-600">{category.category}</h5>
                          {category.suggestions.slice(0, 2).map((suggestion) => (
                            <button
                              key={suggestion}
                              onClick={() => setEditInstructions(suggestion)}
                              className="w-full text-xs text-left p-2 text-blue-700 bg-blue-50 rounded-md hover:bg-blue-100 transition-colors"
                            >
                              {suggestion}
                            </button>
                          ))}
                        </div>
                      ))}
                    </div>
                  </div>

                  {/* Edit Instructions */}
                  <div className="space-y-4">
                    <textarea
                      value={editInstructions}
                      onChange={(e) => setEditInstructions(e.target.value)}
                      placeholder={language === 'sw'
                        ? 'Eleza mabadiliko unayotaka...'
                        : 'Describe the changes you want to make...'
                      }
                      className={`w-full p-4 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 resize-none ${
                        showDestructiveWarning ? 'border-red-300 bg-red-50' : 'border-gray-300'
                      }`}
                      rows={4}
                    />

                    {/* Destructive Warning */}
                    {showDestructiveWarning && (
                      <div className="p-3 bg-red-100 border border-red-300 rounded-lg">
                        <div className="flex items-start space-x-2">
                          <div className="text-red-600 text-lg">⚠️</div>
                          <div>
                            <h4 className="font-medium text-red-800">
                              {language === 'sw' ? 'Onyo la Hatari' : 'Destructive Action Warning'}
                            </h4>
                            <p className="text-sm text-red-700 mt-1">
                              {language === 'sw'
                                ? 'Maagizo haya yanaweza kuharibu tovuti yako. Hakikisha umehifadhi nakala ya kumbuka kabla ya kuendelea.'
                                : 'This instruction may delete or significantly change your website. Make sure you have a backup before proceeding.'
                              }
                            </p>
                            <p className="text-xs text-red-600 mt-2">
                              {language === 'sw'
                                ? 'Tumia "Rudisha" kama kitu kinaenda vibaya.'
                                : 'Use the "Undo" button if something goes wrong.'
                              }
                            </p>
                          </div>
                        </div>
                      </div>
                    )}

                    <button
                      onClick={handleChatEdit}
                      disabled={!editInstructions.trim() || isEditing}
                      className={`w-full px-4 py-3 text-white rounded-lg disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center space-x-2 ${
                        showDestructiveWarning
                          ? 'bg-gradient-to-r from-red-600 to-orange-600 hover:from-red-700 hover:to-orange-700'
                          : 'bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700'
                      }`}
                    >
                      {isEditing ? (
                        <>
                          <RefreshCw className="w-4 h-4 animate-spin" />
                          <span>{language === 'sw' ? 'Inabadilisha...' : 'Applying...'}</span>
                        </>
                      ) : (
                        <>
                          {showDestructiveWarning ? (
                            <>
                              <AlertTriangle className="w-4 h-4" />
                              <span>{language === 'sw' ? 'Tekeleza (Hatari!)' : 'Apply (Risky!)'}</span>
                            </>
                          ) : (
                            <>
                              <Sparkles className="w-4 h-4" />
                              <span>{language === 'sw' ? 'Tekeleza Mabadiliko' : 'Apply Changes'}</span>
                            </>
                          )}
                        </>
                      )}
                    </button>
                  </div>
                </div>
              </div>
            )}

            {activeTab === 'images' && (
              <div>
                <h3 className="text-lg font-semibold text-gray-900 mb-4">
                  {language === 'sw' ? 'Msimamizi wa Picha' : 'Image Management'}
                </h3>
                <ImageManager
                  websiteHtml={websiteData.website.html}
                  onImageReplace={handleImageReplace}
                  onImageRemove={handleImageRemove}
                  language={language}
                  className="border-0"
                />
              </div>
            )}

            {activeTab === 'style' && (
              <div>
                <h3 className="text-lg font-semibold text-gray-900 mb-4">
                  {language === 'sw' ? 'Mtindo na Rangi' : 'Style & Colors'}
                </h3>
                <p className="text-gray-600 text-sm">
                  {language === 'sw' 
                    ? 'Tumia mazungumzo kuhariri mitindo na rangi.'
                    : 'Use chat editing to modify styles and colors.'
                  }
                </p>
              </div>
            )}

            {activeTab === 'layout' && (
              <div>
                <h3 className="text-lg font-semibold text-gray-900 mb-4">
                  {language === 'sw' ? 'Mpangilio wa Ukurasa' : 'Page Layout'}
                </h3>
                <p className="text-gray-600 text-sm">
                  {language === 'sw' 
                    ? 'Tumia mazungumzo kubadilisha mpangilio wa ukurasa.'
                    : 'Use chat editing to modify page layout.'
                  }
                </p>
              </div>
            )}
          </div>
        </div>

        {/* Right Panel - Preview */}
        <div className="flex-1 bg-gray-100 overflow-hidden">
          <div className="h-full flex flex-col">
            {/* Preview Controls */}
            <div className="bg-white border-b border-gray-200 px-4 py-3">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-4">
                  <h4 className="font-medium text-gray-900">
                    {language === 'sw' ? 'Onyesho la Tovuti' : 'Website Preview'}
                  </h4>
                  <div className="flex items-center space-x-2">
                    {['desktop', 'tablet', 'mobile'].map((mode) => (
                      <button
                        key={mode}
                        onClick={() => setPreviewMode(mode as any)}
                        className={`px-3 py-1 text-sm rounded-md ${
                          previewMode === mode
                            ? 'bg-blue-100 text-blue-700'
                            : 'text-gray-600 hover:text-gray-900'
                        }`}
                      >
                        {mode === 'desktop' && <Globe className="w-4 h-4" />}
                        {mode === 'tablet' && <Layout className="w-4 h-4" />}
                        {mode === 'mobile' && <Smartphone className="w-4 h-4" />}
                      </button>
                    ))}
                  </div>
                </div>
                
                <button
                  onClick={() => {
                    if (websiteData?.website?.html) {
                      const newWindow = window.open('', '_blank')
                      if (newWindow) {
                        newWindow.document.write(websiteData.website.html)
                        newWindow.document.close()
                      }
                    } else {
                      alert('No website content available to preview')
                    }
                  }}
                  className="px-3 py-1 bg-gray-100 text-gray-700 text-sm rounded-md hover:bg-gray-200 flex items-center space-x-1"
                >
                  <Share2 className="w-4 h-4" />
                  <span>{language === 'sw' ? 'Fungua Dirisha Jipya' : 'Open in New Tab'}</span>
                </button>
              </div>
            </div>

            {/* Preview Content */}
            <div className="flex-1 p-4">
              <div className={`mx-auto bg-white rounded-lg shadow-lg overflow-hidden ${
                previewMode === 'mobile' ? 'max-w-sm' :
                previewMode === 'tablet' ? 'max-w-2xl' : 'w-full'
              }`}>
                {websiteData?.website?.html ? (
                  <SecureIframeRenderer
                    html={websiteData.website.html}
                    css=""
                    title="Website Preview"
                    className="w-full h-full"
                    onLoad={() => {
                      console.log('✅ EditingDashboard: Preview loaded successfully')
                      console.log('📄 HTML length:', websiteData.website.html.length)
                    }}
                    onError={(error) => {
                      console.error('❌ EditingDashboard: Preview failed to load:', error)
                      console.log('📄 HTML content:', websiteData.website.html.substring(0, 200))
                    }}
                  />
                ) : (
                  <div className="w-full h-full flex items-center justify-center text-gray-500">
                    <div className="text-center">
                      <div className="text-4xl mb-4">📄</div>
                      <p>No website content available</p>
                      <p className="text-sm mt-2">Please generate a website first</p>
                      <p className="text-xs mt-2 text-red-500">
                        Debug: websiteData={JSON.stringify(!!websiteData)},
                        website={JSON.stringify(!!websiteData?.website)},
                        html={JSON.stringify(!!websiteData?.website?.html)}
                      </p>
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
