'use client'

import { useState } from 'react'
import { Check, Palette, Eye } from 'lucide-react'

interface ColorPalette {
  name: string
  description: string
  colors: string[]
  cultural_significance?: string
}

interface KenyanColorPickerProps {
  selectedColor?: string
  onColorSelect: (color: string, palette?: string) => void
  language?: 'en' | 'sw'
  showCustomPicker?: boolean
}

export default function KenyanColorPicker({
  selectedColor = '#000000',
  onColorSelect,
  language = 'en',
  showCustomPicker = true
}: KenyanColorPickerProps) {
  const [activeTab, setActiveTab] = useState<'cultural' | 'modern' | 'custom'>('cultural')
  const [customColor, setCustomColor] = useState(selectedColor)

  // Kenyan cultural color palettes with cultural significance
  const kenyanPalettes: Record<string, ColorPalette> = {
    flag: {
      name: language === 'sw' ? 'Rangi za Bendera ya Kenya' : 'Kenyan Flag Colors',
      description: language === 'sw' 
        ? 'Rangi za bendera ya taifa ya Kenya'
        : 'Official colors of the Kenyan national flag',
      colors: ['#000000', '#FFFFFF', '#FF0000', '#006633'],
      cultural_significance: language === 'sw'
        ? 'Nyeusi: Watu wa Afrika, Nyeupe: Amani, Nyekundu: Damu, Kijani: Mazingira'
        : 'Black: African people, White: Peace, Red: Blood, Green: Natural wealth'
    },
    maasai: {
      name: language === 'sw' ? 'Rangi za Kimaasai' : 'Maasai Colors',
      description: language === 'sw'
        ? 'Rangi za jadi za jamii ya Kimaasai'
        : 'Traditional colors of the Maasai community',
      colors: ['#CC0000', '#000000', '#FFFFFF', '#FFD700', '#8B4513'],
      cultural_significance: language === 'sw'
        ? 'Nyekundu: Ujasiri na nguvu, Nyeusi: Watu, Dhahabu: Utajiri'
        : 'Red: Bravery and strength, Black: People, Gold: Wealth'
    },
    savanna: {
      name: language === 'sw' ? 'Rangi za Savana' : 'Savanna Colors',
      description: language === 'sw'
        ? 'Rangi za mazingira ya savana ya Kenya'
        : 'Colors inspired by Kenyan savanna landscapes',
      colors: ['#DEB887', '#CD853F', '#8FBC8F', '#87CEEB', '#F4A460'],
      cultural_significance: language === 'sw'
        ? 'Rangi za ardhi, miti, na anga za Kenya'
        : 'Colors of Kenyan earth, vegetation, and sky'
    },
    coastal: {
      name: language === 'sw' ? 'Rangi za Pwani' : 'Coastal Colors',
      description: language === 'sw'
        ? 'Rangi za pwani ya Bahari ya Hindi'
        : 'Colors inspired by the Indian Ocean coast',
      colors: ['#006994', '#40E0D0', '#F0F8FF', '#FFE4B5', '#D2691E'],
      cultural_significance: language === 'sw'
        ? 'Rangi za bahari, mchanga, na miombo ya pwani'
        : 'Colors of ocean, sand, and coastal palms'
    },
    highlands: {
      name: language === 'sw' ? 'Rangi za Milima' : 'Highland Colors',
      description: language === 'sw'
        ? 'Rangi za milima na misitu ya Kenya'
        : 'Colors from Kenyan highlands and forests',
      colors: ['#228B22', '#006400', '#8FBC8F', '#654321', '#2F4F4F'],
      cultural_significance: language === 'sw'
        ? 'Rangi za misitu, milima, na mazao'
        : 'Colors of forests, mountains, and agriculture'
    },
    sunset: {
      name: language === 'sw' ? 'Rangi za Macheo' : 'African Sunset',
      description: language === 'sw'
        ? 'Rangi za macheo ya Afrika'
        : 'Colors of African sunset',
      colors: ['#FF4500', '#FF6347', '#FFD700', '#FF69B4', '#8B008B'],
      cultural_significance: language === 'sw'
        ? 'Rangi za jua la macheo katika savana'
        : 'Colors of sunset over the savanna'
    }
  }

  // Modern business-friendly palettes
  const modernPalettes: Record<string, ColorPalette> = {
    professional: {
      name: language === 'sw' ? 'Kibiashara' : 'Professional',
      description: language === 'sw' ? 'Rangi za kibiashara' : 'Business-friendly colors',
      colors: ['#1E40AF', '#059669', '#DC2626', '#7C3AED', '#0891B2']
    },
    warm: {
      name: language === 'sw' ? 'Joto' : 'Warm',
      description: language === 'sw' ? 'Rangi za joto' : 'Warm and inviting colors',
      colors: ['#F59E0B', '#EF4444', '#EC4899', '#8B5CF6', '#F97316']
    },
    cool: {
      name: language === 'sw' ? 'Baridi' : 'Cool',
      description: language === 'sw' ? 'Rangi za baridi' : 'Cool and calming colors',
      colors: ['#3B82F6', '#10B981', '#06B6D4', '#8B5CF6', '#6366F1']
    },
    earth: {
      name: language === 'sw' ? 'Udongo' : 'Earth Tones',
      description: language === 'sw' ? 'Rangi za asili' : 'Natural earth tones',
      colors: ['#92400E', '#059669', '#1F2937', '#7C2D12', '#365314']
    }
  }

  const renderColorPalette = (palettes: Record<string, ColorPalette>, type: string) => {
    return Object.entries(palettes).map(([key, palette]) => (
      <div key={key} className="mb-6">
        <div className="mb-3">
          <h3 className="font-semibold text-gray-900 mb-1">{palette.name}</h3>
          <p className="text-sm text-gray-600 mb-2">{palette.description}</p>
          {palette.cultural_significance && (
            <p className="text-xs text-gray-500 italic">{palette.cultural_significance}</p>
          )}
        </div>
        
        <div className="grid grid-cols-5 gap-2">
          {palette.colors.map((color, index) => (
            <button
              key={index}
              onClick={() => onColorSelect(color, key)}
              className={`
                relative w-12 h-12 rounded-lg border-2 transition-all duration-200 hover:scale-110
                ${selectedColor === color 
                  ? 'border-gray-800 shadow-lg' 
                  : 'border-gray-200 hover:border-gray-400'
                }
              `}
              style={{ backgroundColor: color }}
              title={`${palette.name} - ${color}`}
            >
              {selectedColor === color && (
                <div className="absolute inset-0 flex items-center justify-center">
                  <Check className="w-4 h-4 text-white drop-shadow-lg" />
                </div>
              )}
            </button>
          ))}
        </div>
      </div>
    ))
  }

  return (
    <div className="bg-white rounded-xl shadow-lg border border-gray-200 p-6">
      <div className="mb-6">
        <h2 className="text-xl font-bold text-gray-900 mb-2 flex items-center space-x-2">
          <Palette className="w-6 h-6" />
          <span>{language === 'sw' ? 'Chagua Rangi' : 'Choose Color'}</span>
        </h2>
        <p className="text-gray-600">
          {language === 'sw' 
            ? 'Chagua rangi kutoka kwa mchanganyiko wa kitamaduni cha Kenya'
            : 'Select from culturally inspired Kenyan color palettes'
          }
        </p>
      </div>

      {/* Current Color Display */}
      <div className="mb-6 p-4 bg-gray-50 rounded-lg">
        <div className="flex items-center space-x-4">
          <div
            className="w-16 h-16 rounded-lg border-2 border-gray-300 shadow-inner"
            style={{ backgroundColor: selectedColor }}
          />
          <div>
            <p className="text-sm font-medium text-gray-700">
              {language === 'sw' ? 'Rangi Iliyochaguliwa' : 'Selected Color'}
            </p>
            <p className="text-lg font-mono text-gray-900">{selectedColor}</p>
          </div>
        </div>
      </div>

      {/* Tab Navigation */}
      <div className="flex space-x-1 mb-6 bg-gray-100 rounded-lg p-1">
        <button
          onClick={() => setActiveTab('cultural')}
          className={`flex-1 py-2 px-4 rounded-md text-sm font-medium transition-colors ${
            activeTab === 'cultural'
              ? 'bg-white text-gray-900 shadow-sm'
              : 'text-gray-600 hover:text-gray-900'
          }`}
        >
          {language === 'sw' ? 'Kitamaduni' : 'Cultural'}
        </button>
        <button
          onClick={() => setActiveTab('modern')}
          className={`flex-1 py-2 px-4 rounded-md text-sm font-medium transition-colors ${
            activeTab === 'modern'
              ? 'bg-white text-gray-900 shadow-sm'
              : 'text-gray-600 hover:text-gray-900'
          }`}
        >
          {language === 'sw' ? 'Kisasa' : 'Modern'}
        </button>
        {showCustomPicker && (
          <button
            onClick={() => setActiveTab('custom')}
            className={`flex-1 py-2 px-4 rounded-md text-sm font-medium transition-colors ${
              activeTab === 'custom'
                ? 'bg-white text-gray-900 shadow-sm'
                : 'text-gray-600 hover:text-gray-900'
            }`}
          >
            {language === 'sw' ? 'Maalum' : 'Custom'}
          </button>
        )}
      </div>

      {/* Tab Content */}
      <div className="max-h-96 overflow-y-auto">
        {activeTab === 'cultural' && (
          <div>
            <div className="mb-4 p-3 bg-green-50 border border-green-200 rounded-lg">
              <p className="text-sm text-green-800 flex items-center space-x-2">
                <Eye className="w-4 h-4" />
                <span>
                  {language === 'sw'
                    ? 'Rangi hizi zinawakilisha utamaduni na mazingira ya Kenya'
                    : 'These colors represent Kenyan culture and environment'
                  }
                </span>
              </p>
            </div>
            {renderColorPalette(kenyanPalettes, 'cultural')}
          </div>
        )}

        {activeTab === 'modern' && (
          <div>
            {renderColorPalette(modernPalettes, 'modern')}
          </div>
        )}

        {activeTab === 'custom' && showCustomPicker && (
          <div>
            <div className="mb-4">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                {language === 'sw' ? 'Chagua Rangi Yoyote' : 'Pick Any Color'}
              </label>
              <div className="flex items-center space-x-4">
                <input
                  type="color"
                  value={customColor}
                  onChange={(e) => setCustomColor(e.target.value)}
                  className="w-16 h-16 rounded-lg border-2 border-gray-300 cursor-pointer"
                />
                <div>
                  <input
                    type="text"
                    value={customColor}
                    onChange={(e) => setCustomColor(e.target.value)}
                    placeholder="#000000"
                    className="w-32 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 font-mono text-sm"
                  />
                  <button
                    onClick={() => onColorSelect(customColor, 'custom')}
                    className="ml-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                  >
                    {language === 'sw' ? 'Tumia' : 'Use'}
                  </button>
                </div>
              </div>
            </div>

            <div className="p-3 bg-blue-50 border border-blue-200 rounded-lg">
              <p className="text-sm text-blue-800">
                {language === 'sw'
                  ? 'Unaweza kuingiza nambari ya rangi (hex) au kutumia kichaguzi cha rangi'
                  : 'You can enter a hex color code or use the color picker'
                }
              </p>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}

// Utility function to get color name from hex
export const getColorName = (hex: string, language: 'en' | 'sw' = 'en'): string => {
  const colorNames: Record<string, { en: string; sw: string }> = {
    '#000000': { en: 'Black', sw: 'Nyeusi' },
    '#FFFFFF': { en: 'White', sw: 'Nyeupe' },
    '#FF0000': { en: 'Red', sw: 'Nyekundu' },
    '#006633': { en: 'Green', sw: 'Kijani' },
    '#0000FF': { en: 'Blue', sw: 'Buluu' },
    '#FFFF00': { en: 'Yellow', sw: 'Njano' },
    '#FFA500': { en: 'Orange', sw: 'Machungwa' },
    '#800080': { en: 'Purple', sw: 'Zambarau' },
    '#FFC0CB': { en: 'Pink', sw: 'Waridi' },
    '#A52A2A': { en: 'Brown', sw: 'Kahawia' },
    '#808080': { en: 'Gray', sw: 'Kijivu' }
  }

  return colorNames[hex.toUpperCase()]?.[language] || hex
}

// Utility function to check if color is light or dark
export const isLightColor = (hex: string): boolean => {
  const r = parseInt(hex.slice(1, 3), 16)
  const g = parseInt(hex.slice(3, 5), 16)
  const b = parseInt(hex.slice(5, 7), 16)
  const brightness = (r * 299 + g * 587 + b * 114) / 1000
  return brightness > 128
}
