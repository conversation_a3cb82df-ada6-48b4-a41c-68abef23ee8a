'use client'

import { useState, useRef, useCallback } from 'react'
import { Upload, Image as ImageIcon, X, Check, AlertCircle, Zap, BarChart3 } from 'lucide-react'
import { optimizeImageFile, analyzeImageOptimization } from '@/lib/image-optimization'

interface DragDropImageUploadProps {
  onImageUpload: (file: File, optimizedUrl: string) => void
  currentImage?: string
  className?: string
  language?: 'en' | 'sw'
  maxSizeMB?: number
  acceptedFormats?: string[]
}

export default function DragDropImageUpload({
  onImageUpload,
  currentImage,
  className = '',
  language = 'en',
  maxSizeMB = 5,
  acceptedFormats = ['image/jpeg', 'image/png', 'image/webp', 'image/gif']
}: DragDropImageUploadProps) {
  const [isDragging, setIsDragging] = useState(false)
  const [isUploading, setIsUploading] = useState(false)
  const [isOptimizing, setIsOptimizing] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [preview, setPreview] = useState<string | null>(currentImage || null)
  const [optimizationStats, setOptimizationStats] = useState<any>(null)
  const [showOptimizationDetails, setShowOptimizationDetails] = useState(false)
  const fileInputRef = useRef<HTMLInputElement>(null)

  const validateFile = (file: File): string | null => {
    // Check file type
    if (!acceptedFormats.includes(file.type)) {
      return language === 'sw' 
        ? 'Aina ya faili haijaidhinishwa. Tumia JPEG, PNG, WebP au GIF.'
        : 'File type not supported. Please use JPEG, PNG, WebP, or GIF.'
    }

    // Check file size
    const fileSizeMB = file.size / (1024 * 1024)
    if (fileSizeMB > maxSizeMB) {
      return language === 'sw'
        ? `Faili ni kubwa mno. Ukubwa wa juu ni ${maxSizeMB}MB.`
        : `File is too large. Maximum size is ${maxSizeMB}MB.`
    }

    return null
  }

  const optimizeImage = async (file: File): Promise<string> => {
    return new Promise((resolve, reject) => {
      const canvas = document.createElement('canvas')
      const ctx = canvas.getContext('2d')
      const img = new Image()

      img.onload = () => {
        // Calculate optimal dimensions (max 1920x1080 for web)
        const maxWidth = 1920
        const maxHeight = 1080
        let { width, height } = img

        if (width > maxWidth || height > maxHeight) {
          const ratio = Math.min(maxWidth / width, maxHeight / height)
          width *= ratio
          height *= ratio
        }

        canvas.width = width
        canvas.height = height

        // Draw and compress
        ctx?.drawImage(img, 0, 0, width, height)
        
        // Convert to WebP for better compression (fallback to JPEG)
        const quality = 0.8
        const optimizedDataUrl = canvas.toDataURL('image/webp', quality) || 
                                 canvas.toDataURL('image/jpeg', quality)
        
        resolve(optimizedDataUrl)
      }

      img.onerror = () => reject(new Error('Failed to load image'))
      img.src = URL.createObjectURL(file)
    })
  }

  const handleFile = useCallback(async (file: File) => {
    setError(null)
    setIsUploading(true)

    try {
      // Validate file
      const validationError = validateFile(file)
      if (validationError) {
        setError(validationError)
        return
      }

      // Create preview
      const previewUrl = URL.createObjectURL(file)
      setPreview(previewUrl)

      // Analyze and optimize image
      setIsOptimizing(true)
      const analysis = await analyzeImageOptimization(file)

      const optimized = await optimizeImageFile(file, {
        maxWidth: 1920,
        maxHeight: 1080,
        quality: 0.85,
        format: 'auto'
      })

      setOptimizationStats({
        originalSize: analysis.currentSize,
        optimizedSize: analysis.optimalSize,
        compressionRatio: optimized.compressionRatio,
        recommendations: analysis.recommendations,
        format: optimized.format,
        dimensions: optimized.dimensions
      })

      setIsOptimizing(false)

      // Create optimized file
      const optimizedFile = new File([optimized.blob], file.name, {
        type: optimized.format,
        lastModified: Date.now()
      })

      // Call the upload handler with optimized data
      onImageUpload(optimizedFile, optimized.dataUrl)

      // Clean up preview URL
      URL.revokeObjectURL(previewUrl)
    } catch (err) {
      setError(language === 'sw' 
        ? 'Imeshindwa kusindika picha. Jaribu tena.'
        : 'Failed to process image. Please try again.'
      )
    } finally {
      setIsUploading(false)
    }
  }, [onImageUpload, language, maxSizeMB, acceptedFormats])

  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault()
    setIsDragging(false)

    const files = Array.from(e.dataTransfer.files)
    if (files.length > 0) {
      handleFile(files[0])
    }
  }, [handleFile])

  const handleDragOver = useCallback((e: React.DragEvent) => {
    e.preventDefault()
    setIsDragging(true)
  }, [])

  const handleDragLeave = useCallback((e: React.DragEvent) => {
    e.preventDefault()
    setIsDragging(false)
  }, [])

  const handleFileSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files
    if (files && files.length > 0) {
      handleFile(files[0])
    }
  }

  const removeImage = () => {
    setPreview(null)
    setError(null)
    if (fileInputRef.current) {
      fileInputRef.current.value = ''
    }
  }

  return (
    <div className={`relative ${className}`}>
      <div
        className={`
          border-2 border-dashed rounded-xl p-6 text-center transition-all duration-200
          ${isDragging 
            ? 'border-blue-500 bg-blue-50' 
            : 'border-gray-300 hover:border-gray-400'
          }
          ${preview ? 'border-green-500 bg-green-50' : ''}
          ${error ? 'border-red-500 bg-red-50' : ''}
        `}
        onDrop={handleDrop}
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
        onClick={() => fileInputRef.current?.click()}
      >
        <input
          ref={fileInputRef}
          type="file"
          accept={acceptedFormats.join(',')}
          onChange={handleFileSelect}
          className="hidden"
        />

        {preview ? (
          <div className="relative">
            <img
              src={preview}
              alt="Preview"
              className="max-w-full max-h-48 mx-auto rounded-lg shadow-md"
            />
            <button
              onClick={(e) => {
                e.stopPropagation()
                removeImage()
              }}
              className="absolute top-2 right-2 p-1 bg-red-500 text-white rounded-full hover:bg-red-600 transition-colors"
            >
              <X className="w-4 h-4" />
            </button>
            {!isUploading && !isOptimizing && (
              <div className="absolute bottom-2 right-2 p-1 bg-green-500 text-white rounded-full">
                <Check className="w-4 h-4" />
              </div>
            )}

            {isOptimizing && (
              <div className="absolute bottom-2 right-2 p-1 bg-blue-500 text-white rounded-full">
                <Zap className="w-4 h-4 animate-pulse" />
              </div>
            )}

            {/* Optimization Stats */}
            {optimizationStats && (
              <div className="absolute bottom-2 left-2">
                <button
                  onClick={(e) => {
                    e.stopPropagation()
                    setShowOptimizationDetails(!showOptimizationDetails)
                  }}
                  className="bg-green-500 text-white text-xs px-2 py-1 rounded-md hover:bg-green-600 transition-colors flex items-center gap-1"
                >
                  <Zap className="w-3 h-3" />
                  {optimizationStats.compressionRatio}% {language === 'sw' ? 'optimized' : 'smaller'}
                </button>
              </div>
            )}

            {/* Optimization Details Panel */}
            {showOptimizationDetails && optimizationStats && (
            <div className="mt-4 p-4 bg-gray-50 rounded-lg border">
              <h4 className="font-semibold text-sm mb-2 flex items-center gap-2">
                <Zap className="w-4 h-4 text-green-500" />
                {language === 'sw' ? 'Maelezo ya Uboreshaji' : 'Optimization Details'}
              </h4>

              <div className="grid grid-cols-2 gap-4 text-xs">
                <div>
                  <span className="text-gray-600">
                    {language === 'sw' ? 'Ukubwa wa awali:' : 'Original size:'}
                  </span>
                  <div className="font-medium">{optimizationStats.originalSize}</div>
                </div>
                <div>
                  <span className="text-gray-600">
                    {language === 'sw' ? 'Ukubwa mpya:' : 'Optimized size:'}
                  </span>
                  <div className="font-medium text-green-600">{optimizationStats.optimizedSize}</div>
                </div>
                <div>
                  <span className="text-gray-600">
                    {language === 'sw' ? 'Muundo:' : 'Format:'}
                  </span>
                  <div className="font-medium">{optimizationStats.format}</div>
                </div>
                <div>
                  <span className="text-gray-600">
                    {language === 'sw' ? 'Vipimo:' : 'Dimensions:'}
                  </span>
                  <div className="font-medium">
                    {optimizationStats.dimensions.width} × {optimizationStats.dimensions.height}
                  </div>
                </div>
              </div>

              {optimizationStats.recommendations.length > 0 && (
                <div className="mt-3">
                  <span className="text-gray-600 text-xs">
                    {language === 'sw' ? 'Mapendekezo:' : 'Recommendations:'}
                  </span>
                  <ul className="mt-1 text-xs text-gray-700">
                    {optimizationStats.recommendations.map((rec: string, index: number) => (
                      <li key={index} className="flex items-start gap-1">
                        <span className="text-blue-500">•</span>
                        {rec}
                      </li>
                    ))}
                  </ul>
                </div>
              )}
            </div>
            )}
          </div>
        ) : (
          <div className="space-y-4">
            <div className="mx-auto w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center">
              {isUploading || isOptimizing ? (
                <div className="flex flex-col items-center">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
                  {isOptimizing && (
                    <Zap className="w-4 h-4 text-blue-500 mt-1 animate-pulse" />
                  )}
                </div>
              ) : (
                <Upload className="w-8 h-8 text-gray-400" />
              )}
            </div>
            
            <div>
              <p className="text-lg font-medium text-gray-900 mb-2">
                {isOptimizing
                  ? (language === 'sw' ? 'Inaboresha picha...' : 'Optimizing image...')
                  : isUploading
                    ? (language === 'sw' ? 'Inapakia...' : 'Uploading...')
                    : (language === 'sw' ? 'Buruta na udondoshe picha hapa' : 'Drag and drop an image here')
                }
              </p>
              <p className="text-sm text-gray-600 mb-4">
                {isOptimizing
                  ? (language === 'sw' ? 'Tunapunguza ukubwa na kuboresha ubora' : 'Reducing size and improving quality')
                  : isUploading
                    ? (language === 'sw' ? 'Subiri kidogo...' : 'Please wait...')
                    : (language === 'sw' ? 'au bofya kuchagua faili' : 'or click to select a file')
                }
              </p>
              <p className="text-xs text-gray-500">
                {language === 'sw' 
                  ? `Viumbo vilivyoidhinishwa: ${acceptedFormats.map(f => f.split('/')[1].toUpperCase()).join(', ')}`
                  : `Supported formats: ${acceptedFormats.map(f => f.split('/')[1].toUpperCase()).join(', ')}`
                }
                <br />
                {language === 'sw' 
                  ? `Ukubwa wa juu: ${maxSizeMB}MB`
                  : `Maximum size: ${maxSizeMB}MB`
                }
              </p>
            </div>
          </div>
        )}

        {isUploading && (
          <div className="absolute inset-0 bg-white bg-opacity-75 flex items-center justify-center rounded-xl">
            <div className="text-center">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-2"></div>
              <p className="text-sm text-gray-600">
                {language === 'sw' ? 'Inasindika picha...' : 'Processing image...'}
              </p>
            </div>
          </div>
        )}
      </div>

      {error && (
        <div className="mt-3 p-3 bg-red-50 border border-red-200 rounded-lg flex items-start space-x-2">
          <AlertCircle className="w-5 h-5 text-red-500 flex-shrink-0 mt-0.5" />
          <p className="text-sm text-red-700">{error}</p>
        </div>
      )}

      {preview && !error && (
        <div className="mt-3 p-3 bg-green-50 border border-green-200 rounded-lg">
          <p className="text-sm text-green-700 flex items-center space-x-2">
            <Check className="w-4 h-4" />
            <span>
              {language === 'sw' 
                ? 'Picha imepakiwa na kuboreshwa kwa ajili ya wavuti'
                : 'Image uploaded and optimized for web'
              }
            </span>
          </p>
        </div>
      )}
    </div>
  )
}

// Utility function to compress images
export const compressImage = async (
  file: File, 
  maxWidth: number = 1920, 
  maxHeight: number = 1080, 
  quality: number = 0.8
): Promise<Blob> => {
  return new Promise((resolve, reject) => {
    const canvas = document.createElement('canvas')
    const ctx = canvas.getContext('2d')
    const img = new Image()

    img.onload = () => {
      let { width, height } = img

      // Calculate new dimensions
      if (width > maxWidth || height > maxHeight) {
        const ratio = Math.min(maxWidth / width, maxHeight / height)
        width *= ratio
        height *= ratio
      }

      canvas.width = width
      canvas.height = height

      // Draw image
      ctx?.drawImage(img, 0, 0, width, height)

      // Convert to blob
      canvas.toBlob(
        (blob) => {
          if (blob) {
            resolve(blob)
          } else {
            reject(new Error('Failed to compress image'))
          }
        },
        'image/webp',
        quality
      )
    }

    img.onerror = () => reject(new Error('Failed to load image'))
    img.src = URL.createObjectURL(file)
  })
}
