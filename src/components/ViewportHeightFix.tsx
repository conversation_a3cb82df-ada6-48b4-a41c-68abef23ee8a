'use client'

import { useEffect } from 'react'

/**
 * Client-side component to fix viewport height issues on mobile browsers
 * This prevents hydration mismatches by only running on the client
 */
export default function ViewportHeightFix() {
  useEffect(() => {
    // Fix viewport height on mobile browsers
    function setVH() {
      const vh = window.innerHeight * 0.01
      document.documentElement.style.setProperty('--vh', `${vh}px`)
    }

    // Delay initial setting to avoid hydration mismatch
    const timeoutId = setTimeout(() => {
      setVH()
    }, 100)

    // Update on resize and orientation change
    window.addEventListener('resize', setVH, { passive: true })
    window.addEventListener('orientationchange', setVH, { passive: true })

    // Cleanup
    return () => {
      clearTimeout(timeoutId)
      window.removeEventListener('resize', setVH)
      window.removeEventListener('orientationchange', setVH)
    }
  }, [])

  // This component doesn't render anything
  return null
}
