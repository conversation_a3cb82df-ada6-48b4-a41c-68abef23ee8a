'use client'

import { useState } from 'react'
import { MessageCircle, Phone, Mail, MapPin } from 'lucide-react'

interface ContactMethod {
  id: string
  name: string
  type: 'whatsapp' | 'call' | 'sms' | 'email'
  icon: React.ComponentType<{ className?: string }>
  description: string
  action: string
  color: string
}

interface ContactMethodsProps {
  businessProfile?: any
  onContactSelect?: (method: ContactMethod) => void
  className?: string
  layout?: 'grid' | 'horizontal' | 'vertical'
}

export default function ContactMethods({ 
  businessProfile, 
  onContactSelect, 
  className = '',
  layout = 'grid'
}: ContactMethodsProps) {
  const [selectedMethod, setSelectedMethod] = useState<string | null>(null)

  const phone = businessProfile?.contactInfo?.phone || ''
  const email = businessProfile?.contactInfo?.email || ''
  const businessName = businessProfile?.name || 'Business'

  const contactMethods: ContactMethod[] = [
    {
      id: 'whatsapp',
      name: 'WhatsApp',
      type: 'whatsapp',
      icon: MessageCircle,
      description: 'Chat with us on WhatsApp',
      action: `https://wa.me/${phone.replace(/[^0-9]/g, '')}?text=Hello%20${encodeURIComponent(businessName)},%20I%20would%20like%20to%20inquire%20about%20your%20services.`,
      color: 'bg-green-500 hover:bg-green-600'
    },
    {
      id: 'call',
      name: 'Call Us',
      type: 'call',
      icon: Phone,
      description: 'Call us directly',
      action: `tel:${phone}`,
      color: 'bg-blue-500 hover:bg-blue-600'
    },
    {
      id: 'sms',
      name: 'Send SMS',
      type: 'sms',
      icon: MessageCircle,
      description: 'Send us a text message',
      action: `sms:${phone}?body=Hello%20${encodeURIComponent(businessName)},%20I%20would%20like%20to%20inquire%20about%20your%20services.`,
      color: 'bg-purple-500 hover:bg-purple-600'
    },
    {
      id: 'email',
      name: 'Email Us',
      type: 'email',
      icon: Mail,
      description: 'Send us an email',
      action: `mailto:${email}?subject=Inquiry%20about%20${encodeURIComponent(businessName)}&body=Hello,%0D%0A%0D%0AI%20would%20like%20to%20inquire%20about%20your%20services.`,
      color: 'bg-orange-500 hover:bg-orange-600'
    }
  ]

  // Filter out methods without required info
  const availableMethods = contactMethods.filter(method => {
    if (method.type === 'whatsapp' || method.type === 'call' || method.type === 'sms') {
      return phone
    }
    if (method.type === 'email') {
      return email
    }
    return true
  })

  const handleMethodSelect = (method: ContactMethod) => {
    setSelectedMethod(method.id)
    onContactSelect?.(method)
    
    // Open the contact method
    if (method.action.startsWith('http')) {
      window.open(method.action, '_blank')
    } else {
      window.location.href = method.action
    }
  }

  const getLayoutClasses = () => {
    switch (layout) {
      case 'horizontal':
        return 'flex flex-wrap gap-3'
      case 'vertical':
        return 'flex flex-col gap-3'
      default:
        return 'grid grid-cols-1 sm:grid-cols-2 gap-4'
    }
  }

  return (
    <div className={`contact-methods ${className}`}>
      <div className="mb-6">
        <h3 className="text-xl font-bold text-gray-900 mb-2">Get In Touch</h3>
        <p className="text-gray-600">Choose how you'd like to contact us</p>
      </div>

      <div className={getLayoutClasses()}>
        {availableMethods.map((method) => (
          <button
            key={method.id}
            onClick={() => handleMethodSelect(method)}
            className={`
              group relative p-4 rounded-xl text-white font-semibold
              transform transition-all duration-200 hover:scale-105 hover:shadow-lg
              ${method.color}
              ${layout === 'horizontal' ? 'flex-1 min-w-[120px]' : ''}
            `}
          >
            <div className="flex items-center space-x-3">
              <div className="text-2xl">
                <method.icon className="w-8 h-8" />
              </div>
              <div className="text-left">
                <div className="font-bold">{method.name}</div>
                {layout !== 'horizontal' && (
                  <div className="text-sm opacity-90">{method.description}</div>
                )}
              </div>
            </div>
            
            {/* Hover effect */}
            <div className="absolute inset-0 bg-white opacity-0 group-hover:opacity-10 rounded-xl transition-opacity duration-200"></div>
          </button>
        ))}
      </div>

      {/* Contact info display */}
      <div className="mt-6 p-4 bg-gray-50 rounded-xl">
        <h4 className="font-semibold text-gray-900 mb-3">Contact Information</h4>
        <div className="space-y-2 text-sm">
          {phone && (
            <div className="flex items-center space-x-2">
              <Phone className="w-4 h-4 text-gray-500" />
              <span className="text-gray-700">{phone}</span>
            </div>
          )}
          {email && (
            <div className="flex items-center space-x-2">
              <Mail className="w-4 h-4 text-gray-500" />
              <span className="text-gray-700">{email}</span>
            </div>
          )}
          {businessProfile?.location && (
            <div className="flex items-center space-x-2">
              <MapPin className="w-4 h-4 text-gray-500" />
              <span className="text-gray-700">
                {businessProfile.location.area}, {businessProfile.location.county}
              </span>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}

// Quick contact buttons for generated websites
export function QuickContactButtons({ 
  phone, 
  businessName = 'Business',
  layout = 'horizontal',
  size = 'medium'
}: { 
  phone?: string
  businessName?: string
  layout?: 'horizontal' | 'vertical'
  size?: 'small' | 'medium' | 'large'
}) {
  if (!phone) return null

  const sizeClasses = {
    small: 'px-3 py-2 text-sm',
    medium: 'px-4 py-3 text-base',
    large: 'px-6 py-4 text-lg'
  }

  const whatsappUrl = `https://wa.me/${phone.replace(/[^0-9]/g, '')}?text=Hello%20${encodeURIComponent(businessName)},%20I%20would%20like%20to%20inquire%20about%20your%20services.`
  const callUrl = `tel:${phone}`

  return (
    <div className={`quick-contact ${layout === 'vertical' ? 'flex flex-col gap-3' : 'flex gap-3'}`}>
      <a
        href={whatsappUrl}
        target="_blank"
        rel="noopener noreferrer"
        className={`
          inline-flex items-center justify-center space-x-2 
          bg-green-500 hover:bg-green-600 text-white font-semibold rounded-lg
          transform transition-all duration-200 hover:scale-105 hover:shadow-lg
          ${sizeClasses[size]}
        `}
      >
        <MessageCircle className="w-5 h-5" />
        <span>WhatsApp</span>
      </a>

      <a
        href={callUrl}
        className={`
          inline-flex items-center justify-center space-x-2
          bg-blue-500 hover:bg-blue-600 text-white font-semibold rounded-lg
          transform transition-all duration-200 hover:scale-105 hover:shadow-lg
          ${sizeClasses[size]}
        `}
      >
        <Phone className="w-5 h-5" />
        <span>Call Now</span>
      </a>
    </div>
  )
}

// Floating contact button for websites
export function FloatingContactButton({ phone, businessName = 'Business' }: { phone?: string, businessName?: string }) {
  if (!phone) return null

  const whatsappUrl = `https://wa.me/${phone.replace(/[^0-9]/g, '')}?text=Hello%20${encodeURIComponent(businessName)},%20I%20would%20like%20to%20inquire%20about%20your%20services.`

  return (
    <a
      href={whatsappUrl}
      target="_blank"
      rel="noopener noreferrer"
      className="
        fixed bottom-6 right-6 z-50
        w-14 h-14 bg-green-500 hover:bg-green-600 
        rounded-full shadow-lg hover:shadow-xl
        flex items-center justify-center text-white text-2xl
        transform transition-all duration-200 hover:scale-110
        animate-pulse hover:animate-none
      "
      title="Chat on WhatsApp"
    >
      <MessageCircle className="w-6 h-6" />
    </a>
  )
}
