'use client'

import { useState, useEffect } from 'react'
import { 
  CheckCircle, 
  AlertTriangle, 
  XCircle, 
  Zap, 
  Globe, 
  BookOpen, 
  Search,
  Lightbulb,
  RefreshCw,
  Eye,
  Flag
} from 'lucide-react'
import { ContentAnalysis, analyzeContent } from '@/lib/content-moderation'

interface ContentQualityCheckerProps {
  content: string
  language?: 'en' | 'sw' | 'auto'
  context?: 'business' | 'general'
  onAnalysisComplete?: (analysis: ContentAnalysis) => void
  className?: string
  autoAnalyze?: boolean
}

export default function ContentQualityChecker({
  content,
  language = 'auto',
  context = 'business',
  onAnalysisComplete,
  className = '',
  autoAnalyze = true
}: ContentQualityCheckerProps) {
  const [analysis, setAnalysis] = useState<ContentAnalysis | null>(null)
  const [isAnalyzing, setIsAnalyzing] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [showDetails, setShowDetails] = useState(false)

  const performAnalysis = async () => {
    if (!content.trim()) return

    setIsAnalyzing(true)
    setError(null)

    try {
      const result = await analyzeContent(content, language, context)
      setAnalysis(result)
      onAnalysisComplete?.(result)
    } catch (err) {
      setError('Failed to analyze content')
      console.error('Content analysis error:', err)
    } finally {
      setIsAnalyzing(false)
    }
  }

  useEffect(() => {
    if (autoAnalyze && content.trim().length > 10) {
      const debounceTimer = setTimeout(performAnalysis, 1000)
      return () => clearTimeout(debounceTimer)
    }
  }, [content, language, context, autoAnalyze])

  const getScoreColor = (score: number) => {
    if (score >= 80) return 'text-green-600'
    if (score >= 60) return 'text-yellow-600'
    return 'text-red-600'
  }

  const getScoreIcon = (score: number) => {
    if (score >= 80) return <CheckCircle className="w-5 h-5 text-green-600" />
    if (score >= 60) return <AlertTriangle className="w-5 h-5 text-yellow-600" />
    return <XCircle className="w-5 h-5 text-red-600" />
  }

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'critical': return 'text-red-600 bg-red-50 border-red-200'
      case 'high': return 'text-orange-600 bg-orange-50 border-orange-200'
      case 'medium': return 'text-yellow-600 bg-yellow-50 border-yellow-200'
      case 'low': return 'text-blue-600 bg-blue-50 border-blue-200'
      default: return 'text-gray-600 bg-gray-50 border-gray-200'
    }
  }

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high': return 'text-red-600'
      case 'medium': return 'text-yellow-600'
      case 'low': return 'text-green-600'
      default: return 'text-gray-600'
    }
  }

  if (isAnalyzing) {
    return (
      <div className={`bg-white rounded-lg border p-4 ${className}`}>
        <div className="flex items-center justify-center py-8">
          <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600 mr-3"></div>
          <span className="text-gray-600">Analyzing content quality...</span>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className={`bg-white rounded-lg border border-red-200 p-4 ${className}`}>
        <div className="flex items-center justify-between">
          <div className="flex items-center">
            <XCircle className="w-5 h-5 text-red-600 mr-2" />
            <span className="text-red-600">{error}</span>
          </div>
          <button
            onClick={performAnalysis}
            className="p-1 text-red-600 hover:text-red-700"
          >
            <RefreshCw className="w-4 h-4" />
          </button>
        </div>
      </div>
    )
  }

  if (!analysis) {
    return (
      <div className={`bg-white rounded-lg border p-4 ${className}`}>
        <div className="text-center py-4">
          <BookOpen className="w-8 h-8 text-gray-400 mx-auto mb-2" />
          <p className="text-gray-600 mb-3">Content quality analysis will appear here</p>
          {!autoAnalyze && (
            <button
              onClick={performAnalysis}
              className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
            >
              Analyze Content
            </button>
          )}
        </div>
      </div>
    )
  }

  return (
    <div className={`bg-white rounded-lg border ${className}`}>
      {/* Header with Score */}
      <div className="p-4 border-b">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            {getScoreIcon(analysis.score)}
            <div>
              <h3 className="font-semibold">Content Quality Score</h3>
              <div className="flex items-center gap-2">
                <span className={`text-2xl font-bold ${getScoreColor(analysis.score)}`}>
                  {analysis.score}
                </span>
                <span className="text-gray-500">/100</span>
              </div>
            </div>
          </div>
          <div className="flex items-center gap-2">
            <button
              onClick={() => setShowDetails(!showDetails)}
              className="p-2 text-gray-500 hover:text-gray-700 transition-colors"
              title="Toggle details"
            >
              <Eye className="w-4 h-4" />
            </button>
            <button
              onClick={performAnalysis}
              className="p-2 text-gray-500 hover:text-gray-700 transition-colors"
              title="Refresh analysis"
            >
              <RefreshCw className="w-4 h-4" />
            </button>
          </div>
        </div>
      </div>

      {/* Quick Stats */}
      <div className="p-4 grid grid-cols-3 gap-4 border-b">
        <div className="text-center">
          <Globe className="w-5 h-5 text-blue-500 mx-auto mb-1" />
          <div className="text-sm font-medium">Cultural</div>
          <div className={`text-lg font-semibold ${getScoreColor(analysis.culturalSensitivity.score)}`}>
            {analysis.culturalSensitivity.score}
          </div>
        </div>
        <div className="text-center">
          <BookOpen className="w-5 h-5 text-green-500 mx-auto mb-1" />
          <div className="text-sm font-medium">Readability</div>
          <div className={`text-lg font-semibold ${getScoreColor(analysis.readability.score)}`}>
            {analysis.readability.score}
          </div>
        </div>
        <div className="text-center">
          <Search className="w-5 h-5 text-purple-500 mx-auto mb-1" />
          <div className="text-sm font-medium">SEO</div>
          <div className={`text-lg font-semibold ${getScoreColor(analysis.seoScore.score)}`}>
            {analysis.seoScore.score}
          </div>
        </div>
      </div>

      {/* Issues */}
      {analysis.issues.length > 0 && (
        <div className="p-4 border-b">
          <h4 className="font-medium mb-3 flex items-center gap-2">
            <Flag className="w-4 h-4 text-red-500" />
            Issues Found ({analysis.issues.length})
          </h4>
          <div className="space-y-2">
            {analysis.issues.slice(0, showDetails ? undefined : 3).map((issue, index) => (
              <div
                key={index}
                className={`p-3 rounded-md border ${getSeverityColor(issue.severity)}`}
              >
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <div className="font-medium text-sm">{issue.message}</div>
                    {issue.suggestion && (
                      <div className="text-xs mt-1 opacity-75">
                        Suggestion: {issue.suggestion}
                      </div>
                    )}
                  </div>
                  <span className="text-xs font-medium px-2 py-1 rounded">
                    {issue.severity}
                  </span>
                </div>
              </div>
            ))}
            {!showDetails && analysis.issues.length > 3 && (
              <button
                onClick={() => setShowDetails(true)}
                className="text-sm text-blue-600 hover:text-blue-700"
              >
                Show {analysis.issues.length - 3} more issues
              </button>
            )}
          </div>
        </div>
      )}

      {/* Suggestions */}
      {analysis.suggestions.length > 0 && (
        <div className="p-4 border-b">
          <h4 className="font-medium mb-3 flex items-center gap-2">
            <Lightbulb className="w-4 h-4 text-yellow-500" />
            Improvement Suggestions ({analysis.suggestions.length})
          </h4>
          <div className="space-y-2">
            {analysis.suggestions.slice(0, showDetails ? undefined : 3).map((suggestion, index) => (
              <div key={index} className="p-3 bg-gray-50 rounded-md border">
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <div className="font-medium text-sm">{suggestion.message}</div>
                    {suggestion.example && (
                      <div className="text-xs mt-1 text-gray-600">
                        Example: {suggestion.example}
                      </div>
                    )}
                  </div>
                  <span className={`text-xs font-medium ${getPriorityColor(suggestion.priority)}`}>
                    {suggestion.priority}
                  </span>
                </div>
              </div>
            ))}
            {!showDetails && analysis.suggestions.length > 3 && (
              <button
                onClick={() => setShowDetails(true)}
                className="text-sm text-blue-600 hover:text-blue-700"
              >
                Show {analysis.suggestions.length - 3} more suggestions
              </button>
            )}
          </div>
        </div>
      )}

      {/* Detailed Analysis (when expanded) */}
      {showDetails && (
        <div className="p-4 space-y-4">
          {/* Cultural Sensitivity Details */}
          <div>
            <h5 className="font-medium mb-2">Cultural Sensitivity</h5>
            <div className="text-sm space-y-1">
              <div>Score: {analysis.culturalSensitivity.score}/100</div>
              <div>Language Usage: {analysis.culturalSensitivity.kenyanContext.languageUsage}</div>
              {analysis.culturalSensitivity.kenyanContext.culturalReferences.length > 0 && (
                <div>
                  Cultural References: {analysis.culturalSensitivity.kenyanContext.culturalReferences.join(', ')}
                </div>
              )}
            </div>
          </div>

          {/* Readability Details */}
          <div>
            <h5 className="font-medium mb-2">Readability Analysis</h5>
            <div className="text-sm space-y-1">
              <div>Reading Level: {analysis.readability.level}</div>
              <div>Average Sentence Length: {analysis.readability.averageSentenceLength.toFixed(1)} words</div>
              <div>Complex Words: {analysis.readability.complexWords}</div>
            </div>
          </div>

          {/* SEO Analysis */}
          <div>
            <h5 className="font-medium mb-2">SEO Analysis</h5>
            <div className="text-sm space-y-1">
              <div>H1 Headings: {analysis.seoScore.headingStructure.h1Count}</div>
              <div>H2 Headings: {analysis.seoScore.headingStructure.h2Count}</div>
              <div>Proper Structure: {analysis.seoScore.headingStructure.proper ? 'Yes' : 'No'}</div>
            </div>
          </div>


        </div>
      )}
    </div>
  )
}
