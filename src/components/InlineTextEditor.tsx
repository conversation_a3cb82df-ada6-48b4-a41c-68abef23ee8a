'use client'

import { useState, useRef, useEffect } from 'react'
import { Check, X, Edit3 } from 'lucide-react'

interface InlineTextEditorProps {
  initialText: string
  onSave: (newText: string) => void
  onCancel?: () => void
  placeholder?: string
  multiline?: boolean
  className?: string
  language?: 'en' | 'sw'
}

export default function InlineTextEditor({
  initialText,
  onSave,
  onCancel,
  placeholder,
  multiline = false,
  className = '',
  language = 'en'
}: InlineTextEditorProps) {
  const [isEditing, setIsEditing] = useState(false)
  const [text, setText] = useState(initialText)
  const [isHovered, setIsHovered] = useState(false)
  const inputRef = useRef<HTMLInputElement | HTMLTextAreaElement>(null)

  useEffect(() => {
    if (isEditing && inputRef.current) {
      inputRef.current.focus()
      inputRef.current.select()
    }
  }, [isEditing])

  const handleSave = () => {
    onSave(text)
    setIsEditing(false)
  }

  const handleCancel = () => {
    setText(initialText)
    setIsEditing(false)
    onCancel?.()
  }

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !multiline) {
      e.preventDefault()
      handleSave()
    } else if (e.key === 'Escape') {
      handleCancel()
    } else if (e.key === 'Enter' && e.ctrlKey && multiline) {
      e.preventDefault()
      handleSave()
    }
  }

  if (isEditing) {
    return (
      <div className="relative inline-block w-full">
        {multiline ? (
          <textarea
            ref={inputRef as React.RefObject<HTMLTextAreaElement>}
            value={text}
            onChange={(e) => setText(e.target.value)}
            onKeyDown={handleKeyDown}
            placeholder={placeholder}
            className={`w-full p-2 border-2 border-blue-500 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-200 resize-none ${className}`}
            rows={3}
          />
        ) : (
          <input
            ref={inputRef as React.RefObject<HTMLInputElement>}
            type="text"
            value={text}
            onChange={(e) => setText(e.target.value)}
            onKeyDown={handleKeyDown}
            placeholder={placeholder}
            className={`w-full p-2 border-2 border-blue-500 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-200 ${className}`}
          />
        )}
        
        <div className="absolute right-2 top-2 flex items-center space-x-1">
          <button
            onClick={handleSave}
            className="p-1 text-green-600 hover:bg-green-100 rounded"
            title={language === 'sw' ? 'Hifadhi' : 'Save'}
          >
            <Check className="w-4 h-4" />
          </button>
          <button
            onClick={handleCancel}
            className="p-1 text-red-600 hover:bg-red-100 rounded"
            title={language === 'sw' ? 'Ghairi' : 'Cancel'}
          >
            <X className="w-4 h-4" />
          </button>
        </div>
        
        {multiline && (
          <div className="text-xs text-gray-500 mt-1">
            {language === 'sw' 
              ? 'Bonyeza Ctrl+Enter kuhifadhi, Escape kughairi'
              : 'Press Ctrl+Enter to save, Escape to cancel'
            }
          </div>
        )}
      </div>
    )
  }

  return (
    <div
      className={`relative inline-block cursor-pointer group ${className}`}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
      onClick={() => setIsEditing(true)}
    >
      <span className={`${isHovered ? 'bg-blue-50 rounded px-1' : ''} transition-colors`}>
        {text || placeholder}
      </span>
      
      {isHovered && (
        <div className="absolute -top-8 left-0 bg-black text-white text-xs px-2 py-1 rounded shadow-lg whitespace-nowrap z-10">
          <Edit3 className="w-3 h-3 inline mr-1" />
          {language === 'sw' ? 'Bofya kuhariri' : 'Click to edit'}
        </div>
      )}
    </div>
  )
}

// Higher-order component for making any element editable
export function makeEditable<T extends React.ComponentType<any>>(
  Component: T,
  textSelector: (props: any) => string,
  onTextChange: (props: any, newText: string) => void
) {
  return function EditableComponent(props: any) {
    const [isEditing, setIsEditing] = useState(false)
    const text = textSelector(props)

    const handleSave = (newText: string) => {
      onTextChange(props, newText)
      setIsEditing(false)
    }

    if (isEditing) {
      return (
        <InlineTextEditor
          initialText={text}
          onSave={handleSave}
          onCancel={() => setIsEditing(false)}
          language={props.language}
        />
      )
    }

    return (
      <div
        className="relative group cursor-pointer"
        onClick={() => setIsEditing(true)}
      >
        <Component {...props} />
        <div className="absolute inset-0 bg-blue-500 bg-opacity-0 group-hover:bg-opacity-10 transition-all duration-200 rounded" />
        <div className="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity">
          <Edit3 className="w-4 h-4 text-blue-600" />
        </div>
      </div>
    )
  }
}

// Editable heading component
export const EditableHeading = makeEditable(
  ({ children, className, ...props }: any) => (
    <h1 className={className} {...props}>{children}</h1>
  ),
  (props) => props.children,
  (props, newText) => props.onChange?.(newText)
)

// Editable paragraph component
export const EditableParagraph = makeEditable(
  ({ children, className, ...props }: any) => (
    <p className={className} {...props}>{children}</p>
  ),
  (props) => props.children,
  (props, newText) => props.onChange?.(newText)
)

// Editable button component
export const EditableButton = makeEditable(
  ({ children, className, ...props }: any) => (
    <button className={className} {...props}>{children}</button>
  ),
  (props) => props.children,
  (props, newText) => props.onChange?.(newText)
)
