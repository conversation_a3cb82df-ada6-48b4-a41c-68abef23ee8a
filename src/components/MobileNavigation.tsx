'use client'

import { useState, useEffect } from 'react'
import { Menu, X, Home, User, Settings, LogOut, Globe, Plus } from 'lucide-react'
import { useDeviceInfo } from './PWAProvider'

interface MobileNavigationProps {
  user?: any
  onLogin?: () => void
  onLogout?: () => void
  onDashboard?: () => void
  onCreateNew?: () => void
  currentPage?: 'home' | 'dashboard' | 'editor'
}

export default function MobileNavigation({
  user,
  onLogin,
  onLogout,
  onDashboard,
  onCreateNew,
  currentPage = 'home'
}: MobileNavigationProps) {
  const [isMenuOpen, setIsMenuOpen] = useState(false)
  const [isScrolled, setIsScrolled] = useState(false)
  const { isMobile } = useDeviceInfo()

  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 10)
    }

    window.addEventListener('scroll', handleScroll, { passive: true })
    return () => window.removeEventListener('scroll', handleScroll)
  }, [])

  const toggleMenu = () => {
    setIsMenuOpen(!isMenuOpen)
  }

  const closeMenu = () => {
    setIsMenuOpen(false)
  }

  const handleMenuItemClick = (action: () => void) => {
    action()
    closeMenu()
  }

  if (!isMobile) {
    return (
      <nav className="bg-white shadow-sm border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center space-x-4">
              <Globe className="w-8 h-8 text-blue-600" />
              <h1 className="text-xl font-bold text-gray-900">PagesLab</h1>
            </div>
            
            <div className="flex items-center space-x-4">
              {user ? (
                <>
                  <button
                    onClick={onDashboard}
                    className="text-gray-600 hover:text-gray-900 px-3 py-2 rounded-md text-sm font-medium"
                  >
                    Dashboard
                  </button>
                  <button
                    onClick={onCreateNew}
                    className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md text-sm font-medium"
                  >
                    Create New
                  </button>
                  <button
                    onClick={onLogout}
                    className="text-gray-600 hover:text-gray-900 px-3 py-2 rounded-md text-sm font-medium"
                  >
                    Logout
                  </button>
                </>
              ) : (
                <button
                  onClick={onLogin}
                  className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md text-sm font-medium"
                >
                  Login
                </button>
              )}
            </div>
          </div>
        </div>
      </nav>
    )
  }

  return (
    <>
      {/* Mobile Navigation Header */}
      <nav className={`mobile-nav transition-all duration-200 ${
        isScrolled ? 'shadow-md' : ''
      }`}>
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <Globe className="w-6 h-6 text-blue-600" />
            <h1 className="text-lg font-bold text-gray-900">PagesLab</h1>
          </div>
          
          <button
            onClick={toggleMenu}
            className="p-2 rounded-md text-gray-600 hover:text-gray-900 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-blue-500"
            aria-label="Toggle menu"
            aria-expanded={isMenuOpen}
          >
            {isMenuOpen ? (
              <X className="w-6 h-6" />
            ) : (
              <Menu className="w-6 h-6" />
            )}
          </button>
        </div>
      </nav>

      {/* Mobile Menu Overlay */}
      <div
        className={`mobile-overlay ${isMenuOpen ? 'open' : ''}`}
        onClick={closeMenu}
        aria-hidden="true"
      />

      {/* Mobile Menu */}
      <div className={`mobile-menu ${isMenuOpen ? 'open' : ''}`}>
        <div className="flex flex-col h-full">
          {/* Menu Header */}
          <div className="flex items-center justify-between mb-8">
            <div className="flex items-center space-x-3">
              <Globe className="w-8 h-8 text-blue-600" />
              <div>
                <h2 className="text-lg font-bold text-gray-900">PagesLab</h2>
                <p className="text-sm text-gray-600">AI Website Generator</p>
              </div>
            </div>
            <button
              onClick={closeMenu}
              className="p-2 rounded-md text-gray-600 hover:text-gray-900 hover:bg-gray-100"
              aria-label="Close menu"
            >
              <X className="w-6 h-6" />
            </button>
          </div>

          {/* User Info */}
          {user && (
            <div className="bg-blue-50 rounded-lg p-4 mb-6">
              <div className="flex items-center space-x-3">
                <div className="w-10 h-10 bg-blue-600 rounded-full flex items-center justify-center">
                  <User className="w-5 h-5 text-white" />
                </div>
                <div>
                  <p className="font-medium text-gray-900">{user.name}</p>
                  <p className="text-sm text-gray-600">{user.email}</p>
                </div>
              </div>
            </div>
          )}

          {/* Menu Items */}
          <div className="flex-1 space-y-2">
            <button
              onClick={() => handleMenuItemClick(() => window.location.href = '/')}
              className={`w-full flex items-center space-x-3 px-4 py-3 rounded-lg text-left transition-colors ${
                currentPage === 'home'
                  ? 'bg-blue-100 text-blue-700'
                  : 'text-gray-700 hover:bg-gray-100'
              }`}
            >
              <Home className="w-5 h-5" />
              <span className="font-medium">Home</span>
            </button>

            {user && (
              <>
                <button
                  onClick={() => handleMenuItemClick(onDashboard!)}
                  className={`w-full flex items-center space-x-3 px-4 py-3 rounded-lg text-left transition-colors ${
                    currentPage === 'dashboard'
                      ? 'bg-blue-100 text-blue-700'
                      : 'text-gray-700 hover:bg-gray-100'
                  }`}
                >
                  <User className="w-5 h-5" />
                  <span className="font-medium">My Dashboard</span>
                </button>

                <button
                  onClick={() => handleMenuItemClick(onCreateNew!)}
                  className="w-full flex items-center space-x-3 px-4 py-3 rounded-lg text-left text-gray-700 hover:bg-gray-100 transition-colors"
                >
                  <Plus className="w-5 h-5" />
                  <span className="font-medium">Create New Website</span>
                </button>

                <div className="border-t border-gray-200 my-4" />

                <button
                  onClick={() => handleMenuItemClick(() => {})}
                  className="w-full flex items-center space-x-3 px-4 py-3 rounded-lg text-left text-gray-700 hover:bg-gray-100 transition-colors"
                >
                  <Settings className="w-5 h-5" />
                  <span className="font-medium">Settings</span>
                </button>
              </>
            )}
          </div>

          {/* Menu Footer */}
          <div className="border-t border-gray-200 pt-4">
            {user ? (
              <button
                onClick={() => handleMenuItemClick(onLogout!)}
                className="w-full flex items-center space-x-3 px-4 py-3 rounded-lg text-left text-red-600 hover:bg-red-50 transition-colors"
              >
                <LogOut className="w-5 h-5" />
                <span className="font-medium">Logout</span>
              </button>
            ) : (
              <button
                onClick={() => handleMenuItemClick(onLogin!)}
                className="w-full bg-blue-600 hover:bg-blue-700 text-white font-medium py-3 px-4 rounded-lg transition-colors"
              >
                Login / Sign Up
              </button>
            )}
          </div>
        </div>
      </div>

      {/* Bottom Navigation for Mobile (Alternative) */}
      {user && (
        <div className="fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200 safe-bottom z-40 md:hidden">
          <div className="grid grid-cols-3 gap-1">
            <button
              onClick={() => window.location.href = '/'}
              className={`flex flex-col items-center py-2 px-1 text-xs transition-colors ${
                currentPage === 'home'
                  ? 'text-blue-600'
                  : 'text-gray-600 hover:text-gray-900'
              }`}
            >
              <Home className="w-5 h-5 mb-1" />
              <span>Home</span>
            </button>

            <button
              onClick={onDashboard}
              className={`flex flex-col items-center py-2 px-1 text-xs transition-colors ${
                currentPage === 'dashboard'
                  ? 'text-blue-600'
                  : 'text-gray-600 hover:text-gray-900'
              }`}
            >
              <User className="w-5 h-5 mb-1" />
              <span>Dashboard</span>
            </button>

            <button
              onClick={onCreateNew}
              className="flex flex-col items-center py-2 px-1 text-xs text-blue-600 hover:text-blue-700 transition-colors"
            >
              <div className="w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center mb-1">
                <Plus className="w-4 h-4 text-white" />
              </div>
              <span>Create</span>
            </button>
          </div>
        </div>
      )}

      {/* Add padding to body when bottom nav is present */}
      {user && (
        <style jsx global>{`
          body {
            padding-bottom: 80px;
          }
          
          @media (min-width: 768px) {
            body {
              padding-bottom: 0;
            }
          }
        `}</style>
      )}
    </>
  )
}
