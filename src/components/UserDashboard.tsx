'use client'

import { useState, useEffect } from 'react'
import { 
  Plus, 
  Globe, 
  Edit3, 
  Trash2, 
  Eye, 
  EyeOff, 
  Copy, 
  ExternalLink,
  Calendar,
  BarChart3,
  Settings,
  LogOut,
  Search,
  Filter
} from 'lucide-react'
import { useAuth, websiteAPI } from '@/lib/auth'

interface Website {
  id: string
  name: string
  description?: string
  subdomain: string
  isPublished: boolean
  createdAt: string
  updatedAt: string
  lastPublished?: string
  views: number
  html?: string
  css?: string
  businessProfile: {
    name: string
    type?: string
    location?: {
      area?: string
      county?: string
    }
    contactInfo?: {
      phone?: string
      email?: string
    }
  }
}

interface UserDashboardProps {
  onCreateNew: () => void
  onEditWebsite: (website: Website) => void
  language?: 'en' | 'sw'
}

export default function UserDashboard({ 
  onCreateNew, 
  onEditWebsite, 
  language = 'en' 
}: UserDashboardProps) {
  const { user, logout } = useAuth()
  const [websites, setWebsites] = useState<Website[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState('')
  const [searchTerm, setSearchTerm] = useState('')
  const [filterStatus, setFilterStatus] = useState<'all' | 'published' | 'draft'>('all')

  useEffect(() => {
    loadWebsites()
  }, [])

  const loadWebsites = async () => {
    console.log('🔄 UserDashboard: Loading websites...')
    setIsLoading(true)
    try {
      const result = await websiteAPI.getWebsites()
      console.log('🔄 UserDashboard: API result:', result)
      if (result.success) {
        console.log('✅ UserDashboard: Websites loaded successfully:', result.websites?.length || 0)
        setWebsites(result.websites)
      } else {
        console.error('❌ UserDashboard: Failed to load websites:', result.error)
        setError(result.error || 'Failed to load websites')
      }
    } catch (error) {
      console.error('❌ UserDashboard: Exception while loading websites:', error)
      setError('Failed to load websites')
    } finally {
      setIsLoading(false)
    }
  }

  const handlePublishToggle = async (website: Website) => {
    try {
      const result = website.isPublished 
        ? await websiteAPI.unpublishWebsite(website.id)
        : await websiteAPI.publishWebsite(website.id)
      
      if (result.success) {
        setWebsites(prev => prev.map(w => 
          w.id === website.id 
            ? { ...w, isPublished: !w.isPublished, lastPublished: !w.isPublished ? new Date().toISOString() : w.lastPublished }
            : w
        ))
      } else {
        setError(result.error || 'Failed to update website status')
      }
    } catch (error) {
      setError('Failed to update website status')
    }
  }

  const handleDelete = async (website: Website) => {
    if (!confirm(language === 'sw' 
      ? `Una uhakika unataka kufuta "${website.name}"?`
      : `Are you sure you want to delete "${website.name}"?`
    )) {
      return
    }

    try {
      const result = await websiteAPI.deleteWebsite(website.id)
      if (result.success) {
        setWebsites(prev => prev.filter(w => w.id !== website.id))
      } else {
        setError(result.error || 'Failed to delete website')
      }
    } catch (error) {
      setError('Failed to delete website')
    }
  }

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text)
    // You could add a toast notification here
  }

  const filteredWebsites = websites.filter(website => {
    const matchesSearch = website.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         website.businessProfile.name.toLowerCase().includes(searchTerm.toLowerCase())
    
    const matchesFilter = filterStatus === 'all' || 
                         (filterStatus === 'published' && website.isPublished) ||
                         (filterStatus === 'draft' && !website.isPublished)
    
    return matchesSearch && matchesFilter
  })

  const texts = {
    en: {
      dashboard: 'Dashboard',
      myWebsites: 'My Websites',
      createNew: 'Create New Website',
      search: 'Search websites...',
      all: 'All',
      published: 'Published',
      draft: 'Draft',
      noWebsites: 'No websites found',
      createFirst: 'Create your first website to get started',
      edit: 'Edit',
      delete: 'Delete',
      publish: 'Publish',
      unpublish: 'Unpublish',
      view: 'View',
      copy: 'Copy URL',
      createdOn: 'Created on',
      lastUpdated: 'Last updated',
      views: 'views',
      logout: 'Sign Out'
    },
    sw: {
      dashboard: 'Dashibodi',
      myWebsites: 'Tovuti Zangu',
      createNew: 'Tengeneza Tovuti Mpya',
      search: 'Tafuta tovuti...',
      all: 'Zote',
      published: 'Zilizochapishwa',
      draft: 'Rasimu',
      noWebsites: 'Hakuna tovuti zilizopatikana',
      createFirst: 'Tengeneza tovuti yako ya kwanza kuanza',
      edit: 'Hariri',
      delete: 'Futa',
      publish: 'Chapisha',
      unpublish: 'Sitisha Kuchapisha',
      view: 'Ona',
      copy: 'Nakili URL',
      createdOn: 'Imetengenezwa',
      lastUpdated: 'Imesasishwa mwisho',
      views: 'mionekano',
      logout: 'Toka'
    }
  }

  const t = texts[language]

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading dashboard...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center space-x-4">
              <h1 className="text-2xl font-bold text-gray-900">PagesLab</h1>
              <span className="text-gray-400">|</span>
              <h2 className="text-lg text-gray-600">{t.dashboard}</h2>
            </div>
            
            <div className="flex items-center space-x-4">
              <span className="text-sm text-gray-600">
                {language === 'sw' ? 'Karibu' : 'Welcome'}, {user?.name}
              </span>
              <button
                onClick={logout}
                className="flex items-center space-x-2 px-3 py-2 text-gray-600 hover:text-gray-900 rounded-lg hover:bg-gray-100"
              >
                <LogOut className="w-4 h-4" />
                <span className="hidden sm:inline">{t.logout}</span>
              </button>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Page Header */}
        <div className="flex items-center justify-between mb-8">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">{t.myWebsites}</h1>
            <p className="text-gray-600 mt-2">
              {language === 'sw' 
                ? 'Simamia na hariri tovuti zako zote mahali pamoja'
                : 'Manage and edit all your websites in one place'
              }
            </p>
          </div>
          
          <button
            onClick={onCreateNew}
            className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white font-semibold py-3 px-6 rounded-xl flex items-center space-x-2 transform hover:scale-105 transition-all duration-200 shadow-lg"
          >
            <Plus className="w-5 h-5" />
            <span>{t.createNew}</span>
          </button>
        </div>

        {/* Search and Filter */}
        <div className="flex flex-col sm:flex-row gap-4 mb-6">
          <div className="flex-1 relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
            <input
              type="text"
              placeholder={t.search}
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            />
          </div>
          
          <div className="flex items-center space-x-2">
            <Filter className="w-5 h-5 text-gray-400" />
            <select
              value={filterStatus}
              onChange={(e) => setFilterStatus(e.target.value as any)}
              className="px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            >
              <option value="all">{t.all}</option>
              <option value="published">{t.published}</option>
              <option value="draft">{t.draft}</option>
            </select>
          </div>
        </div>

        {error && (
          <div className="mb-6 p-4 bg-red-50 border border-red-200 rounded-lg">
            <p className="text-red-700">{error}</p>
          </div>
        )}

        {/* Websites Grid */}
        {filteredWebsites.length === 0 ? (
          <div className="text-center py-12">
            <Globe className="w-16 h-16 text-gray-300 mx-auto mb-4" />
            <h3 className="text-xl font-semibold text-gray-900 mb-2">{t.noWebsites}</h3>
            <p className="text-gray-600 mb-6">{t.createFirst}</p>
            <button
              onClick={onCreateNew}
              className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white font-semibold py-3 px-6 rounded-xl flex items-center space-x-2 mx-auto"
            >
              <Plus className="w-5 h-5" />
              <span>{t.createNew}</span>
            </button>
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {filteredWebsites.map((website) => (
              <WebsiteCard
                key={website.id}
                website={website}
                onEdit={() => onEditWebsite(website)}
                onDelete={() => handleDelete(website)}
                onPublishToggle={() => handlePublishToggle(website)}
                onCopyUrl={() => copyToClipboard(`https://${website.subdomain}.pageslab.co.ke`)}
                language={language}
                texts={t}
              />
            ))}
          </div>
        )}
      </main>
    </div>
  )
}

// Website Card Component
interface WebsiteCardProps {
  website: Website
  onEdit: () => void
  onDelete: () => void
  onPublishToggle: () => void
  onCopyUrl: () => void
  language: 'en' | 'sw'
  texts: any
}

function WebsiteCard({ 
  website, 
  onEdit, 
  onDelete, 
  onPublishToggle, 
  onCopyUrl, 
  language, 
  texts 
}: WebsiteCardProps) {
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString(language === 'sw' ? 'sw-KE' : 'en-KE')
  }

  return (
    <div className="bg-white rounded-xl shadow-lg border border-gray-200 overflow-hidden hover:shadow-xl transition-shadow duration-200">
      {/* Website Preview */}
      <div className="h-48 bg-gradient-to-br from-blue-50 to-purple-50 flex items-center justify-center relative">
        <Globe className="w-16 h-16 text-gray-300" />
        <div className={`absolute top-3 right-3 px-2 py-1 rounded-full text-xs font-medium ${
          website.isPublished 
            ? 'bg-green-100 text-green-800' 
            : 'bg-yellow-100 text-yellow-800'
        }`}>
          {website.isPublished ? texts.published : texts.draft}
        </div>
      </div>

      {/* Content */}
      <div className="p-6">
        <h3 className="text-xl font-bold text-gray-900 mb-2">{website.name}</h3>
        <p className="text-gray-600 text-sm mb-4 line-clamp-2">
          {website.description || website.businessProfile.name}
        </p>

        {/* URL */}
        <div className="flex items-center space-x-2 mb-4 p-2 bg-gray-50 rounded-lg">
          <span className="text-sm text-gray-600 font-mono truncate">
            {website.subdomain}.pageslab.co.ke
          </span>
          <button
            onClick={onCopyUrl}
            className="p-1 text-gray-400 hover:text-gray-600"
            title={texts.copy}
          >
            <Copy className="w-4 h-4" />
          </button>
        </div>

        {/* Stats */}
        <div className="flex items-center justify-between text-sm text-gray-500 mb-4">
          <div className="flex items-center space-x-1">
            <BarChart3 className="w-4 h-4" />
            <span>{website.views} {texts.views}</span>
          </div>
          <div className="flex items-center space-x-1">
            <Calendar className="w-4 h-4" />
            <span>{formatDate(website.updatedAt)}</span>
          </div>
        </div>

        {/* Actions */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <button
              onClick={onEdit}
              className="p-2 text-blue-600 hover:bg-blue-50 rounded-lg"
              title={texts.edit}
            >
              <Edit3 className="w-4 h-4" />
            </button>

            <button
              onClick={() => {
                const newWindow = window.open('', '_blank');
                if (newWindow && website.html && website.css) {
                  newWindow.document.write(`
                    <html>
                      <head>
                        <title>${website.businessProfile?.name || 'Website'}</title>
                        <style>${website.css}</style>
                        <meta name="viewport" content="width=device-width, initial-scale=1.0">
                      </head>
                      <body>
                        ${website.html}
                      </body>
                    </html>
                  `);
                  newWindow.document.close();
                }
              }}
              className="p-2 text-purple-600 hover:bg-purple-50 rounded-lg"
              title="Preview Website"
            >
              <ExternalLink className="w-4 h-4" />
            </button>
            
            {website.isPublished && (
              <a
                href={`https://${website.subdomain}.pageslab.co.ke`}
                target="_blank"
                rel="noopener noreferrer"
                className="p-2 text-green-600 hover:bg-green-50 rounded-lg"
                title={texts.view}
              >
                <ExternalLink className="w-4 h-4" />
              </a>
            )}
            
            <button
              onClick={onDelete}
              className="p-2 text-red-600 hover:bg-red-50 rounded-lg"
              title={texts.delete}
            >
              <Trash2 className="w-4 h-4" />
            </button>
          </div>

          <button
            onClick={onPublishToggle}
            className={`flex items-center space-x-1 px-3 py-1 rounded-lg text-sm font-medium ${
              website.isPublished
                ? 'bg-yellow-100 text-yellow-800 hover:bg-yellow-200'
                : 'bg-green-100 text-green-800 hover:bg-green-200'
            }`}
          >
            {website.isPublished ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
            <span>{website.isPublished ? texts.unpublish : texts.publish}</span>
          </button>
        </div>
      </div>
    </div>
  )
}
