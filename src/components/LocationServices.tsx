'use client'

import { useState } from 'react'
import { MapPin, Map, Navigation } from 'lucide-react'

interface LocationData {
  area: string
  county: string
  region?: string
  coordinates?: {
    lat: number
    lng: number
  }
  landmark?: string
  directions?: string
}

interface LocationServicesProps {
  location: LocationData
  businessName?: string
  className?: string
  showMap?: boolean
  showDirections?: boolean
}

export default function LocationServices({ 
  location, 
  businessName = 'Business',
  className = '',
  showMap = true,
  showDirections = true
}: LocationServicesProps) {
  const [isExpanded, setIsExpanded] = useState(false)

  const formattedAddress = formatKenyanAddress(location)
  const googleMapsUrl = generateGoogleMapsUrl(location, businessName)
  const directionsUrl = generateDirectionsUrl(location)

  return (
    <div className={`location-services ${className}`}>
      <div className="mb-6">
        <h3 className="text-xl font-bold text-gray-900 mb-2">Our Location</h3>
        <p className="text-gray-600">Find us easily with these location details</p>
      </div>

      {/* Address Display */}
      <div className="bg-white border border-gray-200 rounded-xl p-6 mb-4">
        <div className="flex items-start space-x-4">
          <div className="text-3xl">
            <MapPin className="w-8 h-8 text-red-500" />
          </div>
          <div className="flex-1">
            <h4 className="font-semibold text-gray-900 mb-2">{businessName}</h4>
            <div className="text-gray-700 space-y-1">
              <p>{location.area}</p>
              <p>{location.county} County</p>
              {location.region && <p className="text-sm text-gray-500">{location.region} Region</p>}
              {location.landmark && (
                <p className="text-sm text-blue-600">
                  <span className="font-medium">Landmark:</span> {location.landmark}
                </p>
              )}
            </div>
          </div>
        </div>

        {location.directions && (
          <div className="mt-4 p-3 bg-blue-50 rounded-lg border border-blue-200">
            <button
              onClick={() => setIsExpanded(!isExpanded)}
              className="flex items-center justify-between w-full text-left"
            >
              <span className="font-medium text-blue-900">Directions</span>
              <span className="text-blue-600">{isExpanded ? '▼' : '▶'}</span>
            </button>
            {isExpanded && (
              <div className="mt-2 text-sm text-blue-800">
                {location.directions}
              </div>
            )}
          </div>
        )}
      </div>

      {/* Action Buttons */}
      <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
        {showMap && (
          <a
            href={googleMapsUrl}
            target="_blank"
            rel="noopener noreferrer"
            className="
              flex items-center justify-center space-x-2 p-4
              bg-red-500 hover:bg-red-600 text-white font-semibold rounded-xl
              transform transition-all duration-200 hover:scale-105 hover:shadow-lg
            "
          >
            <Map className="w-6 h-6" />
            <span>View on Google Maps</span>
          </a>
        )}

        {showDirections && (
          <a
            href={directionsUrl}
            target="_blank"
            rel="noopener noreferrer"
            className="
              flex items-center justify-center space-x-2 p-4
              bg-blue-500 hover:bg-blue-600 text-white font-semibold rounded-xl
              transform transition-all duration-200 hover:scale-105 hover:shadow-lg
            "
          >
            <Navigation className="w-6 h-6" />
            <span>Get Directions</span>
          </a>
        )}
      </div>

      {/* Additional Location Info */}
      <div className="mt-6 grid grid-cols-1 md:grid-cols-2 gap-4">
        <div className="p-4 bg-gray-50 rounded-lg">
          <h5 className="font-semibold text-gray-900 mb-2">County Information</h5>
          <p className="text-sm text-gray-600">
            Located in {location.county} County, {getCountyDescription(location.county)}
          </p>
        </div>

        <div className="p-4 bg-gray-50 rounded-lg">
          <h5 className="font-semibold text-gray-900 mb-2">Transportation</h5>
          <p className="text-sm text-gray-600">
            {getTransportationInfo(location.county)}
          </p>
        </div>
      </div>
    </div>
  )
}

// Utility function to format Kenyan addresses
export function formatKenyanAddress(location: LocationData): string {
  const parts = []
  
  if (location.area) parts.push(location.area)
  if (location.county) parts.push(`${location.county} County`)
  if (location.region) parts.push(`${location.region} Region`)
  parts.push('Kenya')
  
  return parts.join(', ')
}

// Generate Google Maps URL
export function generateGoogleMapsUrl(location: LocationData, businessName?: string): string {
  const query = businessName 
    ? `${businessName}, ${formatKenyanAddress(location)}`
    : formatKenyanAddress(location)
  
  return `https://www.google.com/maps/search/?api=1&query=${encodeURIComponent(query)}`
}

// Generate directions URL
export function generateDirectionsUrl(location: LocationData): string {
  const destination = formatKenyanAddress(location)
  return `https://www.google.com/maps/dir/?api=1&destination=${encodeURIComponent(destination)}`
}

// Get county description
function getCountyDescription(county: string): string {
  const descriptions: Record<string, string> = {
    'Nairobi': 'the capital city and major business hub of Kenya.',
    'Mombasa': 'the coastal county known for its port and tourism.',
    'Kisumu': 'the lakeside county and commercial center of western Kenya.',
    'Nakuru': 'known for its lakes and agricultural activities.',
    'Eldoret': 'the highland county famous for athletics and agriculture.',
    'Thika': 'an industrial town in central Kenya.',
    'Malindi': 'a coastal town popular with tourists.',
    'Kitale': 'an agricultural town in western Kenya.',
    'Garissa': 'a town in northeastern Kenya.',
    'Kakamega': 'the headquarters of the former Western Province.'
  }
  
  return descriptions[county] || 'a vibrant county in Kenya.'
}

// Get transportation information
function getTransportationInfo(county: string): string {
  const transportInfo: Record<string, string> = {
    'Nairobi': 'Accessible by matatu, bus, taxi, and ride-sharing services.',
    'Mombasa': 'Reachable by matatu, tuk-tuk, taxi, and coastal transport.',
    'Kisumu': 'Connected by matatu, boda-boda, and lake transport.',
    'Nakuru': 'Accessible by matatu, bus, and private transport.',
    'Eldoret': 'Connected by matatu, bus, and regional transport.'
  }
  
  return transportInfo[county] || 'Accessible by local matatu and transport services.'
}

// Simple location display component for websites
export function SimpleLocationDisplay({ location, businessName }: { location: LocationData, businessName?: string }) {
  const googleMapsUrl = generateGoogleMapsUrl(location, businessName)
  
  return (
    <div className="simple-location bg-gray-50 p-4 rounded-lg">
      <div className="flex items-center space-x-3 mb-3">
        <MapPin className="w-6 h-6 text-red-500" />
        <div>
          <h4 className="font-semibold text-gray-900">Visit Us</h4>
          <p className="text-sm text-gray-600">{formatKenyanAddress(location)}</p>
        </div>
      </div>
      
      <a
        href={googleMapsUrl}
        target="_blank"
        rel="noopener noreferrer"
        className="
          inline-flex items-center space-x-2 text-blue-600 hover:text-blue-800
          font-medium text-sm transition-colors duration-200
        "
      >
        <Map className="w-4 h-4" />
        <span>View on Map</span>
      </a>
    </div>
  )
}

// Embedded map component (placeholder for future Google Maps integration)
export function EmbeddedMap({ location, businessName, height = '300px' }: { 
  location: LocationData
  businessName?: string
  height?: string 
}) {
  const mapUrl = `https://www.google.com/maps/embed/v1/place?key=YOUR_API_KEY&q=${encodeURIComponent(formatKenyanAddress(location))}`
  
  return (
    <div className="embedded-map">
      <div 
        className="bg-gray-200 rounded-lg flex items-center justify-center text-gray-500"
        style={{ height }}
      >
        <div className="text-center">
          <div className="flex justify-center mb-2">
            <Map className="w-12 h-12 text-gray-400" />
          </div>
          <p className="font-medium">Interactive Map</p>
          <p className="text-sm">{formatKenyanAddress(location)}</p>
          <a
            href={generateGoogleMapsUrl(location, businessName)}
            target="_blank"
            rel="noopener noreferrer"
            className="inline-block mt-2 text-blue-600 hover:text-blue-800 font-medium"
          >
            Open in Google Maps →
          </a>
        </div>
      </div>
    </div>
  )
}
