'use client'

import { useState, useEffect } from 'react'
import { Image as ImageIcon, Upload, Search, Grid, List, Eye, Edit3, Trash2, Download, RefreshCw, AlertCircle } from 'lucide-react'
import DragDropImageUpload from './DragDropImageUpload'

interface WebsiteImage {
  id: string
  src: string
  alt: string
  title?: string
  section: string
  type: 'hero' | 'gallery' | 'content' | 'background' | 'icon'
  dimensions?: { width: number; height: number }
  size?: number
  element?: HTMLImageElement
}

interface ImageManagerProps {
  websiteHtml: string
  onImageReplace: (imageId: string, newImageUrl: string) => void
  onImageRemove: (imageId: string) => void
  onClose?: () => void
  language?: 'en' | 'sw'
  className?: string
}

export default function ImageManager({
  websiteHtml,
  onImageReplace,
  onImageRemove,
  onClose,
  language = 'en',
  className = ''
}: ImageManagerProps) {
  const [images, setImages] = useState<WebsiteImage[]>([])
  const [filteredImages, setFilteredImages] = useState<WebsiteImage[]>([])
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedType, setSelectedType] = useState<string>('all')
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid')
  const [selectedImage, setSelectedImage] = useState<WebsiteImage | null>(null)
  const [isReplacing, setIsReplacing] = useState(false)
  const [loading, setLoading] = useState(true)

  // Extract images from HTML
  useEffect(() => {
    extractImagesFromHtml()
  }, [websiteHtml])

  // Filter images based on search and type
  useEffect(() => {
    let filtered = images

    if (searchTerm) {
      filtered = filtered.filter(img => 
        img.alt.toLowerCase().includes(searchTerm.toLowerCase()) ||
        img.section.toLowerCase().includes(searchTerm.toLowerCase()) ||
        img.title?.toLowerCase().includes(searchTerm.toLowerCase())
      )
    }

    if (selectedType !== 'all') {
      filtered = filtered.filter(img => img.type === selectedType)
    }

    setFilteredImages(filtered)
  }, [images, searchTerm, selectedType])

  const extractImagesFromHtml = () => {
    setLoading(true)
    try {
      // Create a temporary DOM to parse HTML
      const parser = new DOMParser()
      const doc = parser.parseFromString(websiteHtml, 'text/html')
      const imgElements = doc.querySelectorAll('img')

      const extractedImages: WebsiteImage[] = []

      imgElements.forEach((img, index) => {
        const src = img.src || img.getAttribute('data-src') || ''
        if (!src || src.startsWith('data:image/svg')) return // Skip SVG data URLs

        // Determine section based on parent elements
        const section = determineImageSection(img)
        const type = determineImageType(img, section)

        extractedImages.push({
          id: `img-${index}`,
          src,
          alt: img.alt || `Image ${index + 1}`,
          title: img.title,
          section,
          type,
          dimensions: {
            width: img.naturalWidth || parseInt(img.getAttribute('width') || '0'),
            height: img.naturalHeight || parseInt(img.getAttribute('height') || '0')
          },
          element: img
        })
      })

      // Also extract background images from CSS
      const allElements = doc.querySelectorAll('*')
      allElements.forEach((element, index) => {
        const style = element.getAttribute('style') || ''
        const backgroundImageMatch = style.match(/background-image:\s*url\(['"]?([^'"]+)['"]?\)/)

        if (backgroundImageMatch && backgroundImageMatch[1]) {
          const src = backgroundImageMatch[1]
          if (!src.startsWith('data:image/svg')) {
            const section = determineImageSection(element)
            const type = 'background'

            extractedImages.push({
              id: `bg-${index}`,
              src,
              alt: `Background image ${index + 1}`,
              title: 'Background Image',
              section,
              type,
              dimensions: { width: 0, height: 0 },
              element: element as HTMLElement
            })
          }
        }
      })

      setImages(extractedImages)
    } catch (error) {
      console.error('Error extracting images:', error)
    } finally {
      setLoading(false)
    }
  }

  const determineImageSection = (img: HTMLImageElement): string => {
    let parent = img.parentElement
    let depth = 0
    
    while (parent && depth < 10) {
      const className = parent.className?.toLowerCase() || ''
      const id = parent.id?.toLowerCase() || ''
      
      if (className.includes('hero') || id.includes('hero')) return 'Hero Section'
      if (className.includes('about') || id.includes('about')) return 'About Section'
      if (className.includes('service') || id.includes('service')) return 'Services Section'
      if (className.includes('gallery') || id.includes('gallery')) return 'Gallery Section'
      if (className.includes('testimonial') || id.includes('testimonial') || className.includes('review') || className.includes('client')) return 'Testimonials Section'
      if (className.includes('contact') || id.includes('contact')) return 'Contact Section'
      if (className.includes('footer') || id.includes('footer')) return 'Footer Section'
      if (className.includes('header') || id.includes('header')) return 'Header Section'
      
      parent = parent.parentElement
      depth++
    }
    
    return 'Content Section'
  }

  const determineImageType = (img: HTMLImageElement, section: string): WebsiteImage['type'] => {
    const className = img.className?.toLowerCase() || ''
    const src = img.src?.toLowerCase() || ''
    const alt = img.alt?.toLowerCase() || ''

    if (section.includes('Hero') || className.includes('hero')) return 'hero'
    if (className.includes('icon') || src.includes('icon') || alt.includes('icon')) return 'icon'
    if (className.includes('background') || img.style.position === 'absolute') return 'background'
    if (section.includes('Gallery') || className.includes('gallery')) return 'gallery'
    if (section.includes('Testimonials') || className.includes('testimonial') || className.includes('review') || className.includes('client') || alt.includes('testimonial') || alt.includes('client')) return 'content'

    return 'content'
  }

  const handleImageUpload = async (file: File, optimizedUrl: string) => {
    if (!selectedImage) return

    setIsReplacing(true)
    try {
      // Call the parent component's replace function
      await onImageReplace(selectedImage.id, optimizedUrl)
      
      // Update local state
      setImages(prev => prev.map(img => 
        img.id === selectedImage.id 
          ? { ...img, src: optimizedUrl }
          : img
      ))
      
      setSelectedImage(null)
    } catch (error) {
      console.error('Error replacing image:', error)
    } finally {
      setIsReplacing(false)
    }
  }

  const handleRemoveImage = async (image: WebsiteImage) => {
    if (window.confirm(language === 'sw' ? 'Una uhakika unataka kuondoa picha hii?' : 'Are you sure you want to remove this image?')) {
      try {
        await onImageRemove(image.id)
        setImages(prev => prev.filter(img => img.id !== image.id))
      } catch (error) {
        console.error('Error removing image:', error)
      }
    }
  }

  const getTypeColor = (type: WebsiteImage['type']) => {
    switch (type) {
      case 'hero': return 'bg-purple-100 text-purple-800'
      case 'gallery': return 'bg-blue-100 text-blue-800'
      case 'content': return 'bg-green-100 text-green-800'
      case 'background': return 'bg-gray-100 text-gray-800'
      case 'icon': return 'bg-yellow-100 text-yellow-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  const imageTypes = [
    { value: 'all', label: language === 'sw' ? 'Zote' : 'All' },
    { value: 'hero', label: language === 'sw' ? 'Picha Kuu' : 'Hero' },
    { value: 'gallery', label: language === 'sw' ? 'Galeri' : 'Gallery' },
    { value: 'content', label: language === 'sw' ? 'Maudhui' : 'Content' },
    { value: 'background', label: language === 'sw' ? 'Mandhari' : 'Background' },
    { value: 'icon', label: language === 'sw' ? 'Ikoni' : 'Icons' }
  ]

  if (loading) {
    return (
      <div className={`bg-white rounded-xl p-6 ${className}`}>
        <div className="flex items-center justify-center py-12">
          <RefreshCw className="w-8 h-8 text-blue-500 animate-spin mr-3" />
          <span className="text-gray-600">
            {language === 'sw' ? 'Inapakia picha...' : 'Loading images...'}
          </span>
        </div>
      </div>
    )
  }

  return (
    <div className={`bg-white rounded-xl border border-gray-200 ${className}`}>
      {/* Header */}
      <div className="p-6 border-b border-gray-200">
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center">
            <ImageIcon className="w-6 h-6 text-blue-600 mr-3" />
            <h3 className="text-xl font-semibold text-gray-900">
              {language === 'sw' ? 'Msimamizi wa Picha' : 'Image Manager'}
            </h3>
            <span className="ml-3 px-2 py-1 bg-blue-100 text-blue-800 text-sm rounded-full">
              {images.length} {language === 'sw' ? 'picha' : 'images'}
            </span>
          </div>
          
          <div className="flex items-center space-x-2">
            <button
              onClick={() => setViewMode(viewMode === 'grid' ? 'list' : 'grid')}
              className="p-2 text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded-lg transition-colors"
            >
              {viewMode === 'grid' ? <List className="w-5 h-5" /> : <Grid className="w-5 h-5" />}
            </button>
          </div>
        </div>

        {/* Search and Filters */}
        <div className="flex flex-col sm:flex-row gap-4">
          <div className="relative flex-1">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
            <input
              type="text"
              placeholder={language === 'sw' ? 'Tafuta picha...' : 'Search images...'}
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            />
          </div>
          
          <select
            value={selectedType}
            onChange={(e) => setSelectedType(e.target.value)}
            className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          >
            {imageTypes.map(type => (
              <option key={type.value} value={type.value}>
                {type.label}
              </option>
            ))}
          </select>
        </div>
      </div>

      {/* Images Grid/List */}
      <div className="p-6">
        {filteredImages.length === 0 ? (
          <div className="text-center py-12">
            <ImageIcon className="w-16 h-16 text-gray-300 mx-auto mb-4" />
            <h4 className="text-lg font-medium text-gray-900 mb-2">
              {language === 'sw' ? 'Hakuna picha zilizopatikana' : 'No images found'}
            </h4>
            <p className="text-gray-500">
              {searchTerm || selectedType !== 'all' 
                ? (language === 'sw' ? 'Jaribu kubadilisha vichungi vyako' : 'Try adjusting your filters')
                : (language === 'sw' ? 'Tovuti hii haina picha' : 'This website has no images')
              }
            </p>
          </div>
        ) : (
          <div className={viewMode === 'grid' 
            ? 'grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6'
            : 'space-y-4'
          }>
            {filteredImages.map((image) => (
              <div
                key={image.id}
                className={`border border-gray-200 rounded-lg overflow-hidden hover:shadow-lg transition-shadow ${
                  viewMode === 'list' ? 'flex items-center p-4' : 'bg-white'
                }`}
              >
                {/* Image Preview */}
                <div className={viewMode === 'list' ? 'w-20 h-20 flex-shrink-0 mr-4' : 'aspect-video'}>
                  <img
                    src={image.src}
                    alt={image.alt}
                    className="w-full h-full object-cover rounded"
                    onError={(e) => {
                      const target = e.target as HTMLImageElement
                      target.src = '/placeholder-image.jpg'
                    }}
                  />
                </div>

                {/* Image Info */}
                <div className={viewMode === 'list' ? 'flex-1 min-w-0' : 'p-4'}>
                  <div className="flex items-start justify-between mb-2">
                    <h4 className="font-medium text-gray-900 truncate">
                      {image.alt || `Image ${image.id}`}
                    </h4>
                    <span className={`px-2 py-1 text-xs rounded-full ${getTypeColor(image.type)}`}>
                      {image.type}
                    </span>
                  </div>
                  
                  <p className="text-sm text-gray-600 mb-2">{image.section}</p>
                  
                  {image.dimensions && (
                    <p className="text-xs text-gray-500 mb-3">
                      {image.dimensions.width} × {image.dimensions.height}
                    </p>
                  )}

                  {/* Action Buttons */}
                  <div className={`flex gap-1 ${viewMode === 'grid' ? 'justify-center' : ''}`}>
                    <button
                      onClick={() => setSelectedImage(image)}
                      className="p-1.5 bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors shadow-sm"
                      title={language === 'sw' ? 'Badilisha' : 'Replace'}
                    >
                      <Edit3 className="w-3 h-3" />
                    </button>

                    <button
                      onClick={() => window.open(image.src, '_blank')}
                      className="p-1.5 bg-gray-100 text-gray-700 rounded hover:bg-gray-200 transition-colors shadow-sm border border-gray-300"
                      title={language === 'sw' ? 'Ona' : 'View'}
                    >
                      <Eye className="w-3 h-3" />
                    </button>

                    <button
                      onClick={() => handleRemoveImage(image)}
                      className="p-1.5 bg-red-100 text-red-700 rounded hover:bg-red-200 transition-colors shadow-sm border border-red-300"
                      title={language === 'sw' ? 'Ondoa' : 'Remove'}
                    >
                      <Trash2 className="w-3 h-3" />
                    </button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>

      {/* Image Replacement Modal */}
      {selectedImage && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto">
            <div className="p-6 border-b border-gray-200">
              <div className="flex items-center justify-between">
                <h3 className="text-lg font-semibold text-gray-900">
                  {language === 'sw' ? 'Badilisha Picha' : 'Replace Image'}
                </h3>
                <button
                  onClick={() => setSelectedImage(null)}
                  className="text-gray-400 hover:text-gray-600"
                >
                  ×
                </button>
              </div>
            </div>
            
            <div className="p-6">
              <div className="mb-6">
                <h4 className="font-medium text-gray-900 mb-2">
                  {language === 'sw' ? 'Picha ya Sasa' : 'Current Image'}
                </h4>
                <div className="border border-gray-200 rounded-lg p-4">
                  <img
                    src={selectedImage.src}
                    alt={selectedImage.alt}
                    className="w-full h-48 object-cover rounded mb-3"
                  />
                  <div className="text-sm text-gray-600">
                    <p><strong>{language === 'sw' ? 'Sehemu:' : 'Section:'}</strong> {selectedImage.section}</p>
                    <p><strong>{language === 'sw' ? 'Aina:' : 'Type:'}</strong> {selectedImage.type}</p>
                    {selectedImage.dimensions && (
                      <p><strong>{language === 'sw' ? 'Ukubwa:' : 'Dimensions:'}</strong> {selectedImage.dimensions.width} × {selectedImage.dimensions.height}</p>
                    )}
                  </div>
                </div>
              </div>

              <div>
                <h4 className="font-medium text-gray-900 mb-4">
                  {language === 'sw' ? 'Pakia Picha Mpya' : 'Upload New Image'}
                </h4>
                <DragDropImageUpload
                  onImageUpload={handleImageUpload}
                  language={language}
                  className="mb-4"
                />
              </div>

              {isReplacing && (
                <div className="flex items-center justify-center py-4">
                  <RefreshCw className="w-5 h-5 text-blue-500 animate-spin mr-2" />
                  <span className="text-gray-600">
                    {language === 'sw' ? 'Inabadilisha picha...' : 'Replacing image...'}
                  </span>
                </div>
              )}
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
