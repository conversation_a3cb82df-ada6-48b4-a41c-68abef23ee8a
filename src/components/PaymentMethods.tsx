'use client'

import { useState } from 'react'
import { Smartphone, CreditCard, Building2, Banknote, Lightbulb } from 'lucide-react'

interface PaymentMethod {
  id: string
  name: string
  type: 'mobile_money' | 'bank_transfer' | 'cash'
  icon: React.ComponentType<{ className?: string }>
  description: string
  instructions: string
  isPopular?: boolean
}

interface PaymentMethodsProps {
  businessProfile?: any
  onPaymentSelect?: (method: PaymentMethod) => void
  className?: string
}

export default function PaymentMethods({ businessProfile, onPaymentSelect, className = '' }: PaymentMethodsProps) {
  const [selectedMethod, setSelectedMethod] = useState<string | null>(null)

  const paymentMethods: PaymentMethod[] = [
    {
      id: 'mpesa',
      name: 'M-Pesa',
      type: 'mobile_money',
      icon: Smartphone,
      description: 'Pay with M-Pesa mobile money',
      instructions: 'Send money to our M-Pesa number',
      isPopular: true
    },
    {
      id: 'airtel_money',
      name: 'Airtel Money',
      type: 'mobile_money',
      icon: Smartphone,
      description: 'Pay with Airtel Money',
      instructions: 'Use Airtel Money to complete payment'
    },
    {
      id: 'tkash',
      name: 'T-<PERSON><PERSON>',
      type: 'mobile_money',
      icon: CreditCard,
      description: 'Pay with T-Kash mobile wallet',
      instructions: 'Transfer via T-Kash mobile wallet'
    },
    {
      id: 'bank_transfer',
      name: 'Bank Transfer',
      type: 'bank_transfer',
      icon: Building2,
      description: 'Direct bank transfer',
      instructions: 'Transfer to our bank account'
    },
    {
      id: 'cash',
      name: 'Cash Payment',
      type: 'cash',
      icon: Banknote,
      description: 'Pay in cash at our location',
      instructions: 'Visit us to pay in cash'
    }
  ]

  const handleMethodSelect = (method: PaymentMethod) => {
    setSelectedMethod(method.id)
    onPaymentSelect?.(method)
  }

  return (
    <div className={`payment-methods ${className}`}>
      <div className="mb-6">
        <h3 className="text-xl font-bold text-gray-900 mb-2">Payment Methods</h3>
        <p className="text-gray-600">Choose your preferred payment method</p>
      </div>

      <div className="grid gap-4">
        {paymentMethods.map((method) => (
          <div
            key={method.id}
            onClick={() => handleMethodSelect(method)}
            className={`
              relative p-4 border-2 rounded-xl cursor-pointer transition-all duration-200
              ${selectedMethod === method.id 
                ? 'border-primary-500 bg-primary-50' 
                : 'border-gray-200 hover:border-primary-300 hover:bg-gray-50'
              }
              ${method.isPopular ? 'ring-2 ring-green-200' : ''}
            `}
          >
            {method.isPopular && (
              <div className="absolute -top-2 left-4">
                <span className="bg-green-500 text-white text-xs font-bold px-2 py-1 rounded-full">
                  Most Popular
                </span>
              </div>
            )}

            <div className="flex items-center space-x-4">
              <div className="text-3xl">
                <method.icon className="w-8 h-8" />
              </div>
              <div className="flex-1">
                <div className="flex items-center space-x-2">
                  <h4 className="font-semibold text-gray-900">{method.name}</h4>
                  {method.type === 'mobile_money' && (
                    <span className="bg-blue-100 text-blue-800 text-xs font-medium px-2 py-1 rounded-full">
                      Mobile Money
                    </span>
                  )}
                </div>
                <p className="text-sm text-gray-600 mt-1">{method.description}</p>
                {selectedMethod === method.id && (
                  <div className="mt-3 p-3 bg-white rounded-lg border border-primary-200">
                    <p className="text-sm font-medium text-primary-800">{method.instructions}</p>
                    {method.id === 'mpesa' && businessProfile?.contactInfo?.phone && (
                      <div className="mt-2 p-2 bg-green-50 rounded border border-green-200">
                        <p className="text-sm text-green-800">
                          <strong>M-Pesa Number:</strong> {businessProfile.contactInfo.phone}
                        </p>
                        <p className="text-xs text-green-600 mt-1">
                          Send payment and share transaction code
                        </p>
                      </div>
                    )}
                  </div>
                )}
              </div>
              <div className="text-primary-500">
                {selectedMethod === method.id ? '✓' : '→'}
              </div>
            </div>
          </div>
        ))}
      </div>

      {selectedMethod && (
        <div className="mt-6 p-4 bg-blue-50 rounded-xl border border-blue-200">
          <div className="flex items-start space-x-3">
            <div className="text-blue-500 text-xl">
              <Lightbulb className="w-6 h-6" />
            </div>
            <div>
              <h4 className="font-semibold text-blue-900">Payment Instructions</h4>
              <p className="text-sm text-blue-800 mt-1">
                After making payment, please contact us with your transaction details for confirmation.
              </p>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}

// Utility function to format Kenyan phone numbers for M-Pesa
export function formatKenyanPhone(phone: string): string {
  // Remove all non-digits
  const digits = phone.replace(/\D/g, '')
  
  // Handle different formats
  if (digits.startsWith('254')) {
    return `+${digits}`
  } else if (digits.startsWith('0')) {
    return `+254${digits.slice(1)}`
  } else if (digits.length === 9) {
    return `+254${digits}`
  }
  
  return phone
}

// Component for displaying payment methods in generated websites
export function PaymentMethodDisplay({ methods, businessPhone }: { methods: string[], businessPhone?: string }) {
  return (
    <div className="payment-display">
      <h3 className="font-bold text-lg mb-4">We Accept</h3>
      <div className="flex flex-wrap gap-3">
        {methods.includes('mpesa') && (
          <div className="flex items-center space-x-2 bg-green-100 text-green-800 px-3 py-2 rounded-lg">
            <Smartphone className="w-4 h-4" />
            <span className="font-medium">M-Pesa</span>
          </div>
        )}
        {methods.includes('airtel_money') && (
          <div className="flex items-center space-x-2 bg-red-100 text-red-800 px-3 py-2 rounded-lg">
            <Smartphone className="w-4 h-4" />
            <span className="font-medium">Airtel Money</span>
          </div>
        )}
        {methods.includes('bank_transfer') && (
          <div className="flex items-center space-x-2 bg-blue-100 text-blue-800 px-3 py-2 rounded-lg">
            <Building2 className="w-4 h-4" />
            <span className="font-medium">Bank Transfer</span>
          </div>
        )}
        {methods.includes('cash') && (
          <div className="flex items-center space-x-2 bg-gray-100 text-gray-800 px-3 py-2 rounded-lg">
            <Banknote className="w-4 h-4" />
            <span className="font-medium">Cash</span>
          </div>
        )}
      </div>
      {businessPhone && methods.includes('mpesa') && (
        <div className="mt-4 p-3 bg-green-50 rounded-lg border border-green-200">
          <p className="text-sm text-green-800">
            <strong>M-Pesa:</strong> {formatKenyanPhone(businessPhone)}
          </p>
        </div>
      )}
    </div>
  )
}
