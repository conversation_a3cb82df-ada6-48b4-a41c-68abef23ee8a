'use client'

import React, { Component, ErrorInfo, ReactNode } from 'react'
import { <PERSON>ert<PERSON><PERSON>gle, RefreshCw, Home, Bug, Mail } from 'lucide-react'
import { errorHand<PERSON>, ErrorDetails, createError } from '@/lib/error-handling'

interface Props {
  children: ReactNode
  fallback?: ReactNode
  onError?: (error: ErrorDetails) => void
  showDetails?: boolean
  language?: 'en' | 'sw'
}

interface State {
  hasError: boolean
  error: ErrorDetails | null
  errorId: string | null
}

export class ErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props)
    this.state = {
      hasError: false,
      error: null,
      errorId: null
    }
  }

  static getDerivedStateFromError(error: Error): State {
    // Generate unique error ID for tracking
    const errorId = `err_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
    
    const errorDetails = createError('INTERNAL_ERROR', error, {
      component: 'ErrorBoundary',
      action: 'render'
    })

    return {
      hasError: true,
      error: errorDetails,
      errorId
    }
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    const errorDetails = createError('INTERNAL_ERROR', error, {
      component: 'ErrorBoundary',
      action: 'componentDidCatch',
      metadata: {
        componentStack: errorInfo.componentStack,
        errorBoundary: true
      }
    })

    // Log the error
    errorHandler.logError(errorDetails)

    // Call custom error handler if provided
    this.props.onError?.(errorDetails)

    console.error('ErrorBoundary caught an error:', error, errorInfo)
  }

  handleRetry = () => {
    this.setState({
      hasError: false,
      error: null,
      errorId: null
    })
  }

  handleReportError = () => {
    if (this.state.error && this.state.errorId) {
      const subject = `Error Report - ${this.state.errorId}`
      const body = `Error ID: ${this.state.errorId}\nError Code: ${this.state.error.code}\nMessage: ${this.state.error.message}\nTimestamp: ${this.state.error.context.timestamp}`
      
      const mailtoLink = `mailto:<EMAIL>?subject=${encodeURIComponent(subject)}&body=${encodeURIComponent(body)}`
      window.open(mailtoLink)
    }
  }

  render() {
    if (this.state.hasError && this.state.error) {
      // Use custom fallback if provided
      if (this.props.fallback) {
        return this.props.fallback
      }

      const language = this.props.language || 'en'
      const userMessage = errorHandler.getUserMessage(this.state.error, language)
      const suggestions = errorHandler.getRecoverySuggestions(this.state.error, language)

      return (
        <div className="min-h-screen bg-gray-50 flex items-center justify-center p-4">
          <div className="max-w-md w-full bg-white rounded-lg shadow-lg p-6">
            {/* Error Icon */}
            <div className="flex justify-center mb-4">
              <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center">
                <AlertTriangle className="w-8 h-8 text-red-600" />
              </div>
            </div>

            {/* Error Title */}
            <h1 className="text-xl font-bold text-gray-900 text-center mb-2">
              {language === 'sw' ? 'Hitilafu Imetokea' : 'Something Went Wrong'}
            </h1>

            {/* Error Message */}
            <p className="text-gray-600 text-center mb-6">
              {userMessage}
            </p>

            {/* Error ID */}
            {this.state.errorId && (
              <div className="bg-gray-100 rounded-md p-3 mb-4">
                <p className="text-sm text-gray-600 text-center">
                  {language === 'sw' ? 'Kitambulisho cha Hitilafu' : 'Error ID'}: 
                  <span className="font-mono ml-1">{this.state.errorId}</span>
                </p>
              </div>
            )}

            {/* Recovery Suggestions */}
            {suggestions.length > 0 && (
              <div className="mb-6">
                <h3 className="text-sm font-medium text-gray-900 mb-2">
                  {language === 'sw' ? 'Mapendekezo ya Kutatua' : 'Try These Solutions'}:
                </h3>
                <ul className="text-sm text-gray-600 space-y-1">
                  {suggestions.map((suggestion, index) => (
                    <li key={index} className="flex items-start">
                      <span className="w-1.5 h-1.5 bg-gray-400 rounded-full mt-2 mr-2 flex-shrink-0"></span>
                      {suggestion}
                    </li>
                  ))}
                </ul>
              </div>
            )}

            {/* Action Buttons */}
            <div className="space-y-3">
              <button
                onClick={this.handleRetry}
                className="w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 transition-colors flex items-center justify-center gap-2"
              >
                <RefreshCw className="w-4 h-4" />
                {language === 'sw' ? 'Jaribu Tena' : 'Try Again'}
              </button>

              <button
                onClick={() => window.location.href = '/'}
                className="w-full bg-gray-600 text-white py-2 px-4 rounded-md hover:bg-gray-700 transition-colors flex items-center justify-center gap-2"
              >
                <Home className="w-4 h-4" />
                {language === 'sw' ? 'Rudi Nyumbani' : 'Go Home'}
              </button>

              <button
                onClick={this.handleReportError}
                className="w-full bg-gray-200 text-gray-700 py-2 px-4 rounded-md hover:bg-gray-300 transition-colors flex items-center justify-center gap-2"
              >
                <Mail className="w-4 h-4" />
                {language === 'sw' ? 'Ripoti Hitilafu' : 'Report Error'}
              </button>
            </div>

            {/* Technical Details (Development Only) */}
            {this.props.showDetails && process.env.NODE_ENV === 'development' && this.state.error && (
              <details className="mt-6 bg-gray-100 rounded-md p-3">
                <summary className="cursor-pointer text-sm font-medium text-gray-700 flex items-center gap-2">
                  <Bug className="w-4 h-4" />
                  Technical Details
                </summary>
                <div className="mt-3 text-xs text-gray-600 space-y-2">
                  <div>
                    <strong>Code:</strong> {this.state.error.code}
                  </div>
                  <div>
                    <strong>Message:</strong> {this.state.error.message}
                  </div>
                  <div>
                    <strong>Severity:</strong> {this.state.error.severity}
                  </div>
                  <div>
                    <strong>Category:</strong> {this.state.error.category}
                  </div>
                  <div>
                    <strong>Timestamp:</strong> {this.state.error.context.timestamp}
                  </div>
                  {this.state.error.stack && (
                    <div>
                      <strong>Stack:</strong>
                      <pre className="mt-1 text-xs bg-gray-200 p-2 rounded overflow-x-auto">
                        {this.state.error.stack}
                      </pre>
                    </div>
                  )}
                </div>
              </details>
            )}

            {/* Kenyan Flag */}
            <div className="mt-6 text-center">
              <span className="text-2xl">🇰🇪</span>
              <p className="text-xs text-gray-500 mt-1">
                {language === 'sw' ? 'Tunaomba radhi kwa usumbufu' : 'We apologize for the inconvenience'}
              </p>
            </div>
          </div>
        </div>
      )
    }

    return this.props.children
  }
}

// Higher-order component for wrapping components with error boundary
export function withErrorBoundary<P extends object>(
  Component: React.ComponentType<P>,
  errorBoundaryProps?: Omit<Props, 'children'>
) {
  const WrappedComponent = (props: P) => (
    <ErrorBoundary {...errorBoundaryProps}>
      <Component {...props} />
    </ErrorBoundary>
  )

  WrappedComponent.displayName = `withErrorBoundary(${Component.displayName || Component.name})`
  return WrappedComponent
}

// Hook for error handling in functional components
export function useErrorHandler() {
  const handleError = (error: Error, context?: Partial<import('@/lib/error-handling').ErrorContext>) => {
    const errorDetails = createError('INTERNAL_ERROR', error, context)
    errorHandler.logError(errorDetails)
    
    // In a real app, you might want to show a toast notification
    console.error('Error handled:', errorDetails)
  }

  return { handleError }
}

// Async error boundary for handling promise rejections
export class AsyncErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props)
    this.state = {
      hasError: false,
      error: null,
      errorId: null
    }
  }

  componentDidMount() {
    // Handle unhandled promise rejections
    window.addEventListener('unhandledrejection', this.handleUnhandledRejection)
  }

  componentWillUnmount() {
    window.removeEventListener('unhandledrejection', this.handleUnhandledRejection)
  }

  handleUnhandledRejection = (event: PromiseRejectionEvent) => {
    const errorDetails = createError('INTERNAL_ERROR', new Error(event.reason), {
      component: 'AsyncErrorBoundary',
      action: 'unhandledRejection'
    })

    this.setState({
      hasError: true,
      error: errorDetails,
      errorId: `async_err_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
    })

    errorHandler.logError(errorDetails)
    event.preventDefault()
  }

  render() {
    if (this.state.hasError) {
      return <ErrorBoundary {...this.props} />
    }

    return this.props.children
  }
}

export default ErrorBoundary
