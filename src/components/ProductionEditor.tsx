'use client'

import { useState } from 'react'
import { 
  CheckCircle, 
  AlertTriangle, 
  Eye, 
  RefreshCw, 
  Shield, 
  Zap,
  ArrowRight,
  ArrowLeft,
  Save,
  AlertCircle
} from 'lucide-react'
import { EditProposal, ValidationResult, EditIntent } from '@/lib/production-editing-system'

interface ProductionEditorProps {
  websiteData: any
  onSave: (updatedData: any) => void
  language?: 'en' | 'sw'
}

type EditStage = 'input' | 'analyzing' | 'proposing' | 'reviewing' | 'validating' | 'applying' | 'complete'

export default function ProductionEditor({ websiteData, onSave, language = 'en' }: ProductionEditorProps) {
  const [stage, setStage] = useState<EditStage>('input')
  const [editInstructions, setEditInstructions] = useState('')
  const [intent, setIntent] = useState<EditIntent | null>(null)
  const [proposals, setProposals] = useState<EditProposal[]>([])
  const [selectedProposal, setSelectedProposal] = useState<EditProposal | null>(null)
  const [validation, setValidation] = useState<ValidationResult | null>(null)
  const [diffPreview, setDiffPreview] = useState<any>(null)
  const [error, setError] = useState<string | null>(null)
  const [backup, setBackup] = useState<any>(null)

  const stageLabels = {
    input: language === 'sw' ? 'Maagizo' : 'Instructions',
    analyzing: language === 'sw' ? 'Inachambua...' : 'Analyzing...',
    proposing: language === 'sw' ? 'Inaunda Mapendekezo...' : 'Creating Proposals...',
    reviewing: language === 'sw' ? 'Kagua Mapendekezo' : 'Review Proposals',
    validating: language === 'sw' ? 'Inahakiki...' : 'Validating...',
    applying: language === 'sw' ? 'Inatekeleza...' : 'Applying...',
    complete: language === 'sw' ? 'Imekamilika' : 'Complete'
  }

  const handleStartEdit = async () => {
    if (!editInstructions.trim()) return

    setError(null)
    setStage('analyzing')

    try {
      // Stage 1: Analyze intent
      const analyzeResponse = await fetch('/api/production-edit', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          editInstructions,
          stage: 'analyze'
        })
      })

      if (!analyzeResponse.ok) throw new Error('Failed to analyze edit intent')
      
      const analyzeResult = await analyzeResponse.json()
      setIntent(analyzeResult.data.intent)
      setStage('proposing')

      // Stage 2: Generate proposals
      const proposeResponse = await fetch('/api/production-edit', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          currentWebsite: websiteData?.website?.html || '',
          editInstructions,
          stage: 'propose'
        })
      })

      if (!proposeResponse.ok) throw new Error('Failed to generate proposals')
      
      const proposeResult = await proposeResponse.json()
      setProposals(proposeResult.data.proposals)
      setStage('reviewing')

    } catch (error) {
      setError(error instanceof Error ? error.message : 'Unknown error')
      setStage('input')
    }
  }

  const handleSelectProposal = async (proposal: EditProposal) => {
    setSelectedProposal(proposal)
    setStage('validating')

    try {
      // Stage 3: Validate proposal
      const validateResponse = await fetch('/api/production-edit', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          currentWebsite: websiteData?.website?.html || '',
          proposalId: proposal.id,
          proposals,
          stage: 'validate'
        })
      })

      if (!validateResponse.ok) throw new Error('Failed to validate proposal')
      
      const validateResult = await validateResponse.json()
      setValidation(validateResult.data.validation)
      setDiffPreview(validateResult.data.diffPreview)
      setStage('reviewing')

    } catch (error) {
      setError(error instanceof Error ? error.message : 'Validation failed')
      setStage('reviewing')
    }
  }

  const handleApplyEdit = async () => {
    if (!selectedProposal || !validation?.isValid) return

    setStage('applying')

    try {
      // Stage 4: Apply proposal
      const applyResponse = await fetch('/api/production-edit', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          selectedProposal,
          stage: 'apply'
        })
      })

      if (!applyResponse.ok) throw new Error('Failed to apply edit')
      
      const applyResult = await applyResponse.json()
      setBackup(applyResult.data.backup)
      
      // Update website data
      const updatedData = {
        ...websiteData,
        website: applyResult.data.website,
        lastEdit: applyResult.data.editSummary
      }
      
      onSave(updatedData)
      setStage('complete')

    } catch (error) {
      setError(error instanceof Error ? error.message : 'Failed to apply edit')
      setStage('reviewing')
    }
  }

  const handleReset = () => {
    setStage('input')
    setEditInstructions('')
    setIntent(null)
    setProposals([])
    setSelectedProposal(null)
    setValidation(null)
    setDiffPreview(null)
    setError(null)
    setBackup(null)
  }

  const getRiskColor = (risk: string) => {
    switch (risk) {
      case 'low': return 'text-green-600 bg-green-100'
      case 'medium': return 'text-yellow-600 bg-yellow-100'
      case 'high': return 'text-red-600 bg-red-100'
      default: return 'text-gray-600 bg-gray-100'
    }
  }

  const getConfidenceColor = (confidence: number) => {
    if (confidence >= 0.8) return 'text-green-600'
    if (confidence >= 0.6) return 'text-yellow-600'
    return 'text-red-600'
  }

  return (
    <div className="max-w-4xl mx-auto p-6 space-y-6">
      {/* Header */}
      <div className="text-center">
        <h2 className="text-2xl font-bold text-gray-900 mb-2">
          {language === 'sw' ? 'Mhariri Mahiri' : 'Smart Editor'}
        </h2>
        <p className="text-gray-600">
          {language === 'sw'
            ? 'Mfumo wa usalama wa hatua nyingi kwa mabadiliko makuu'
            : 'Safe, intelligent editing with preview and validation'
          }
        </p>
      </div>

      {/* Progress Indicator */}
      <div className="w-full py-4">
        {/* Mobile: Vertical Layout */}
        <div className="block sm:hidden space-y-3">
          {Object.entries(stageLabels).map(([key, label], index) => {
            const isActive = stage === key
            const isCompleted = Object.keys(stageLabels).indexOf(stage) > index

            return (
              <div key={key} className="flex items-center space-x-3">
                <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium flex-shrink-0 ${
                  isCompleted ? 'bg-green-500 text-white' :
                  isActive ? 'bg-blue-500 text-white' :
                  'bg-gray-200 text-gray-600'
                }`}>
                  {isCompleted ? <CheckCircle className="w-4 h-4" /> : index + 1}
                </div>
                <span className={`text-sm ${isActive ? 'font-medium text-gray-900' : 'text-gray-500'}`}>
                  {label}
                </span>
              </div>
            )
          })}
        </div>

        {/* Desktop: Horizontal Layout */}
        <div className="hidden sm:flex items-center justify-center">
          <div className="flex items-center space-x-2 lg:space-x-4 overflow-x-auto max-w-full">
            {Object.entries(stageLabels).map(([key, label], index) => {
              const isActive = stage === key
              const isCompleted = Object.keys(stageLabels).indexOf(stage) > index

              return (
                <div key={key} className="flex items-center flex-shrink-0">
                  <div className="flex items-center space-x-2">
                    <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium ${
                      isCompleted ? 'bg-green-500 text-white' :
                      isActive ? 'bg-blue-500 text-white' :
                      'bg-gray-200 text-gray-600'
                    }`}>
                      {isCompleted ? <CheckCircle className="w-4 h-4" /> : index + 1}
                    </div>
                    <span className={`text-xs lg:text-sm whitespace-nowrap ${
                      isActive ? 'font-medium text-gray-900' : 'text-gray-500'
                    }`}>
                      {label}
                    </span>
                  </div>
                  {index < Object.keys(stageLabels).length - 1 && (
                    <ArrowRight className="w-3 h-3 lg:w-4 lg:h-4 text-gray-400 mx-2 lg:mx-3 flex-shrink-0" />
                  )}
                </div>
              )
            })}
          </div>
        </div>
      </div>

      {/* Error Display */}
      {error && (
        <div className="p-4 bg-red-50 border border-red-200 rounded-lg">
          <div className="flex items-center space-x-2">
            <AlertCircle className="w-5 h-5 text-red-600" />
            <span className="text-red-800 font-medium">
              {language === 'sw' ? 'Hitilafu' : 'Error'}
            </span>
          </div>
          <p className="text-red-700 mt-1">{error}</p>
        </div>
      )}

      {/* Stage 1: Input */}
      {stage === 'input' && (
        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              {language === 'sw' ? 'Eleza mabadiliko unayotaka' : 'Describe the changes you want'}
            </label>
            <textarea
              value={editInstructions}
              onChange={(e) => setEditInstructions(e.target.value)}
              placeholder={language === 'sw' 
                ? 'Mfano: Badilisha rangi ya kichwa kuwa bluu'
                : 'Example: Change the header color to blue'
              }
              className="w-full p-4 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 resize-none"
              rows={4}
            />
          </div>
          
          <button
            onClick={handleStartEdit}
            disabled={!editInstructions.trim()}
            className="w-full px-6 py-3 bg-gradient-to-r from-blue-600 to-purple-600 text-white rounded-lg hover:from-blue-700 hover:to-purple-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center space-x-2"
          >
            <Shield className="w-5 h-5" />
            <span>{language === 'sw' ? 'Anza Mchakato wa Usalama' : 'Start Safe Edit Process'}</span>
          </button>
        </div>
      )}

      {/* Stage 2: Analyzing/Proposing */}
      {(stage === 'analyzing' || stage === 'proposing') && (
        <div className="text-center py-8">
          <RefreshCw className="w-8 h-8 text-blue-500 animate-spin mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">
            {stageLabels[stage]}
          </h3>
          <p className="text-gray-600">
            {stage === 'analyzing' 
              ? (language === 'sw' ? 'Tunachambua maagizo yako...' : 'Analyzing your instructions...')
              : (language === 'sw' ? 'Tunaunda mapendekezo ya mabadiliko...' : 'Creating edit proposals...')
            }
          </p>
        </div>
      )}

      {/* Stage 3: Review Proposals */}
      {stage === 'reviewing' && proposals.length > 0 && (
        <div className="space-y-6">
          <h3 className="text-lg font-semibold text-gray-900">
            {language === 'sw' ? 'Chagua Pendekezo' : 'Select Proposal'}
          </h3>
          
          {/* Intent Summary */}
          {intent && (
            <div className="p-4 bg-blue-50 border border-blue-200 rounded-lg">
              <h4 className="font-medium text-blue-900 mb-2">
                {language === 'sw' ? 'Nia ya Mabadiliko' : 'Edit Intent'}
              </h4>
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <span className="text-blue-700 font-medium">
                    {language === 'sw' ? 'Kitendo:' : 'Action:'}
                  </span>
                  <span className="ml-2 text-blue-800">{intent.action}</span>
                </div>
                <div>
                  <span className="text-blue-700 font-medium">
                    {language === 'sw' ? 'Upeo:' : 'Scope:'}
                  </span>
                  <span className="ml-2 text-blue-800">{intent.scope}</span>
                </div>
              </div>
            </div>
          )}

          {/* Proposals */}
          <div className="grid gap-4">
            {proposals.map((proposal) => (
              <div
                key={proposal.id}
                className={`p-4 border rounded-lg cursor-pointer transition-all ${
                  selectedProposal?.id === proposal.id
                    ? 'border-blue-500 bg-blue-50'
                    : 'border-gray-200 hover:border-gray-300'
                }`}
                onClick={() => handleSelectProposal(proposal)}
              >
                <div className="flex items-start justify-between mb-3">
                  <h4 className="font-medium text-gray-900">{proposal.description}</h4>
                  <div className="flex items-center space-x-2">
                    <span className={`px-2 py-1 rounded-full text-xs font-medium ${getRiskColor(proposal.riskLevel)}`}>
                      {proposal.riskLevel} risk
                    </span>
                    <span className={`text-sm font-medium ${getConfidenceColor(proposal.confidence)}`}>
                      {Math.round(proposal.confidence * 100)}%
                    </span>
                  </div>
                </div>
                
                <p className="text-sm text-gray-600 mb-3">{proposal.estimatedImpact}</p>
                
                <div className="text-xs text-gray-500">
                  {proposal.changes.length} {language === 'sw' ? 'mabadiliko' : 'changes'}
                </div>
              </div>
            ))}
          </div>

          {/* Validation Results */}
          {selectedProposal && validation && (
            <div className="space-y-4">
              <div className={`p-4 border rounded-lg ${
                validation.isValid ? 'border-green-200 bg-green-50' : 'border-red-200 bg-red-50'
              }`}>
                <div className="flex items-center space-x-2 mb-3">
                  {validation.isValid ? (
                    <CheckCircle className="w-5 h-5 text-green-600" />
                  ) : (
                    <AlertTriangle className="w-5 h-5 text-red-600" />
                  )}
                  <h4 className={`font-medium ${validation.isValid ? 'text-green-900' : 'text-red-900'}`}>
                    {language === 'sw' ? 'Matokeo ya Uhakiki' : 'Validation Results'}
                  </h4>
                  <span className={`text-sm ${validation.isValid ? 'text-green-700' : 'text-red-700'}`}>
                    Score: {validation.score}/100
                  </span>
                </div>

                {validation.issues.length > 0 && (
                  <div className="mb-3">
                    <h5 className="text-sm font-medium text-red-800 mb-1">
                      {language === 'sw' ? 'Matatizo:' : 'Issues:'}
                    </h5>
                    <ul className="text-sm text-red-700 list-disc list-inside">
                      {validation.issues.map((issue, index) => (
                        <li key={index}>{issue}</li>
                      ))}
                    </ul>
                  </div>
                )}

                {validation.warnings.length > 0 && (
                  <div className="mb-3">
                    <h5 className="text-sm font-medium text-yellow-800 mb-1">
                      {language === 'sw' ? 'Maonyo:' : 'Warnings:'}
                    </h5>
                    <ul className="text-sm text-yellow-700 list-disc list-inside">
                      {validation.warnings.map((warning, index) => (
                        <li key={index}>{warning}</li>
                      ))}
                    </ul>
                  </div>
                )}
              </div>

              {/* Diff Preview */}
              {diffPreview && (
                <div className="p-4 bg-gray-50 border border-gray-200 rounded-lg">
                  <h4 className="font-medium text-gray-900 mb-2">
                    {language === 'sw' ? 'Muhtasari wa Mabadiliko' : 'Change Summary'}
                  </h4>
                  <p className="text-sm text-gray-700 mb-3">{diffPreview.summary}</p>
                  {diffPreview.changes.map((change: any, index: number) => (
                    <div key={index} className="text-sm text-gray-600">
                      <span className="font-medium">{change.type}:</span> {change.description}
                      <span className={`ml-2 text-xs ${
                        change.impact === 'significant' ? 'text-red-600' : 'text-yellow-600'
                      }`}>
                        ({change.impact})
                      </span>
                    </div>
                  ))}
                </div>
              )}

              {/* Action Buttons */}
              <div className="flex space-x-4">
                <button
                  onClick={handleReset}
                  className="flex-1 px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50"
                >
                  <ArrowLeft className="w-4 h-4 inline mr-2" />
                  {language === 'sw' ? 'Rudi Nyuma' : 'Start Over'}
                </button>
                
                {validation.isValid && (
                  <button
                    onClick={handleApplyEdit}
                    className="flex-1 px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700"
                  >
                    <Zap className="w-4 h-4 inline mr-2" />
                    {language === 'sw' ? 'Tekeleza Mabadiliko' : 'Apply Changes'}
                  </button>
                )}
              </div>
            </div>
          )}
        </div>
      )}

      {/* Stage 4: Applying */}
      {stage === 'applying' && (
        <div className="text-center py-8">
          <RefreshCw className="w-8 h-8 text-green-500 animate-spin mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">
            {language === 'sw' ? 'Inatekeleza Mabadiliko...' : 'Applying Changes...'}
          </h3>
          <p className="text-gray-600">
            {language === 'sw' 
              ? 'Tunatekeleza mabadiliko yako kwa usalama...'
              : 'Safely applying your changes...'
            }
          </p>
        </div>
      )}

      {/* Stage 5: Complete */}
      {stage === 'complete' && (
        <div className="text-center py-8">
          <CheckCircle className="w-12 h-12 text-green-500 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">
            {language === 'sw' ? 'Mabadiliko Yametekelezwa!' : 'Changes Applied Successfully!'}
          </h3>
          <p className="text-gray-600 mb-6">
            {language === 'sw' 
              ? 'Mabadiliko yako yametekelezwa kwa usalama. Nakala ya kumbuka imehifadhiwa.'
              : 'Your changes have been applied safely. A backup has been created.'
            }
          </p>
          
          {backup && (
            <div className="p-4 bg-blue-50 border border-blue-200 rounded-lg mb-6">
              <p className="text-sm text-blue-800">
                {language === 'sw' ? 'Nakala ya Kumbuka:' : 'Backup ID:'} {backup.backupId}
              </p>
            </div>
          )}
          
          <button
            onClick={handleReset}
            className="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
          >
            {language === 'sw' ? 'Hariri Zaidi' : 'Make Another Edit'}
          </button>
        </div>
      )}
    </div>
  )
}
