'use client'

import React, { useEffect, useRef, useState, useCallback } from 'react'
import { Loader2, AlertCircle } from 'lucide-react'

/**
 * Optimize HTML for faster preview loading
 */
const optimizeHtmlForPreview = (html: string): string => {
  // Remove large base64 images and replace with placeholders for faster loading
  let optimized = html.replace(
    /data:image\/[^;]+;base64,[A-Za-z0-9+/=]{1000,}/g,
    'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAwIiBoZWlnaHQ9IjMwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjZjNmNGY2Ii8+PHRleHQgeD0iNTAlIiB5PSI1MCUiIGZvbnQtZmFtaWx5PSJBcmlhbCIgZm9udC1zaXplPSIxNCIgZmlsbD0iIzk5YTNhZiIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZHk9Ii4zZW0iPkxvYWRpbmcgSW1hZ2UuLi48L3RleHQ+PC9zdmc+'
  )

  // Add loading="lazy" to images for better performance
  optimized = optimized.replace(/<img([^>]*?)>/g, '<img$1 loading="lazy">')

  // Add performance optimizations
  const performanceScript = `
    <script>
      // Optimize images after load
      document.addEventListener('DOMContentLoaded', function() {
        const images = document.querySelectorAll('img[loading="lazy"]');
        images.forEach(img => {
          img.addEventListener('load', function() {
            this.style.opacity = '1';
            this.style.transition = 'opacity 0.3s ease';
          });
        });
      });
    </script>
  `

  // Insert performance script before closing body tag
  optimized = optimized.replace('</body>', performanceScript + '</body>')

  return optimized
}

interface SecureIframeRendererProps {
  html: string
  css?: string
  title?: string
  onLoad?: () => void
  onError?: (error: string) => void
  className?: string
}

/**
 * Secure iframe renderer based on Claude Artifacts pattern
 * Uses postMessage communication and proper sandboxing
 */
export const SecureIframeRenderer: React.FC<SecureIframeRendererProps> = ({
  html,
  css,
  title = 'Website Preview',
  onLoad,
  onError,
  className = ''
}) => {
  const iframeRef = useRef<HTMLIFrameElement>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [iframeHeight, setIframeHeight] = useState(1200) // Default height for full websites
  const [contentId] = useState(() => `content-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`)
  const [isVisible, setIsVisible] = useState(true) // Always visible for immediate loading
  const [contentCache] = useState(new Map<string, string>()) // Content cache

  /**
   * Generate secure HTML document with proper structure
   */
  const generateSecureDocument = useCallback((htmlContent: string, cssContent: string = '', id: string): string => {
    // If htmlContent is already a complete HTML document (Pure AI format), use it directly
    if (htmlContent.includes('<!DOCTYPE html>') || htmlContent.includes('<html')) {
      // Add cache busting and content ID to existing HTML
      return htmlContent.replace(
        '<head>',
        `<head>
  <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate, max-age=0">
  <meta http-equiv="Pragma" content="no-cache">
  <meta http-equiv="Expires" content="0">
  <meta name="content-id" content="${id}">`
      )
    }

    // Legacy format: separate HTML and CSS
    return `<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate, max-age=0">
  <meta http-equiv="Pragma" content="no-cache">
  <meta http-equiv="Expires" content="0">
  <meta name="content-id" content="${id}">
  <title>${title}</title>
  <style>
    /* Reset and base styles */
    * {
      box-sizing: border-box;
    }
    
    body {
      margin: 0;
      padding: 0;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
      line-height: 1.6;
      color: #333;
      background: #fff;
    }
    
    /* Cache busting comment: ${id} */
    ${cssContent}
    
    /* Error handling styles */
    .error-container {
      display: flex;
      align-items: center;
      justify-content: center;
      min-height: 100vh;
      padding: 2rem;
      background: #f8f9fa;
    }
    
    .error-message {
      text-align: center;
      color: #dc3545;
      font-size: 1.1rem;
    }
    
    /* Loading styles */
    .loading-container {
      display: flex;
      align-items: center;
      justify-content: center;
      min-height: 100vh;
      background: #f8f9fa;
    }
    
    .loading-spinner {
      width: 40px;
      height: 40px;
      border: 4px solid #e9ecef;
      border-top: 4px solid #007bff;
      border-radius: 50%;
      animation: spin 1s linear infinite;
    }
    
    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }
  </style>
  <script>
    // Security and communication setup
    (function() {
      'use strict';
      
      // Prevent iframe from being embedded in unauthorized domains
      if (window.self !== window.top) {
        try {
          // Allow communication with parent
          window.addEventListener('message', function(event) {
            // Add origin validation here if needed
            console.log('Received message:', event.data);
          });
          
          // Send ready signal to parent
          window.parent.postMessage({
            type: 'iframe-ready',
            contentId: '${id}',
            timestamp: Date.now()
          }, '*');
          
        } catch (e) {
          console.error('Communication setup failed:', e);
        }
      }
      
      // Error handling
      window.addEventListener('error', function(event) {
        console.error('Runtime error:', event.error);
        window.parent.postMessage({
          type: 'iframe-error',
          contentId: '${id}',
          error: event.error?.message || 'Unknown error',
          timestamp: Date.now()
        }, '*');
      });
      
      // Console capture for debugging
      const originalConsole = {
        log: console.log,
        error: console.error,
        warn: console.warn
      };
      
      ['log', 'error', 'warn'].forEach(method => {
        console[method] = function(...args) {
          originalConsole[method].apply(console, args);
          try {
            window.parent.postMessage({
              type: 'console-' + method,
              contentId: '${id}',
              message: args.map(arg => 
                typeof arg === 'object' ? JSON.stringify(arg) : String(arg)
              ).join(' '),
              timestamp: Date.now()
            }, '*');
          } catch (e) {
            // Ignore postMessage errors
          }
        };
      });
      
      // Performance monitoring
      window.addEventListener('load', function() {
        const loadTime = performance.now();
        window.parent.postMessage({
          type: 'performance-metrics',
          contentId: '${id}',
          loadTime: loadTime,
          timestamp: Date.now()
        }, '*');
      });
      
    })();
  </script>
</head>
<body data-content-id="${id}">
  <!-- Cache busting comment: ${id} -->
  ${htmlContent}
  
  <script>
    // Additional initialization after content load
    document.addEventListener('DOMContentLoaded', function() {
      // Notify parent that content is ready
      window.parent.postMessage({
        type: 'content-ready',
        contentId: '${id}',
        timestamp: Date.now()
      }, '*');
      
      // Add click tracking for analytics
      document.addEventListener('click', function(event) {
        window.parent.postMessage({
          type: 'user-interaction',
          contentId: '${id}',
          interaction: 'click',
          target: event.target.tagName,
          timestamp: Date.now()
        }, '*');
      });
    });
  </script>
</body>
</html>`
  }, [title])

  /**
   * Handle messages from iframe
   */
  const handleMessage = useCallback((event: MessageEvent) => {
    if (!event.data || event.data.contentId !== contentId) {
      return
    }

    switch (event.data.type) {
      case 'iframe-ready':
        console.log('🔗 Iframe ready:', event.data)
        break
        
      case 'content-ready':
        console.log('✅ Content ready:', event.data)
        setIsLoading(false)
        onLoad?.()
        break
        
      case 'iframe-error':
        console.error('❌ Iframe error:', event.data.error)
        setError(event.data.error)
        setIsLoading(false)
        onError?.(event.data.error)
        break
        
      case 'console-log':
        console.log('📝 Iframe log:', event.data.message)
        break
        
      case 'console-error':
        console.error('🚨 Iframe error:', event.data.message)
        break
        
      case 'console-warn':
        console.warn('⚠️ Iframe warning:', event.data.message)
        break
        
      case 'performance-metrics':
        console.log('📊 Performance:', `${event.data.loadTime.toFixed(2)}ms`)
        break
        
      case 'user-interaction':
        console.log('👆 User interaction:', event.data.interaction, event.data.target)
        break
        
      default:
        console.log('📨 Unknown message:', event.data)
    }
  }, [contentId, onLoad, onError])

  /**
   * Immediate loading - no lazy loading for better performance
   */
  useEffect(() => {
    // Always set visible immediately for fastest loading
    setIsVisible(true)
  }, [])

  /**
   * Initialize iframe with content (with lazy loading and caching)
   */
  useEffect(() => {
    const iframe = iframeRef.current
    if (!iframe || !html || !isVisible) {
      console.log('🚫 Iframe initialization skipped:', {
        hasIframe: !!iframe,
        hasHtml: !!html,
        htmlLength: html?.length || 0,
        isVisible,
        htmlPreview: html?.substring(0, 100) || 'No HTML'
      })
      return
    }

    console.log('🔄 Initializing iframe content...', {
      htmlLength: html.length,
      htmlPreview: html.substring(0, 100),
      contentId
    })
    setIsLoading(true)
    setError(null)

    try {
      // Check cache first for immediate loading
      const cacheKey = `${html.substring(0, 100)}-${html.length}`
      const cachedContent = contentCache.get(cacheKey)

      if (cachedContent) {
        console.log('⚡ Using cached content for instant loading')
        iframe.src = cachedContent
        setIsLoading(false)
        onLoad?.()
        return
      }

      // For Pure AI complete HTML, use it directly for faster loading
      if (html.includes('<!DOCTYPE html>') || html.includes('<html')) {
        console.log('📄 Using complete HTML document directly')

        // Optimize HTML for faster loading
        const optimizedHtml = optimizeHtmlForPreview(html)

        // Direct content injection for faster loading
        iframe.onload = () => {
          console.log('✅ Iframe loaded successfully')
          setIsLoading(false)

          // Calculate dynamic height after content loads
          setTimeout(() => {
            try {
              const iframeDocument = iframe.contentDocument || iframe.contentWindow?.document
              if (iframeDocument) {
                const body = iframeDocument.body
                const html = iframeDocument.documentElement

                if (body && html) {
                  const contentHeight = Math.max(
                    body.scrollHeight,
                    body.offsetHeight,
                    html.clientHeight,
                    html.scrollHeight,
                    html.offsetHeight
                  )

                  const finalHeight = Math.max(contentHeight + 100, 1000)
                  console.log('📏 Calculated iframe height:', finalHeight, 'px')
                  setIframeHeight(finalHeight)
                }
              }
            } catch (error) {
              console.warn('⚠️ Could not calculate iframe height:', error)
              setIframeHeight(2000) // Fallback for full websites
            }
          }, 1500) // Give content time to render

          onLoad?.()
        }

        iframe.onerror = () => {
          console.error('❌ Iframe loading error')
          setError('Failed to load content')
          setIsLoading(false)
          onError?.('Failed to load content')
        }

        // Use data URL for immediate loading (simpler and more reliable)
        const dataUrl = `data:text/html;charset=utf-8,${encodeURIComponent(optimizedHtml)}`

        // Cache the data URL for future use
        contentCache.set(cacheKey, dataUrl)

        iframe.src = dataUrl
      } else {
        console.log('🔧 Using legacy format with secure document generation')
        // Legacy format: generate secure document
        const secureDocument = generateSecureDocument(html, css || '', contentId)
        const dataUrl = `data:text/html;charset=utf-8,${encodeURIComponent(secureDocument)}`

        iframe.onload = () => {
          console.log('✅ Iframe loaded successfully')
          setIsLoading(false)

          // Calculate dynamic height for legacy format too
          setTimeout(() => {
            try {
              const iframeDocument = iframe.contentDocument || iframe.contentWindow?.document
              if (iframeDocument) {
                const body = iframeDocument.body
                const html = iframeDocument.documentElement

                if (body && html) {
                  const contentHeight = Math.max(
                    body.scrollHeight,
                    body.offsetHeight,
                    html.clientHeight,
                    html.scrollHeight,
                    html.offsetHeight
                  )

                  const finalHeight = Math.max(contentHeight + 100, 1000)
                  console.log('📏 Calculated iframe height (legacy):', finalHeight, 'px')
                  setIframeHeight(finalHeight)
                }
              }
            } catch (error) {
              console.warn('⚠️ Could not calculate iframe height (legacy):', error)
              setIframeHeight(2000)
            }
          }, 1500)

          onLoad?.()
        }

        iframe.src = dataUrl
      }
    } catch (err) {
      console.error('Failed to initialize iframe:', err)
      setError('Failed to load content')
      setIsLoading(false)
      onError?.('Failed to load content')
    }
  }, [html, css, contentId, generateSecureDocument, onLoad, onError])

  /**
   * Handle iframe load event and calculate dynamic height
   */
  const handleIframeLoad = useCallback(() => {
    console.log('🔄 Iframe load event triggered')

    // Calculate dynamic height after content loads
    if (iframeRef.current) {
      try {
        const iframe = iframeRef.current
        const iframeDocument = iframe.contentDocument || iframe.contentWindow?.document

        if (iframeDocument) {
          // Wait a bit for content to render, then calculate height
          setTimeout(() => {
            const body = iframeDocument.body
            const html = iframeDocument.documentElement

            if (body && html) {
              // Get the full height of the content
              const contentHeight = Math.max(
                body.scrollHeight,
                body.offsetHeight,
                html.clientHeight,
                html.scrollHeight,
                html.offsetHeight
              )

              // Set minimum height and add some padding
              const finalHeight = Math.max(contentHeight + 100, 800)
              console.log('📏 Calculated iframe height:', finalHeight, 'px')
              setIframeHeight(finalHeight)
            }
          }, 1000) // Give content time to render
        }
      } catch (error) {
        console.warn('⚠️ Could not calculate iframe height (cross-origin):', error)
        // Fallback to a reasonable height for full websites
        setIframeHeight(2000)
      }
    }
  }, [setIframeHeight])

  /**
   * Handle iframe error event
   */
  const handleIframeError = useCallback(() => {
    console.error('💥 Iframe failed to load')
    setError('Failed to load iframe')
    setIsLoading(false)
    onError?.('Failed to load iframe')
  }, [onError])

  return (
    <div className={`relative w-full h-full ${className}`}>
      {/* Loading overlay */}
      {isLoading && (
        <div className="absolute inset-0 bg-white z-10">
          <div className="flex items-center justify-center h-full">
            <div className="text-center">
              <Loader2 className="w-8 h-8 text-blue-500 animate-spin mx-auto mb-4" />
              <p className="text-gray-600 text-sm">Loading preview...</p>
            </div>
          </div>
        </div>
      )}

      {/* Error overlay */}
      {error && (
        <div className="absolute inset-0 flex items-center justify-center bg-red-50 z-10">
          <div className="text-center p-4">
            <div className="text-red-600 text-lg mb-2">⚠️</div>
            <p className="text-sm text-red-700 mb-2">{error}</p>
            <div className="text-xs text-gray-600 mb-3 max-w-md">
              <details>
                <summary className="cursor-pointer">Debug Info</summary>
                <div className="mt-2 text-left bg-gray-100 p-2 rounded">
                  <div>HTML Length: {html?.length || 0}</div>
                  <div>Content ID: {contentId}</div>
                  <div>Is Visible: {isVisible.toString()}</div>
                  <div>HTML Preview: {html?.substring(0, 100) || 'No HTML'}</div>
                </div>
              </details>
            </div>
            <button
              onClick={() => {
                setError(null)
                setIsLoading(true)
                // Force reload
                if (iframeRef.current) {
                  iframeRef.current.src = iframeRef.current.src
                }
              }}
              className="mt-2 px-3 py-1 bg-red-600 text-white text-xs rounded hover:bg-red-700"
            >
              Retry
            </button>
          </div>
        </div>
      )}

      {/* Secure iframe */}
      <iframe
        ref={iframeRef}
        title={title}
        className="w-full border-0"
        sandbox="allow-scripts allow-same-origin allow-forms allow-popups allow-popups-to-escape-sandbox"
        onLoad={handleIframeLoad}
        onError={handleIframeError}
        style={{
          height: `${iframeHeight}px`,
          opacity: isLoading || error ? 0 : 1,
          transition: 'opacity 0.3s ease, height 0.5s ease'
        }}
      />
    </div>
  )
}

export default SecureIframeRenderer
