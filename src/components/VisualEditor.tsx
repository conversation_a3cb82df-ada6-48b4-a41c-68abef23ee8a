'use client'

import { useState, useRef } from 'react'
import {
  MessageCircle,
  Smartphone,
  Tablet,
  Monitor,
  Save,
  Undo,
  Redo,
  Eye,
  EyeOff,
  Sparkles,
  RefreshCw,
  Camera,
  ExternalLink
} from 'lucide-react'
import ImageManager from './ImageManager'
import SecureIframeRender<PERSON> from './SecureIframeRenderer'

interface VisualEditorProps {
  websiteData: any
  onSave: (updatedData: any) => void
  language?: 'en' | 'sw'
}

export default function VisualEditor({ websiteData, onSave, language = 'en' }: VisualEditorProps) {
  const [activeTab, setActiveTab] = useState<'chat' | 'images' | 'preview'>('chat')
  const [previewMode, setPreviewMode] = useState<'mobile' | 'tablet' | 'desktop'>('desktop')
  const [showEditor, setShowEditor] = useState(true)
  const [editHistory, setEditHistory] = useState<any[]>([websiteData])
  const [historyIndex, setHistoryIndex] = useState(0)
  const [editInstructions, setEditInstructions] = useState('')
  const [isEditing, setIsEditing] = useState(false)
  const [showImageManager, setShowImageManager] = useState(false)
  const iframeRef = useRef<HTMLIFrameElement>(null)

  // AI-powered editing functions
  const handleChatEdit = async () => {
    if (!editInstructions.trim()) return

    setIsEditing(true)
    try {
      const response = await fetch('/api/edit-website-chat', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          currentWebsite: websiteData.website.html,
          editInstructions: editInstructions,
          conversationHistory: websiteData.conversationHistory || []
        })
      })

      if (!response.ok) {
        throw new Error(`Edit request failed: ${response.status}`)
      }

      const result = await response.json()

      if (result.success) {
        const updatedData = {
          ...websiteData,
          website: result.data.website,
          conversationHistory: result.data.conversationHistory || [],
          lastEdit: result.data.editSummary
        }

        // Add to history
        const newHistory = editHistory.slice(0, historyIndex + 1)
        newHistory.push(updatedData)
        setEditHistory(newHistory)
        setHistoryIndex(newHistory.length - 1)

        setEditInstructions('')
        onSave(updatedData)
      }
    } catch (error) {
      console.error('Edit failed:', error)
    } finally {
      setIsEditing(false)
    }
  }

  const handleImageReplace = async (imageId: string, newImageUrl: string) => {
    const parser = new DOMParser()
    const doc = parser.parseFromString(websiteData.website.html, 'text/html')
    const images = doc.querySelectorAll('img')

    const imageIndex = parseInt(imageId.replace('img-', ''))
    if (images[imageIndex]) {
      images[imageIndex].src = newImageUrl

      const updatedHtml = doc.documentElement.outerHTML
      const updatedData = {
        ...websiteData,
        website: {
          ...websiteData.website,
          html: updatedHtml
        }
      }

      // Add to history
      const newHistory = editHistory.slice(0, historyIndex + 1)
      newHistory.push(updatedData)
      setEditHistory(newHistory)
      setHistoryIndex(newHistory.length - 1)

      onSave(updatedData)
    }
  }

  const handleImageRemove = async (imageId: string) => {
    const parser = new DOMParser()
    const doc = parser.parseFromString(websiteData.website.html, 'text/html')
    const images = doc.querySelectorAll('img')

    const imageIndex = parseInt(imageId.replace('img-', ''))
    if (images[imageIndex]) {
      images[imageIndex].remove()

      const updatedHtml = doc.documentElement.outerHTML
      const updatedData = {
        ...websiteData,
        website: {
          ...websiteData.website,
          html: updatedHtml
        }
      }

      // Add to history
      const newHistory = editHistory.slice(0, historyIndex + 1)
      newHistory.push(updatedData)
      setEditHistory(newHistory)
      setHistoryIndex(newHistory.length - 1)

      onSave(updatedData)
    }
  }

  const handleUndo = () => {
    if (historyIndex > 0) {
      setHistoryIndex(historyIndex - 1)
      onSave(editHistory[historyIndex - 1])
    }
  }

  const handleRedo = () => {
    if (historyIndex < editHistory.length - 1) {
      setHistoryIndex(historyIndex + 1)
      onSave(editHistory[historyIndex + 1])
    }
  }

  const handleSave = () => {
    onSave(websiteData)
  }

  const getPreviewWidth = () => {
    switch (previewMode) {
      case 'mobile': return '375px'
      case 'tablet': return '768px'
      case 'desktop': return '100%'
      default: return '100%'
    }
  }

  const quickEditSuggestions = [
    { category: 'Colors & Style', suggestions: ['Make colors more vibrant', 'Use African-inspired colors', 'Change to modern style'] },
    { category: 'Content', suggestions: ['Add testimonials section', 'Include pricing table', 'Add team section'] },
    { category: 'Layout', suggestions: ['Improve mobile layout', 'Make header bigger', 'Add more spacing'] },
    { category: 'Contact', suggestions: ['Add WhatsApp button', 'Make contact prominent', 'Include social media'] }
  ]

  const tabs = [
    { id: 'chat', label: language === 'sw' ? 'Mazungumzo' : 'Chat Edit', icon: MessageCircle },
    { id: 'images', label: language === 'sw' ? 'Picha' : 'Images', icon: Camera },
    { id: 'preview', label: language === 'sw' ? 'Onyesho' : 'Preview', icon: Eye }
  ]

  return (
    <div className="h-full flex flex-col bg-gray-50">
      {/* Header */}
      <div className="bg-white border-b border-gray-200 px-6 py-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <h1 className="text-xl font-semibold text-gray-900">
              {language === 'sw' ? 'Mhariri wa Muonekano' : 'Visual Editor'}
            </h1>
          </div>

          <div className="flex items-center space-x-3">
            {/* History Controls */}
            <div className="flex items-center space-x-1">
              <button
                onClick={handleUndo}
                disabled={historyIndex <= 0}
                className="p-2 text-gray-500 hover:text-gray-700 disabled:opacity-50 disabled:cursor-not-allowed"
                title={language === 'sw' ? 'Rudisha' : 'Undo'}
              >
                <Undo className="w-4 h-4" />
              </button>
              <button
                onClick={handleRedo}
                disabled={historyIndex >= editHistory.length - 1}
                className="p-2 text-gray-500 hover:text-gray-700 disabled:opacity-50 disabled:cursor-not-allowed"
                title={language === 'sw' ? 'Rudia' : 'Redo'}
              >
                <Redo className="w-4 h-4" />
              </button>
            </div>

            {/* Action Buttons */}
            <button
              onClick={handleSave}
              className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 flex items-center space-x-2"
            >
              <Save className="w-4 h-4" />
              <span>{language === 'sw' ? 'Hifadhi' : 'Save'}</span>
            </button>

            <button
              onClick={() => setShowEditor(!showEditor)}
              className="p-2 text-gray-500 hover:text-gray-700"
            >
              {showEditor ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
            </button>
          </div>
        </div>
      </div>

      {/* Tab Navigation */}
      <div className="bg-white border-b border-gray-200">
        <div className="px-6">
          <nav className="flex space-x-8">
            {tabs.map((tab) => {
              const Icon = tab.icon
              return (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id as any)}
                  className={`py-4 px-1 border-b-2 font-medium text-sm flex items-center space-x-2 ${
                    activeTab === tab.id
                      ? 'border-blue-500 text-blue-600'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  }`}
                >
                  <Icon className="w-4 h-4" />
                  <span>{tab.label}</span>
                </button>
              )
            })}
          </nav>
        </div>
      </div>

      {/* Content Area */}
      <div className="flex-1 flex overflow-hidden">
        {/* Left Panel - Editing Tools */}
        {showEditor && (
          <div className="w-1/3 bg-white border-r border-gray-200 overflow-y-auto">
            <div className="p-6">
              {/* Chat Edit Tab */}
              {activeTab === 'chat' && (
                <div className="space-y-6">
                  <div>
                    <h3 className="text-lg font-semibold text-gray-900 mb-4">
                      {language === 'sw' ? 'Hariri kwa Mazungumzo' : 'Chat Edit'}
                    </h3>
                    <p className="text-sm text-gray-600 mb-4">
                      {language === 'sw'
                        ? 'Eleza mabadiliko unayotaka kufanya kwenye tovuti yako'
                        : 'Describe the changes you want to make to your website'
                      }
                    </p>

                    <div className="space-y-4">
                      <textarea
                        value={editInstructions}
                        onChange={(e) => setEditInstructions(e.target.value)}
                        placeholder={language === 'sw'
                          ? 'Mfano: Badilisha rangi ya header kuwa nyekundu, ongeza sehemu ya testimonials...'
                          : 'Example: Change header color to red, add testimonials section...'
                        }
                        className="w-full h-32 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 resize-none"
                      />

                      <button
                        onClick={handleChatEdit}
                        disabled={isEditing || !editInstructions.trim()}
                        className="w-full px-4 py-3 bg-gradient-to-r from-blue-600 to-purple-600 text-white rounded-lg hover:from-blue-700 hover:to-purple-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center space-x-2"
                      >
                        {isEditing ? (
                          <>
                            <RefreshCw className="w-4 h-4 animate-spin" />
                            <span>{language === 'sw' ? 'Inabadilisha...' : 'Editing...'}</span>
                          </>
                        ) : (
                          <>
                            <Sparkles className="w-4 h-4" />
                            <span>{language === 'sw' ? 'Badilisha kwa AI' : 'Edit with AI'}</span>
                          </>
                        )}
                      </button>
                    </div>

                    {/* Quick Edit Suggestions */}
                    <div className="mt-6">
                      <h4 className="text-sm font-medium text-gray-700 mb-3">
                        {language === 'sw' ? 'Mapendekezo ya Haraka' : 'Quick Suggestions'}
                      </h4>
                      <div className="space-y-3">
                        {quickEditSuggestions.map((category, idx) => (
                          <div key={idx}>
                            <h5 className="text-xs font-medium text-gray-600 mb-2">{category.category}</h5>
                            <div className="flex flex-wrap gap-2">
                              {category.suggestions.map((suggestion, suggestionIdx) => (
                                <button
                                  key={suggestionIdx}
                                  onClick={() => setEditInstructions(suggestion)}
                                  className="px-3 py-1 text-xs bg-gray-100 text-gray-700 rounded-full hover:bg-gray-200 transition-colors"
                                >
                                  {suggestion}
                                </button>
                              ))}
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                  </div>
                </div>
              )}

              {/* Images Tab */}
              {activeTab === 'images' && (
                <div className="space-y-6">
                  <div>
                    <h3 className="text-lg font-semibold text-gray-900 mb-4">
                      {language === 'sw' ? 'Simamia Picha' : 'Manage Images'}
                    </h3>
                    <button
                      onClick={() => setShowImageManager(true)}
                      className="w-full px-4 py-3 bg-green-600 text-white rounded-lg hover:bg-green-700 flex items-center justify-center space-x-2"
                    >
                      <Camera className="w-4 h-4" />
                      <span>{language === 'sw' ? 'Fungua Msimamizi wa Picha' : 'Open Image Manager'}</span>
                    </button>
                  </div>
                </div>
              )}

              {/* Preview Tab */}
              {activeTab === 'preview' && (
                <div className="space-y-6">
                  <div>
                    <h3 className="text-lg font-semibold text-gray-900 mb-4">
                      {language === 'sw' ? 'Muonekano wa Kifaa' : 'Device Preview'}
                    </h3>
                    <div className="grid grid-cols-3 gap-2">
                      <button
                        onClick={() => setPreviewMode('mobile')}
                        className={`p-3 rounded-lg flex flex-col items-center space-y-2 ${
                          previewMode === 'mobile'
                            ? 'bg-blue-100 text-blue-700 border-2 border-blue-500'
                            : 'text-gray-600 hover:bg-gray-100 border-2 border-transparent'
                        }`}
                      >
                        <Smartphone className="w-6 h-6" />
                        <span className="text-xs font-medium">Mobile</span>
                      </button>
                      <button
                        onClick={() => setPreviewMode('tablet')}
                        className={`p-3 rounded-lg flex flex-col items-center space-y-2 ${
                          previewMode === 'tablet'
                            ? 'bg-blue-100 text-blue-700 border-2 border-blue-500'
                            : 'text-gray-600 hover:bg-gray-100 border-2 border-transparent'
                        }`}
                      >
                        <Tablet className="w-6 h-6" />
                        <span className="text-xs font-medium">Tablet</span>
                      </button>
                      <button
                        onClick={() => setPreviewMode('desktop')}
                        className={`p-3 rounded-lg flex flex-col items-center space-y-2 ${
                          previewMode === 'desktop'
                            ? 'bg-blue-100 text-blue-700 border-2 border-blue-500'
                            : 'text-gray-600 hover:bg-gray-100 border-2 border-transparent'
                        }`}
                      >
                        <Monitor className="w-6 h-6" />
                        <span className="text-xs font-medium">Desktop</span>
                      </button>
                    </div>

                    <div className="mt-4">
                      <button
                        onClick={() => {
                          const newWindow = window.open('', '_blank')
                          if (newWindow && websiteData.website?.html) {
                            newWindow.document.write(websiteData.website.html)
                            newWindow.document.close()
                          }
                        }}
                        className="w-full px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 flex items-center justify-center space-x-2"
                      >
                        <ExternalLink className="w-4 h-4" />
                        <span>{language === 'sw' ? 'Fungua katika Tab Mpya' : 'Open in New Tab'}</span>
                      </button>
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>
        )}

        {/* Right Panel - Website Preview */}
        <div className="flex-1 bg-gray-100 flex items-center justify-center p-6">
          <div
            className="bg-white rounded-lg shadow-lg overflow-hidden transition-all duration-300"
            style={{
              width: getPreviewWidth(),
              height: previewMode === 'desktop' ? '100%' : previewMode === 'tablet' ? '800px' : '600px'
            }}
          >
            {websiteData.website?.html ? (
              <SecureIframeRenderer
                html={websiteData.website.html}
                className="w-full h-full"
              />
            ) : (
              <div className="w-full h-full flex items-center justify-center text-gray-500">
                {language === 'sw' ? 'Hakuna tovuti ya kuonyesha' : 'No website to preview'}
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Image Manager Modal */}
      {showImageManager && websiteData.website?.html && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg shadow-xl max-w-4xl w-full mx-4 max-h-[90vh] overflow-hidden">
            <ImageManager
              websiteHtml={websiteData.website.html}
              onImageReplace={handleImageReplace}
              onImageRemove={handleImageRemove}
              onClose={() => setShowImageManager(false)}
              language={language}
            />
          </div>
        </div>
      )}
    </div>
  )
}
