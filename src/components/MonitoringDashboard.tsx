'use client'

import { useState, useEffect } from 'react'
import { 
  Activity, 
  AlertTriangle, 
  CheckCircle, 
  Clock, 
  TrendingUp, 
  TrendingDown,
  Server,
  Database,
  Zap,
  Users,
  Globe,
  RefreshCw
} from 'lucide-react'

interface SystemMetrics {
  uptime: number
  responseTime: number
  errorRate: number
  successRate: number
  activeUsers: number
  totalRequests: number
  cacheHitRate: number
  databaseHealth: 'healthy' | 'warning' | 'critical'
  apiHealth: 'healthy' | 'warning' | 'critical'
  lastUpdated: string
}

interface ErrorMetrics {
  total: number
  byCategory: { [key: string]: number }
  bySeverity: { [key: string]: number }
  recentErrors: Array<{
    id: string
    code: string
    message: string
    severity: string
    timestamp: string
    count: number
  }>
}

interface PerformanceMetrics {
  averageResponseTime: number
  p95ResponseTime: number
  p99ResponseTime: number
  throughput: number
  errorRate: number
  trends: {
    responseTime: number[]
    errorRate: number[]
    throughput: number[]
  }
}

export default function MonitoringDashboard() {
  const [systemMetrics, setSystemMetrics] = useState<SystemMetrics | null>(null)
  const [errorMetrics, setErrorMetrics] = useState<ErrorMetrics | null>(null)
  const [performanceMetrics, setPerformanceMetrics] = useState<PerformanceMetrics | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [lastRefresh, setLastRefresh] = useState<Date>(new Date())

  useEffect(() => {
    fetchMetrics()
    const interval = setInterval(fetchMetrics, 30000) // Refresh every 30 seconds
    return () => clearInterval(interval)
  }, [])

  const fetchMetrics = async () => {
    try {
      setIsLoading(true)
      
      // Simulate API calls to monitoring endpoints
      const [systemData, errorData, performanceData] = await Promise.all([
        fetchSystemMetrics(),
        fetchErrorMetrics(),
        fetchPerformanceMetrics()
      ])

      setSystemMetrics(systemData)
      setErrorMetrics(errorData)
      setPerformanceMetrics(performanceData)
      setLastRefresh(new Date())
    } catch (error) {
      console.error('Failed to fetch monitoring metrics:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const getHealthColor = (health: string) => {
    switch (health) {
      case 'healthy': return 'text-green-600 bg-green-100'
      case 'warning': return 'text-yellow-600 bg-yellow-100'
      case 'critical': return 'text-red-600 bg-red-100'
      default: return 'text-gray-600 bg-gray-100'
    }
  }

  const getHealthIcon = (health: string) => {
    switch (health) {
      case 'healthy': return <CheckCircle className="w-4 h-4" />
      case 'warning': return <AlertTriangle className="w-4 h-4" />
      case 'critical': return <AlertTriangle className="w-4 h-4" />
      default: return <Activity className="w-4 h-4" />
    }
  }

  const formatUptime = (seconds: number) => {
    const days = Math.floor(seconds / 86400)
    const hours = Math.floor((seconds % 86400) / 3600)
    const minutes = Math.floor((seconds % 3600) / 60)
    return `${days}d ${hours}h ${minutes}m`
  }

  const formatNumber = (num: number) => {
    if (num >= 1000000) return `${(num / 1000000).toFixed(1)}M`
    if (num >= 1000) return `${(num / 1000).toFixed(1)}K`
    return num.toString()
  }

  if (isLoading && !systemMetrics) {
    return (
      <div className="p-6 bg-white rounded-lg border">
        <div className="animate-pulse space-y-4">
          <div className="h-6 bg-gray-200 rounded w-1/4"></div>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {[1, 2, 3].map(i => (
              <div key={i} className="h-24 bg-gray-200 rounded"></div>
            ))}
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <h2 className="text-2xl font-bold text-gray-900">System Monitoring</h2>
        <div className="flex items-center gap-4">
          <span className="text-sm text-gray-500">
            Last updated: {lastRefresh.toLocaleTimeString()}
          </span>
          <button
            onClick={fetchMetrics}
            disabled={isLoading}
            className="p-2 text-gray-500 hover:text-gray-700 transition-colors disabled:opacity-50"
          >
            <RefreshCw className={`w-4 h-4 ${isLoading ? 'animate-spin' : ''}`} />
          </button>
        </div>
      </div>

      {/* System Health Overview */}
      {systemMetrics && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <div className="bg-white p-4 rounded-lg border">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">System Status</p>
                <div className={`inline-flex items-center gap-1 px-2 py-1 rounded-full text-xs font-medium ${getHealthColor(systemMetrics.apiHealth)}`}>
                  {getHealthIcon(systemMetrics.apiHealth)}
                  {systemMetrics.apiHealth}
                </div>
              </div>
              <Server className="w-8 h-8 text-blue-500" />
            </div>
          </div>

          <div className="bg-white p-4 rounded-lg border">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Uptime</p>
                <p className="text-lg font-semibold">{formatUptime(systemMetrics.uptime)}</p>
              </div>
              <Clock className="w-8 h-8 text-green-500" />
            </div>
          </div>

          <div className="bg-white p-4 rounded-lg border">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Response Time</p>
                <p className="text-lg font-semibold">{systemMetrics.responseTime}ms</p>
              </div>
              <Zap className="w-8 h-8 text-yellow-500" />
            </div>
          </div>

          <div className="bg-white p-4 rounded-lg border">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Success Rate</p>
                <p className="text-lg font-semibold">{systemMetrics.successRate.toFixed(1)}%</p>
              </div>
              <CheckCircle className="w-8 h-8 text-green-500" />
            </div>
          </div>
        </div>
      )}

      {/* Performance Metrics */}
      {performanceMetrics && (
        <div className="bg-white p-6 rounded-lg border">
          <h3 className="text-lg font-semibold mb-4">Performance Metrics</h3>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div>
              <p className="text-sm text-gray-600 mb-2">Response Time</p>
              <div className="space-y-1">
                <div className="flex justify-between">
                  <span className="text-xs text-gray-500">Average</span>
                  <span className="text-sm font-medium">{performanceMetrics.averageResponseTime}ms</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-xs text-gray-500">95th percentile</span>
                  <span className="text-sm font-medium">{performanceMetrics.p95ResponseTime}ms</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-xs text-gray-500">99th percentile</span>
                  <span className="text-sm font-medium">{performanceMetrics.p99ResponseTime}ms</span>
                </div>
              </div>
            </div>

            <div>
              <p className="text-sm text-gray-600 mb-2">Throughput</p>
              <div className="flex items-center gap-2">
                <span className="text-2xl font-bold">{formatNumber(performanceMetrics.throughput)}</span>
                <span className="text-sm text-gray-500">req/min</span>
                <TrendingUp className="w-4 h-4 text-green-500" />
              </div>
            </div>

            <div>
              <p className="text-sm text-gray-600 mb-2">Error Rate</p>
              <div className="flex items-center gap-2">
                <span className="text-2xl font-bold">{performanceMetrics.errorRate.toFixed(2)}%</span>
                {performanceMetrics.errorRate < 1 ? (
                  <TrendingDown className="w-4 h-4 text-green-500" />
                ) : (
                  <TrendingUp className="w-4 h-4 text-red-500" />
                )}
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Error Metrics */}
      {errorMetrics && (
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <div className="bg-white p-6 rounded-lg border">
            <h3 className="text-lg font-semibold mb-4">Error Distribution</h3>
            <div className="space-y-3">
              <div>
                <p className="text-sm text-gray-600 mb-2">By Severity</p>
                <div className="space-y-2">
                  {Object.entries(errorMetrics.bySeverity).map(([severity, count]) => (
                    <div key={severity} className="flex justify-between items-center">
                      <span className="text-sm capitalize">{severity}</span>
                      <span className="text-sm font-medium">{count}</span>
                    </div>
                  ))}
                </div>
              </div>

              <div>
                <p className="text-sm text-gray-600 mb-2">By Category</p>
                <div className="space-y-2">
                  {Object.entries(errorMetrics.byCategory).map(([category, count]) => (
                    <div key={category} className="flex justify-between items-center">
                      <span className="text-sm capitalize">{category}</span>
                      <span className="text-sm font-medium">{count}</span>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>

          <div className="bg-white p-6 rounded-lg border">
            <h3 className="text-lg font-semibold mb-4">Recent Errors</h3>
            <div className="space-y-3">
              {errorMetrics.recentErrors.slice(0, 5).map((error) => (
                <div key={error.id} className="border-l-4 border-red-400 pl-3 py-2">
                  <div className="flex justify-between items-start">
                    <div>
                      <p className="text-sm font-medium">{error.code}</p>
                      <p className="text-xs text-gray-600">{error.message}</p>
                      <p className="text-xs text-gray-500">{new Date(error.timestamp).toLocaleString()}</p>
                    </div>
                    <span className="text-xs bg-red-100 text-red-600 px-2 py-1 rounded">
                      {error.count}x
                    </span>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      )}

      {/* Additional Metrics */}
      {systemMetrics && (
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="bg-white p-4 rounded-lg border">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Active Users</p>
                <p className="text-2xl font-bold">{formatNumber(systemMetrics.activeUsers)}</p>
              </div>
              <Users className="w-8 h-8 text-purple-500" />
            </div>
          </div>

          <div className="bg-white p-4 rounded-lg border">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Total Requests</p>
                <p className="text-2xl font-bold">{formatNumber(systemMetrics.totalRequests)}</p>
              </div>
              <Globe className="w-8 h-8 text-blue-500" />
            </div>
          </div>

          <div className="bg-white p-4 rounded-lg border">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Cache Hit Rate</p>
                <p className="text-2xl font-bold">{systemMetrics.cacheHitRate.toFixed(1)}%</p>
              </div>
              <Database className="w-8 h-8 text-green-500" />
            </div>
          </div>
        </div>
      )}
    </div>
  )
}

// Mock data functions (in production, these would be real API calls)
async function fetchSystemMetrics(): Promise<SystemMetrics> {
  // Simulate API delay
  await new Promise(resolve => setTimeout(resolve, 500))
  
  return {
    uptime: 2592000, // 30 days in seconds
    responseTime: Math.floor(Math.random() * 200) + 50,
    errorRate: Math.random() * 2,
    successRate: 99.5 + Math.random() * 0.5,
    activeUsers: Math.floor(Math.random() * 1000) + 500,
    totalRequests: Math.floor(Math.random() * 100000) + 500000,
    cacheHitRate: 85 + Math.random() * 10,
    databaseHealth: 'healthy',
    apiHealth: 'healthy',
    lastUpdated: new Date().toISOString()
  }
}

async function fetchErrorMetrics(): Promise<ErrorMetrics> {
  await new Promise(resolve => setTimeout(resolve, 300))
  
  return {
    total: 45,
    byCategory: {
      network: 15,
      validation: 12,
      generation: 8,
      auth: 5,
      system: 5
    },
    bySeverity: {
      low: 25,
      medium: 12,
      high: 6,
      critical: 2
    },
    recentErrors: [
      {
        id: '1',
        code: 'NETWORK_TIMEOUT',
        message: 'Request timeout after 30 seconds',
        severity: 'medium',
        timestamp: new Date(Date.now() - 300000).toISOString(),
        count: 3
      },
      {
        id: '2',
        code: 'GENERATION_FAILED',
        message: 'Website generation process failed',
        severity: 'high',
        timestamp: new Date(Date.now() - 600000).toISOString(),
        count: 1
      },
      {
        id: '3',
        code: 'INVALID_INPUT',
        message: 'Input validation failed',
        severity: 'low',
        timestamp: new Date(Date.now() - 900000).toISOString(),
        count: 5
      }
    ]
  }
}

async function fetchPerformanceMetrics(): Promise<PerformanceMetrics> {
  await new Promise(resolve => setTimeout(resolve, 400))
  
  return {
    averageResponseTime: Math.floor(Math.random() * 200) + 100,
    p95ResponseTime: Math.floor(Math.random() * 500) + 300,
    p99ResponseTime: Math.floor(Math.random() * 1000) + 800,
    throughput: Math.floor(Math.random() * 1000) + 2000,
    errorRate: Math.random() * 2,
    trends: {
      responseTime: Array.from({ length: 24 }, () => Math.floor(Math.random() * 200) + 100),
      errorRate: Array.from({ length: 24 }, () => Math.random() * 2),
      throughput: Array.from({ length: 24 }, () => Math.floor(Math.random() * 1000) + 2000)
    }
  }
}
