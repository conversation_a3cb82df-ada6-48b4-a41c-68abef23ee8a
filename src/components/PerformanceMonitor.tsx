'use client'

import { useState, useEffect } from 'react'
import { 
  <PERSON>ap, 
  Bar<PERSON>hart3, 
  Clock, 
  Image as ImageIcon, 
  Database, 
  Globe,
  TrendingUp,
  AlertTriangle,
  CheckCircle,
  RefreshCw
} from 'lucide-react'

interface PerformanceMetrics {
  pageLoadTime: number
  firstContentfulPaint: number
  largestContentfulPaint: number
  cumulativeLayoutShift: number
  firstInputDelay: number
  totalBlockingTime: number
  imageOptimizationSavings: number
  cacheHitRate: number
}

interface PerformanceReport {
  score: number
  metrics: PerformanceMetrics
  recommendations: string[]
}

interface PerformanceMonitorProps {
  language?: 'en' | 'sw'
  className?: string
}

export default function PerformanceMonitor({ 
  language = 'en', 
  className = '' 
}: PerformanceMonitorProps) {
  const [metrics, setMetrics] = useState<PerformanceMetrics | null>(null)
  const [report, setReport] = useState<PerformanceReport | null>(null)
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const fetchPerformanceData = async () => {
    setIsLoading(true)
    setError(null)

    try {
      // Fetch current metrics
      const metricsResponse = await fetch('/api/performance?action=metrics')
      const metricsData = await metricsResponse.json()

      if (metricsData.success) {
        setMetrics(metricsData.data)
      }

      // Fetch performance report
      const reportResponse = await fetch('/api/performance?action=report')
      const reportData = await reportResponse.json()

      if (reportData.success) {
        setReport(reportData.data)
      }
    } catch (err) {
      setError(language === 'sw' 
        ? 'Imeshindwa kupata data ya utendaji'
        : 'Failed to fetch performance data'
      )
    } finally {
      setIsLoading(false)
    }
  }

  useEffect(() => {
    fetchPerformanceData()
  }, [])

  const getScoreColor = (score: number) => {
    if (score >= 90) return 'text-green-600'
    if (score >= 70) return 'text-yellow-600'
    return 'text-red-600'
  }

  const getScoreIcon = (score: number) => {
    if (score >= 90) return <CheckCircle className="w-5 h-5 text-green-600" />
    if (score >= 70) return <AlertTriangle className="w-5 h-5 text-yellow-600" />
    return <AlertTriangle className="w-5 h-5 text-red-600" />
  }

  const formatTime = (time: number) => {
    if (time < 1000) return `${Math.round(time)}ms`
    return `${(time / 1000).toFixed(2)}s`
  }

  if (isLoading) {
    return (
      <div className={`bg-white rounded-lg shadow-md p-6 ${className}`}>
        <div className="flex items-center justify-center h-32">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
          <span className="ml-3 text-gray-600">
            {language === 'sw' ? 'Inapima utendaji...' : 'Measuring performance...'}
          </span>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className={`bg-white rounded-lg shadow-md p-6 ${className}`}>
        <div className="text-center">
          <AlertTriangle className="w-12 h-12 text-red-500 mx-auto mb-4" />
          <p className="text-red-600 mb-4">{error}</p>
          <button
            onClick={fetchPerformanceData}
            className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors flex items-center gap-2 mx-auto"
          >
            <RefreshCw className="w-4 h-4" />
            {language === 'sw' ? 'Jaribu Tena' : 'Try Again'}
          </button>
        </div>
      </div>
    )
  }

  return (
    <div className={`bg-white rounded-lg shadow-md p-6 ${className}`}>
      <div className="flex items-center justify-between mb-6">
        <h3 className="text-lg font-semibold flex items-center gap-2">
          <BarChart3 className="w-5 h-5 text-blue-600" />
          {language === 'sw' ? 'Ufuatiliaji wa Utendaji' : 'Performance Monitor'}
        </h3>
        <button
          onClick={fetchPerformanceData}
          className="p-2 text-gray-500 hover:text-gray-700 transition-colors"
          title={language === 'sw' ? 'Sasisha' : 'Refresh'}
        >
          <RefreshCw className="w-4 h-4" />
        </button>
      </div>

      {/* Performance Score */}
      {report && (
        <div className="mb-6 p-4 bg-gray-50 rounded-lg">
          <div className="flex items-center justify-between mb-2">
            <span className="text-sm font-medium text-gray-700">
              {language === 'sw' ? 'Alama ya Utendaji' : 'Performance Score'}
            </span>
            {getScoreIcon(report.score)}
          </div>
          <div className="flex items-center gap-2">
            <div className={`text-3xl font-bold ${getScoreColor(report.score)}`}>
              {report.score}
            </div>
            <div className="text-gray-500">/100</div>
          </div>
        </div>
      )}

      {/* Core Web Vitals */}
      {metrics && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-6">
          <div className="p-4 border rounded-lg">
            <div className="flex items-center gap-2 mb-2">
              <Clock className="w-4 h-4 text-blue-500" />
              <span className="text-sm font-medium">
                {language === 'sw' ? 'Muda wa Kupakia' : 'Load Time'}
              </span>
            </div>
            <div className="text-xl font-semibold">
              {formatTime(metrics.pageLoadTime)}
            </div>
          </div>

          <div className="p-4 border rounded-lg">
            <div className="flex items-center gap-2 mb-2">
              <TrendingUp className="w-4 h-4 text-green-500" />
              <span className="text-sm font-medium">FCP</span>
            </div>
            <div className="text-xl font-semibold">
              {formatTime(metrics.firstContentfulPaint)}
            </div>
          </div>

          <div className="p-4 border rounded-lg">
            <div className="flex items-center gap-2 mb-2">
              <ImageIcon className="w-4 h-4 text-purple-500" />
              <span className="text-sm font-medium">LCP</span>
            </div>
            <div className="text-xl font-semibold">
              {formatTime(metrics.largestContentfulPaint)}
            </div>
          </div>

          <div className="p-4 border rounded-lg">
            <div className="flex items-center gap-2 mb-2">
              <Zap className="w-4 h-4 text-yellow-500" />
              <span className="text-sm font-medium">FID</span>
            </div>
            <div className="text-xl font-semibold">
              {formatTime(metrics.firstInputDelay)}
            </div>
          </div>

          <div className="p-4 border rounded-lg">
            <div className="flex items-center gap-2 mb-2">
              <Database className="w-4 h-4 text-indigo-500" />
              <span className="text-sm font-medium">
                {language === 'sw' ? 'Cache Hit Rate' : 'Cache Hit Rate'}
              </span>
            </div>
            <div className="text-xl font-semibold">
              {Math.round(metrics.cacheHitRate)}%
            </div>
          </div>

          <div className="p-4 border rounded-lg">
            <div className="flex items-center gap-2 mb-2">
              <Globe className="w-4 h-4 text-cyan-500" />
              <span className="text-sm font-medium">
                {language === 'sw' ? 'Uokozi wa Picha' : 'Image Savings'}
              </span>
            </div>
            <div className="text-xl font-semibold">
              {Math.round(metrics.imageOptimizationSavings)}%
            </div>
          </div>
        </div>
      )}

      {/* Recommendations */}
      {report && report.recommendations.length > 0 && (
        <div>
          <h4 className="font-medium mb-3 flex items-center gap-2">
            <AlertTriangle className="w-4 h-4 text-orange-500" />
            {language === 'sw' ? 'Mapendekezo ya Uboreshaji' : 'Optimization Recommendations'}
          </h4>
          <ul className="space-y-2">
            {report.recommendations.map((recommendation, index) => (
              <li key={index} className="flex items-start gap-2 text-sm">
                <span className="text-orange-500 mt-1">•</span>
                <span className="text-gray-700">{recommendation}</span>
              </li>
            ))}
          </ul>
        </div>
      )}

      {/* Performance Tips */}
      <div className="mt-6 p-4 bg-blue-50 rounded-lg">
        <h4 className="font-medium mb-2 text-blue-800">
          {language === 'sw' ? 'Vidokezo vya Utendaji' : 'Performance Tips'}
        </h4>
        <ul className="text-sm text-blue-700 space-y-1">
          <li>• {language === 'sw' 
            ? 'Tumia picha zilizoboreswa kwa ukubwa mdogo'
            : 'Use optimized images for faster loading'
          }</li>
          <li>• {language === 'sw' 
            ? 'Washa cache kwa maudhui yanayorudiwa'
            : 'Enable caching for repeated content'
          }</li>
          <li>• {language === 'sw' 
            ? 'Punguza idadi ya vipengele vya ukurasa'
            : 'Minimize the number of page elements'
          }</li>
        </ul>
      </div>
    </div>
  )
}
