'use client'

import React, { useState } from 'react'
import { AlertTriangle, RefreshCw, Info, X, Co<PERSON>, CheckCircle, ExternalLink } from 'lucide-react'

interface ErrorDisplayProps {
  error: {
    code?: string
    message: string
    userMessage?: string
    retryable?: boolean
    severity?: 'low' | 'medium' | 'high' | 'critical'
    category?: 'network' | 'validation' | 'generation' | 'auth' | 'system' | 'user'
  }
  onRetry?: () => void
  onDismiss?: () => void
  showDetails?: boolean
  className?: string
}

const ErrorDisplay: React.FC<ErrorDisplayProps> = ({
  error,
  onRetry,
  onDismiss,
  showDetails = false,
  className = ''
}) => {
  const [showTechnicalDetails, setShowTechnicalDetails] = useState(false)
  const [copied, setCopied] = useState(false)

  const getSeverityColor = (severity?: string) => {
    switch (severity) {
      case 'critical':
        return 'border-red-500 bg-red-50 text-red-800'
      case 'high':
        return 'border-orange-500 bg-orange-50 text-orange-800'
      case 'medium':
        return 'border-yellow-500 bg-yellow-50 text-yellow-800'
      case 'low':
        return 'border-blue-500 bg-blue-50 text-blue-800'
      default:
        return 'border-gray-500 bg-gray-50 text-gray-800'
    }
  }

  const getSeverityIcon = (severity?: string) => {
    switch (severity) {
      case 'critical':
      case 'high':
        return <AlertTriangle className="w-5 h-5 text-red-500" />
      case 'medium':
        return <AlertTriangle className="w-5 h-5 text-yellow-500" />
      case 'low':
        return <Info className="w-5 h-5 text-blue-500" />
      default:
        return <AlertTriangle className="w-5 h-5 text-gray-500" />
    }
  }

  const getRecoveryMessage = (code?: string, category?: string) => {
    if (code === 'NETWORK_TIMEOUT' || code === 'NETWORK_UNAVAILABLE') {
      return {
        title: 'Connection Issue',
        message: 'Please check your internet connection and try again.',
        action: 'Retry'
      }
    }
    
    if (code === 'API_RATE_LIMIT') {
      return {
        title: 'Too Many Requests',
        message: 'Please wait a moment before trying again.',
        action: 'Wait & Retry'
      }
    }
    
    if (code === 'GENERATION_FAILED') {
      return {
        title: 'Generation Error',
        message: 'Try rephrasing your business description or check for any unusual characters.',
        action: 'Try Again'
      }
    }
    
    if (code === 'INVALID_INPUT') {
      return {
        title: 'Input Error',
        message: 'Please provide a more detailed business description (at least 10 characters).',
        action: 'Fix Input'
      }
    }
    
    if (category === 'auth') {
      return {
        title: 'Authentication Required',
        message: 'Please log in to continue using PagesLab.',
        action: 'Log In'
      }
    }
    
    return {
      title: 'Something went wrong',
      message: 'Please try again. If the problem persists, contact support.',
      action: 'Retry'
    }
  }

  const copyErrorDetails = async () => {
    const details = `Error Code: ${error.code || 'Unknown'}
Message: ${error.message}
Severity: ${error.severity || 'Unknown'}
Category: ${error.category || 'Unknown'}
Time: ${new Date().toISOString()}`

    try {
      await navigator.clipboard.writeText(details)
      setCopied(true)
      setTimeout(() => setCopied(false), 2000)
    } catch (err) {
      console.error('Failed to copy error details:', err)
    }
  }

  const recovery = getRecoveryMessage(error.code, error.category)
  const displayMessage = error.userMessage || error.message
  const severityColor = getSeverityColor(error.severity)
  const severityIcon = getSeverityIcon(error.severity)

  return (
    <div className={`border-l-4 p-4 rounded-lg ${severityColor} ${className}`}>
      <div className="flex items-start">
        <div className="flex-shrink-0">
          {severityIcon}
        </div>
        
        <div className="ml-3 flex-1">
          <h3 className="text-sm font-medium">
            {recovery.title}
          </h3>
          
          <div className="mt-2 text-sm">
            <p>{displayMessage}</p>
          </div>
          
          <div className="mt-2 text-sm">
            <p className="text-gray-600">{recovery.message}</p>
          </div>
          
          <div className="mt-4 flex flex-wrap gap-2">
            {error.retryable && onRetry && (
              <button
                onClick={onRetry}
                className="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors"
              >
                <RefreshCw className="w-4 h-4 mr-1" />
                {recovery.action}
              </button>
            )}
            
            {showDetails && (
              <button
                onClick={() => setShowTechnicalDetails(!showTechnicalDetails)}
                className="inline-flex items-center px-3 py-2 border border-gray-300 text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors"
              >
                <Info className="w-4 h-4 mr-1" />
                {showTechnicalDetails ? 'Hide' : 'Show'} Details
              </button>
            )}
            
            <button
              onClick={copyErrorDetails}
              className="inline-flex items-center px-3 py-2 border border-gray-300 text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors"
            >
              {copied ? (
                <>
                  <CheckCircle className="w-4 h-4 mr-1 text-green-500" />
                  Copied!
                </>
              ) : (
                <>
                  <Copy className="w-4 h-4 mr-1" />
                  Copy Details
                </>
              )}
            </button>
            
            <a
              href="mailto:<EMAIL>?subject=Error Report&body=Please describe what you were doing when this error occurred."
              className="inline-flex items-center px-3 py-2 border border-gray-300 text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors"
            >
              <ExternalLink className="w-4 h-4 mr-1" />
              Contact Support
            </a>
          </div>
          
          {showTechnicalDetails && (
            <div className="mt-4 p-3 bg-gray-100 rounded-md">
              <h4 className="text-xs font-medium text-gray-900 mb-2">Technical Details</h4>
              <div className="text-xs text-gray-700 space-y-1">
                <div><strong>Code:</strong> {error.code || 'Unknown'}</div>
                <div><strong>Message:</strong> {error.message}</div>
                <div><strong>Severity:</strong> {error.severity || 'Unknown'}</div>
                <div><strong>Category:</strong> {error.category || 'Unknown'}</div>
                <div><strong>Retryable:</strong> {error.retryable ? 'Yes' : 'No'}</div>
                <div><strong>Time:</strong> {new Date().toISOString()}</div>
              </div>
            </div>
          )}
        </div>
        
        {onDismiss && (
          <div className="ml-auto pl-3">
            <div className="-mx-1.5 -my-1.5">
              <button
                onClick={onDismiss}
                className="inline-flex rounded-md p-1.5 text-gray-500 hover:text-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors"
              >
                <span className="sr-only">Dismiss</span>
                <X className="w-5 h-5" />
              </button>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}

export default ErrorDisplay

// Hook for using error display
export const useErrorDisplay = () => {
  const [error, setError] = useState<ErrorDisplayProps['error'] | null>(null)
  
  const showError = (errorData: ErrorDisplayProps['error']) => {
    setError(errorData)
  }
  
  const clearError = () => {
    setError(null)
  }
  
  const retryLastAction = () => {
    // This would be implemented based on the specific use case
    clearError()
  }
  
  return {
    error,
    showError,
    clearError,
    retryLastAction
  }
}

// Toast notification for errors
export const ErrorToast: React.FC<{
  error: ErrorDisplayProps['error']
  onDismiss: () => void
}> = ({ error, onDismiss }) => {
  return (
    <div className="fixed top-4 right-4 z-50 max-w-sm">
      <ErrorDisplay
        error={error}
        onDismiss={onDismiss}
        className="shadow-lg"
      />
    </div>
  )
}
