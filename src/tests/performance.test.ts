/**
 * Performance Tests for PagesLab
 * Tests image optimization, caching, and website generation performance
 */

import { 
  optimizeImageFile, 
  analyzeImageOptimization,
  imageOptimizer 
} from '../lib/image-optimization'
import { 
  websiteCache, 
  imageCache, 
  performanceMonitor 
} from '../lib/caching'
import { performanceOptimizer } from '../lib/performance-optimizer'

// Mock File for testing
class MockFile extends File {
  constructor(bits: BlobPart[], name: string, options?: FilePropertyBag) {
    super(bits, name, options)
  }
}

// Create a mock image file for testing
const createMockImageFile = (sizeKB: number = 500): File => {
  const buffer = new ArrayBuffer(sizeKB * 1024)
  const uint8Array = new Uint8Array(buffer)
  
  // Fill with some data to simulate image
  for (let i = 0; i < uint8Array.length; i++) {
    uint8Array[i] = Math.floor(Math.random() * 256)
  }
  
  return new MockFile([uint8Array], 'test-image.jpg', { 
    type: 'image/jpeg',
    lastModified: Date.now()
  })
}

// Performance test suite
export class PerformanceTestSuite {
  private results: { [key: string]: any } = {}

  async runAllTests(): Promise<{ [key: string]: any }> {
    console.log('🚀 Starting Performance Test Suite...')
    
    try {
      // Test image optimization performance
      await this.testImageOptimization()
      
      // Test caching performance
      await this.testCaching()
      
      // Test website generation performance
      await this.testWebsiteGeneration()
      
      // Test performance monitoring
      await this.testPerformanceMonitoring()
      
      console.log('✅ All performance tests completed')
      return this.results
    } catch (error) {
      console.error('❌ Performance tests failed:', error)
      throw error
    }
  }

  private async testImageOptimization(): Promise<void> {
    console.log('📸 Testing image optimization...')
    
    const timer = performanceMonitor.startTimer('image-optimization-test')
    
    try {
      // Test with different image sizes
      const testSizes = [100, 500, 1000, 2000] // KB
      const optimizationResults = []
      
      for (const size of testSizes) {
        const mockFile = createMockImageFile(size)
        const startTime = performance.now()
        
        // Test optimization
        const optimized = await optimizeImageFile(mockFile, {
          maxWidth: 1920,
          maxHeight: 1080,
          quality: 0.85,
          format: 'webp'
        })
        
        const endTime = performance.now()
        const processingTime = endTime - startTime
        
        // Test analysis
        const analysis = await analyzeImageOptimization(mockFile)
        
        optimizationResults.push({
          originalSize: size,
          optimizedSize: optimized.size,
          compressionRatio: optimized.compressionRatio,
          processingTime,
          recommendations: analysis.recommendations.length
        })
      }
      
      const totalTime = timer()
      
      this.results.imageOptimization = {
        totalTime,
        averageCompressionRatio: optimizationResults.reduce((sum, r) => sum + r.compressionRatio, 0) / optimizationResults.length,
        averageProcessingTime: optimizationResults.reduce((sum, r) => sum + r.processingTime, 0) / optimizationResults.length,
        results: optimizationResults,
        passed: totalTime < 5000 && optimizationResults.every(r => r.compressionRatio > 0)
      }
      
      console.log(`✅ Image optimization test completed in ${totalTime.toFixed(2)}ms`)
    } catch (error) {
      console.error('❌ Image optimization test failed:', error)
      this.results.imageOptimization = { passed: false, error: error.message }
    }
  }

  private async testCaching(): Promise<void> {
    console.log('💾 Testing caching performance...')
    
    const timer = performanceMonitor.startTimer('caching-test')
    
    try {
      const testData = {
        id: 'test-website-123',
        html: '<html><body><h1>Test Website</h1></body></html>',
        css: 'body { font-family: Arial; }',
        timestamp: Date.now()
      }
      
      // Test website cache
      const cacheSetStart = performance.now()
      await websiteCache.cacheWebsite(testData.id, testData)
      const cacheSetTime = performance.now() - cacheSetStart
      
      const cacheGetStart = performance.now()
      const cachedData = await websiteCache.getCachedWebsite(testData.id)
      const cacheGetTime = performance.now() - cacheGetStart
      
      // Test cache hit
      const cacheExists = await websiteCache.exists(`website:${testData.id}`)
      
      // Test cache statistics
      const stats = await websiteCache.getStats()
      
      const totalTime = timer()
      
      this.results.caching = {
        totalTime,
        cacheSetTime,
        cacheGetTime,
        cacheHit: !!cachedData,
        cacheExists,
        stats,
        passed: cacheSetTime < 100 && cacheGetTime < 50 && !!cachedData
      }
      
      console.log(`✅ Caching test completed in ${totalTime.toFixed(2)}ms`)
    } catch (error) {
      console.error('❌ Caching test failed:', error)
      this.results.caching = { passed: false, error: error.message }
    }
  }

  private async testWebsiteGeneration(): Promise<void> {
    console.log('🌐 Testing website generation performance...')
    
    const timer = performanceMonitor.startTimer('website-generation-test')
    
    try {
      const testWebsiteData = {
        id: 'test-gen-123',
        html: `
          <!DOCTYPE html>
          <html>
            <head>
              <title>Test Business</title>
              <style>
                body { margin: 0; padding: 20px; font-family: Arial, sans-serif; }
                .hero { background: #2563eb; color: white; padding: 40px; text-align: center; }
                .content { padding: 20px; }
                img { max-width: 100%; height: auto; }
              </style>
            </head>
            <body>
              <div class="hero">
                <h1>Test Business</h1>
                <p>Professional services in Nairobi</p>
              </div>
              <div class="content">
                <h2>About Us</h2>
                <p>We provide excellent services to our customers.</p>
                <img src="test-image.jpg" alt="Business" />
              </div>
            </body>
          </html>
        `,
        css: 'body { margin: 0; padding: 0; }',
        metadata: {
          title: 'Test Business',
          description: 'Professional services',
          keywords: ['business', 'services', 'nairobi']
        }
      }
      
      // Test website optimization
      const optimizationStart = performance.now()
      const optimizedWebsite = await performanceOptimizer.optimizeWebsite(testWebsiteData)
      const optimizationTime = performance.now() - optimizationStart
      
      // Verify optimization results
      const htmlSizeReduction = testWebsiteData.html.length - optimizedWebsite.html.length
      const cssOptimized = optimizedWebsite.css.includes('box-sizing:border-box') || 
                          optimizedWebsite.css.length < testWebsiteData.css.length
      
      const totalTime = timer()
      
      this.results.websiteGeneration = {
        totalTime,
        optimizationTime,
        htmlSizeReduction,
        cssOptimized,
        hasPerformanceOptimizations: optimizedWebsite.html.includes('loading="lazy"'),
        passed: optimizationTime < 2000 && htmlSizeReduction >= 0
      }
      
      console.log(`✅ Website generation test completed in ${totalTime.toFixed(2)}ms`)
    } catch (error) {
      console.error('❌ Website generation test failed:', error)
      this.results.websiteGeneration = { passed: false, error: error.message }
    }
  }

  private async testPerformanceMonitoring(): Promise<void> {
    console.log('📊 Testing performance monitoring...')
    
    const timer = performanceMonitor.startTimer('monitoring-test')
    
    try {
      // Record some test metrics
      performanceMonitor.recordMetric('test-metric-1', 100)
      performanceMonitor.recordMetric('test-metric-1', 150)
      performanceMonitor.recordMetric('test-metric-1', 120)
      
      performanceMonitor.recordMetric('test-metric-2', 50)
      performanceMonitor.recordMetric('test-metric-2', 75)
      
      // Get metrics
      const metrics1 = performanceMonitor.getMetrics('test-metric-1')
      const metrics2 = performanceMonitor.getMetrics('test-metric-2')
      const allMetrics = performanceMonitor.getAllMetrics()
      
      const totalTime = timer()
      
      this.results.performanceMonitoring = {
        totalTime,
        metrics1,
        metrics2,
        allMetricsCount: Object.keys(allMetrics).length,
        passed: !!metrics1 && !!metrics2 && metrics1.count === 3 && metrics2.count === 2
      }
      
      console.log(`✅ Performance monitoring test completed in ${totalTime.toFixed(2)}ms`)
    } catch (error) {
      console.error('❌ Performance monitoring test failed:', error)
      this.results.performanceMonitoring = { passed: false, error: error.message }
    }
  }

  getTestSummary(): {
    totalTests: number
    passedTests: number
    failedTests: number
    overallPassed: boolean
    details: { [key: string]: boolean }
  } {
    const testResults = Object.values(this.results)
    const totalTests = testResults.length
    const passedTests = testResults.filter(r => r.passed).length
    const failedTests = totalTests - passedTests
    
    const details: { [key: string]: boolean } = {}
    Object.keys(this.results).forEach(key => {
      details[key] = this.results[key].passed
    })
    
    return {
      totalTests,
      passedTests,
      failedTests,
      overallPassed: failedTests === 0,
      details
    }
  }
}

// Export test runner function
export const runPerformanceTests = async (): Promise<any> => {
  const testSuite = new PerformanceTestSuite()
  const results = await testSuite.runAllTests()
  const summary = testSuite.getTestSummary()
  
  return {
    results,
    summary,
    timestamp: new Date().toISOString()
  }
}

// Performance benchmarks
export const PERFORMANCE_BENCHMARKS = {
  imageOptimization: {
    maxProcessingTime: 2000, // 2 seconds
    minCompressionRatio: 10, // 10% reduction
    maxFileSize: 5 * 1024 * 1024 // 5MB
  },
  caching: {
    maxSetTime: 100, // 100ms
    maxGetTime: 50, // 50ms
    minHitRate: 80 // 80%
  },
  websiteGeneration: {
    maxGenerationTime: 30000, // 30 seconds
    maxOptimizationTime: 2000, // 2 seconds
    targetLoadTime: 2000 // 2 seconds
  },
  coreWebVitals: {
    maxLCP: 2500, // 2.5 seconds
    maxFID: 100, // 100ms
    maxCLS: 0.1, // 0.1
    maxFCP: 1800 // 1.8 seconds
  }
}
