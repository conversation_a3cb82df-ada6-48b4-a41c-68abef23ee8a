/**
 * Error Handling and Monitoring Tests for PagesLab
 * Tests error detection, retry logic, monitoring, and recovery mechanisms
 */

import { 
  errorHandler, 
  createError, 
  withErrorHandling, 
  ERROR_CODES,
  ErrorDetails 
} from '../lib/error-handling'

// Test scenarios for different error types
const errorScenarios = {
  network: {
    timeout: () => {
      const error = new Error('Request timeout')
      error.name = 'TimeoutError'
      return error
    },
    unavailable: () => {
      const error = new Error('Network unavailable')
      error.name = 'NetworkError'
      return error
    },
    rateLimit: () => {
      const error = { status: 429, message: 'Too many requests' }
      return error
    }
  },
  validation: {
    invalidInput: () => createError('INVALID_INPUT', new Error('Invalid data')),
    contentTooShort: () => createError('CONTENT_TOO_SHORT', new Error('Content too short')),
    inappropriateContent: () => createError('INAPPROPRIATE_CONTENT', new Error('Content flagged'))
  },
  generation: {
    failed: () => createError('GENERATION_FAILED', new Error('Generation process failed')),
    imageOptimization: () => createError('IMAGE_OPTIMIZATION_FAILED', new Error('Image processing failed'))
  },
  auth: {
    unauthorized: () => createError('UNAUTHORIZED', new Error('Not authenticated')),
    sessionExpired: () => createError('SESSION_EXPIRED', new Error('Session expired')),
    invalidCredentials: () => createError('INVALID_CREDENTIALS', new Error('Invalid login'))
  },
  system: {
    database: () => createError('DATABASE_ERROR', new Error('Database connection failed')),
    cache: () => createError('CACHE_ERROR', new Error('Cache operation failed')),
    internal: () => createError('INTERNAL_ERROR', new Error('Internal server error'))
  }
}

export class ErrorHandlingTestSuite {
  private results: { [key: string]: any } = {}

  async runAllTests(): Promise<{ [key: string]: any }> {
    console.log('🛡️ Starting Error Handling Test Suite...')
    
    try {
      // Test error creation and classification
      await this.testErrorCreation()
      
      // Test retry logic
      await this.testRetryLogic()
      
      // Test error processing
      await this.testErrorProcessing()
      
      // Test user message generation
      await this.testUserMessages()
      
      // Test recovery suggestions
      await this.testRecoverySuggestions()
      
      // Test monitoring integration
      await this.testMonitoringIntegration()
      
      // Test error boundary scenarios
      await this.testErrorBoundaryScenarios()
      
      // Test performance under error conditions
      await this.testErrorPerformance()
      
      console.log('✅ All error handling tests completed')
      return this.results
    } catch (error) {
      console.error('❌ Error handling tests failed:', error)
      throw error
    }
  }

  private async testErrorCreation(): Promise<void> {
    console.log('🔧 Testing error creation and classification...')
    
    try {
      const testCases = [
        { code: 'NETWORK_TIMEOUT', expectedSeverity: 'medium', expectedCategory: 'network' },
        { code: 'GENERATION_FAILED', expectedSeverity: 'high', expectedCategory: 'generation' },
        { code: 'INTERNAL_ERROR', expectedSeverity: 'critical', expectedCategory: 'system' },
        { code: 'INVALID_INPUT', expectedSeverity: 'low', expectedCategory: 'validation' }
      ]

      const results = testCases.map(testCase => {
        const error = createError(testCase.code as keyof typeof ERROR_CODES, new Error('Test error'))
        
        return {
          code: testCase.code,
          severityCorrect: error.severity === testCase.expectedSeverity,
          categoryCorrect: error.category === testCase.expectedCategory,
          hasUserMessage: !!error.userMessage,
          hasContext: !!error.context,
          hasTimestamp: !!error.context.timestamp
        }
      })

      const allCorrect = results.every(r => 
        r.severityCorrect && r.categoryCorrect && r.hasUserMessage && r.hasContext && r.hasTimestamp
      )

      this.results.errorCreation = {
        testCases: results,
        allCorrect,
        passed: allCorrect
      }

      console.log(`✅ Error creation test completed`)
    } catch (error) {
      console.error('❌ Error creation test failed:', error)
      this.results.errorCreation = { passed: false, error: error.message }
    }
  }

  private async testRetryLogic(): Promise<void> {
    console.log('🔄 Testing retry logic...')
    
    try {
      let attemptCount = 0
      const maxAttempts = 3

      // Test successful retry
      const successfulOperation = async () => {
        attemptCount++
        if (attemptCount < 2) {
          throw errorScenarios.network.timeout()
        }
        return 'success'
      }

      attemptCount = 0
      const successResult = await withErrorHandling(successfulOperation)
      const successfulRetry = successResult === 'success' && attemptCount === 2

      // Test failed retry (non-retryable error)
      attemptCount = 0
      const nonRetryableOperation = async () => {
        attemptCount++
        throw errorScenarios.validation.invalidInput()
      }

      let nonRetryableResult = null
      try {
        await withErrorHandling(nonRetryableOperation)
      } catch (error) {
        nonRetryableResult = error
      }

      const nonRetryableCorrect = attemptCount === 1 && nonRetryableResult !== null

      // Test max attempts reached
      attemptCount = 0
      const alwaysFailOperation = async () => {
        attemptCount++
        throw errorScenarios.network.timeout()
      }

      let maxAttemptsResult = null
      try {
        await withErrorHandling(alwaysFailOperation)
      } catch (error) {
        maxAttemptsResult = error
      }

      const maxAttemptsCorrect = attemptCount === maxAttempts && maxAttemptsResult !== null

      this.results.retryLogic = {
        successfulRetry,
        nonRetryableCorrect,
        maxAttemptsCorrect,
        passed: successfulRetry && nonRetryableCorrect && maxAttemptsCorrect
      }

      console.log(`✅ Retry logic test completed`)
    } catch (error) {
      console.error('❌ Retry logic test failed:', error)
      this.results.retryLogic = { passed: false, error: error.message }
    }
  }

  private async testErrorProcessing(): Promise<void> {
    console.log('⚙️ Testing error processing...')
    
    try {
      // Test different error types
      const testErrors = [
        new TypeError('fetch is not defined'),
        new Error('timeout'),
        { status: 429, message: 'Rate limited' },
        { status: 401, message: 'Unauthorized' },
        'string error',
        null,
        undefined
      ]

      const processedErrors = testErrors.map(error => {
        try {
          return errorHandler.processError(error)
        } catch (e) {
          return null
        }
      })

      const allProcessed = processedErrors.every(error => error !== null)
      const allHaveRequiredFields = processedErrors.every(error => 
        error && error.code && error.userMessage && error.severity && error.category
      )

      // Test specific error type detection
      const networkError = errorHandler.processError(new TypeError('fetch is not defined'))
      const timeoutError = errorHandler.processError(new Error('timeout'))
      const rateLimitError = errorHandler.processError({ status: 429, message: 'Rate limited' })

      const specificDetectionCorrect = 
        networkError.code === ERROR_CODES.NETWORK_UNAVAILABLE &&
        timeoutError.code === ERROR_CODES.NETWORK_TIMEOUT &&
        rateLimitError.code === ERROR_CODES.API_RATE_LIMIT

      this.results.errorProcessing = {
        allProcessed,
        allHaveRequiredFields,
        specificDetectionCorrect,
        processedCount: processedErrors.filter(e => e !== null).length,
        totalCount: testErrors.length,
        passed: allProcessed && allHaveRequiredFields && specificDetectionCorrect
      }

      console.log(`✅ Error processing test completed`)
    } catch (error) {
      console.error('❌ Error processing test failed:', error)
      this.results.errorProcessing = { passed: false, error: error.message }
    }
  }

  private async testUserMessages(): Promise<void> {
    console.log('💬 Testing user message generation...')
    
    try {
      const testCodes = [
        'NETWORK_TIMEOUT',
        'GENERATION_FAILED',
        'INVALID_INPUT',
        'UNAUTHORIZED'
      ]

      const messageTests = testCodes.map(code => {
        const error = createError(code as keyof typeof ERROR_CODES)
        const englishMessage = errorHandler.getUserMessage(error, 'en')
        const swahiliMessage = errorHandler.getUserMessage(error, 'sw')
        
        return {
          code,
          hasEnglishMessage: !!englishMessage && englishMessage.length > 0,
          hasSwahiliMessage: !!swahiliMessage && swahiliMessage.length > 0,
          messagesDifferent: englishMessage !== swahiliMessage,
          englishLength: englishMessage.length,
          swahiliLength: swahiliMessage.length
        }
      })

      const allHaveMessages = messageTests.every(test => 
        test.hasEnglishMessage && test.hasSwahiliMessage
      )
      const allMessagesDifferent = messageTests.every(test => test.messagesDifferent)
      const reasonableLength = messageTests.every(test => 
        test.englishLength > 10 && test.swahiliLength > 10
      )

      this.results.userMessages = {
        messageTests,
        allHaveMessages,
        allMessagesDifferent,
        reasonableLength,
        passed: allHaveMessages && allMessagesDifferent && reasonableLength
      }

      console.log(`✅ User message test completed`)
    } catch (error) {
      console.error('❌ User message test failed:', error)
      this.results.userMessages = { passed: false, error: error.message }
    }
  }

  private async testRecoverySuggestions(): Promise<void> {
    console.log('💡 Testing recovery suggestions...')
    
    try {
      const testCodes = [
        'NETWORK_TIMEOUT',
        'GENERATION_FAILED'
      ]

      const suggestionTests = testCodes.map(code => {
        const error = createError(code as keyof typeof ERROR_CODES)
        const englishSuggestions = errorHandler.getRecoverySuggestions(error, 'en')
        const swahiliSuggestions = errorHandler.getRecoverySuggestions(error, 'sw')
        
        return {
          code,
          hasEnglishSuggestions: englishSuggestions.length > 0,
          hasSwahiliSuggestions: swahiliSuggestions.length > 0,
          englishCount: englishSuggestions.length,
          swahiliCount: swahiliSuggestions.length,
          suggestionsUseful: englishSuggestions.every(s => s.length > 10)
        }
      })

      const allHaveSuggestions = suggestionTests.every(test => 
        test.hasEnglishSuggestions && test.hasSwahiliSuggestions
      )
      const suggestionsUseful = suggestionTests.every(test => test.suggestionsUseful)

      this.results.recoverySuggestions = {
        suggestionTests,
        allHaveSuggestions,
        suggestionsUseful,
        passed: allHaveSuggestions && suggestionsUseful
      }

      console.log(`✅ Recovery suggestions test completed`)
    } catch (error) {
      console.error('❌ Recovery suggestions test failed:', error)
      this.results.recoverySuggestions = { passed: false, error: error.message }
    }
  }

  private async testMonitoringIntegration(): Promise<void> {
    console.log('📊 Testing monitoring integration...')
    
    try {
      // Test error logging
      const testError = createError('GENERATION_FAILED', new Error('Test error'))
      
      // Capture console output to verify logging
      const originalConsoleError = console.error
      let loggedError = null
      console.error = (...args) => {
        loggedError = args
      }

      errorHandler.logError(testError)
      console.error = originalConsoleError

      const errorLogged = loggedError !== null

      // Test performance logging
      const originalConsoleLog = console.log
      let loggedPerformance = null
      console.log = (...args) => {
        if (args[0] && args[0].includes('Performance:')) {
          loggedPerformance = args
        }
      }

      errorHandler.logPerformance('test_metric', 150)
      console.log = originalConsoleLog

      const performanceLogged = loggedPerformance !== null

      this.results.monitoringIntegration = {
        errorLogged,
        performanceLogged,
        passed: errorLogged && performanceLogged
      }

      console.log(`✅ Monitoring integration test completed`)
    } catch (error) {
      console.error('❌ Monitoring integration test failed:', error)
      this.results.monitoringIntegration = { passed: false, error: error.message }
    }
  }

  private async testErrorBoundaryScenarios(): Promise<void> {
    console.log('🛡️ Testing error boundary scenarios...')
    
    try {
      // Test different error scenarios that would trigger error boundaries
      const scenarios = [
        {
          name: 'Component render error',
          error: new Error('Cannot read property of undefined'),
          expectedHandling: true
        },
        {
          name: 'Async operation error',
          error: new Error('Promise rejection'),
          expectedHandling: true
        },
        {
          name: 'Network error in component',
          error: new TypeError('fetch is not defined'),
          expectedHandling: true
        }
      ]

      const scenarioResults = scenarios.map(scenario => {
        const processedError = errorHandler.processError(scenario.error)
        
        return {
          name: scenario.name,
          errorProcessed: !!processedError,
          hasUserMessage: !!processedError.userMessage,
          hasSeverity: !!processedError.severity,
          hasCategory: !!processedError.category
        }
      })

      const allScenariosHandled = scenarioResults.every(result => 
        result.errorProcessed && result.hasUserMessage && result.hasSeverity && result.hasCategory
      )

      this.results.errorBoundaryScenarios = {
        scenarioResults,
        allScenariosHandled,
        passed: allScenariosHandled
      }

      console.log(`✅ Error boundary scenarios test completed`)
    } catch (error) {
      console.error('❌ Error boundary scenarios test failed:', error)
      this.results.errorBoundaryScenarios = { passed: false, error: error.message }
    }
  }

  private async testErrorPerformance(): Promise<void> {
    console.log('⚡ Testing error handling performance...')
    
    try {
      const iterations = 100
      const errors = Array.from({ length: iterations }, (_, i) => 
        new Error(`Test error ${i}`)
      )

      // Test error processing performance
      const startTime = performance.now()
      
      const processedErrors = errors.map(error => 
        errorHandler.processError(error)
      )
      
      const endTime = performance.now()
      const totalTime = endTime - startTime
      const averageTime = totalTime / iterations

      const allProcessed = processedErrors.length === iterations
      const performanceAcceptable = averageTime < 10 // Less than 10ms per error
      const allValid = processedErrors.every(error => 
        error.code && error.userMessage && error.severity
      )

      this.results.errorPerformance = {
        iterations,
        totalTime,
        averageTime,
        allProcessed,
        allValid,
        performanceAcceptable,
        passed: allProcessed && allValid && performanceAcceptable
      }

      console.log(`✅ Error performance test completed in ${totalTime.toFixed(2)}ms`)
    } catch (error) {
      console.error('❌ Error performance test failed:', error)
      this.results.errorPerformance = { passed: false, error: error.message }
    }
  }

  getTestSummary(): {
    totalTests: number
    passedTests: number
    failedTests: number
    overallPassed: boolean
    details: { [key: string]: boolean }
  } {
    const testResults = Object.values(this.results)
    const totalTests = testResults.length
    const passedTests = testResults.filter(r => r.passed).length
    const failedTests = totalTests - passedTests
    
    const details: { [key: string]: boolean } = {}
    Object.keys(this.results).forEach(key => {
      details[key] = this.results[key].passed
    })
    
    return {
      totalTests,
      passedTests,
      failedTests,
      overallPassed: failedTests === 0,
      details
    }
  }
}

// Export test runner function
export const runErrorHandlingTests = async (): Promise<any> => {
  const testSuite = new ErrorHandlingTestSuite()
  const results = await testSuite.runAllTests()
  const summary = testSuite.getTestSummary()
  
  return {
    results,
    summary,
    timestamp: new Date().toISOString()
  }
}

// Error handling benchmarks
export const ERROR_HANDLING_BENCHMARKS = {
  errorProcessing: {
    maxProcessingTime: 10, // Less than 10ms per error
    minAccuracy: 95 // 95% accuracy in error classification
  },
  retryLogic: {
    maxRetryDelay: 10000, // Maximum 10 seconds delay
    minSuccessRate: 80 // 80% success rate with retries
  },
  userMessages: {
    minMessageLength: 10, // At least 10 characters
    bilingualSupport: true // Must support English and Swahili
  },
  monitoring: {
    maxLogDelay: 100, // Less than 100ms to log error
    minDataCapture: 90 // Capture 90% of error context
  },
  recovery: {
    minSuggestionCount: 2, // At least 2 recovery suggestions
    maxSuggestionTime: 50 // Less than 50ms to generate suggestions
  }
}
