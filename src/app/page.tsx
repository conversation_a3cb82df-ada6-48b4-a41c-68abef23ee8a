'use client'

import { useState } from 'react'
import {
  Sparkles,
  Zap,
  Palette,
  Bot,
  Smartphone,
  DollarSign,
  MessageCircle,
  Mic,
  Camera,
  CheckCircle,
  ArrowRight,
  ArrowDown,
  Globe,
  Users,
  Star,
  Lightbulb,
  Scissors,
  ExternalLink,
  UtensilsCrossed,
  Hospital,
  BarChart3,
  PenTool,
  Rocket,
  PartyPopper,
  Download,
  RotateCcw,
  Edit3,
  Phone,
  MapPin,
  Settings,
  X,
  Undo2,
  Upload
} from 'lucide-react'
import ErrorDisplay, { useErrorDisplay } from '@/components/ErrorDisplay'

import AuthModal from '@/components/AuthModal'
import UserDashboard from '@/components/UserDashboard'
import MobileNavigation from '@/components/MobileNavigation'
import ImageManager from '@/components/ImageManager'
import EditingDashboard from '@/components/EditingDashboard'
import { useAuth, websiteAPI } from '@/lib/auth'
import { useDeviceInfo } from '@/components/PWAProvider'
// Pure AI generation - no templates needed
import SecureIframeRenderer from '@/components/SecureIframeRenderer'

export default function Home() {
  const [businessInput, setBusinessInput] = useState('')
  const [isGenerating, setIsGenerating] = useState(false)
  const [generatedWebsite, setGeneratedWebsite] = useState<any>(null)
  const [originalWebsite, setOriginalWebsite] = useState<any>(null) // For revert functionality
  const [error, setError] = useState<string | null>(null)
  const { error: displayError, showError, clearError } = useErrorDisplay()
  const [currentStep, setCurrentStep] = useState<'input' | 'generating' | 'complete'>('input')
  const [progress, setProgress] = useState(0)
  const [isListening, setIsListening] = useState(false)
  const [isUploading, setIsUploading] = useState(false)
  const [language, setLanguage] = useState<'en' | 'sw'>('en')
  const [currentGenerationStep, setCurrentGenerationStep] = useState('')
  const [generationLogs, setGenerationLogs] = useState<string[]>([])
  const [previewContent, setPreviewContent] = useState('')

  const [showAuthModal, setShowAuthModal] = useState(false)
  const [showDashboard, setShowDashboard] = useState(false)
  const [includeImages, setIncludeImages] = useState(true)
  // Kenyan-optimized integrations - WhatsApp and phone focused
  const [selectedIntegrations, setSelectedIntegrations] = useState({
    whatsapp: true,
    phone: true,
    sms: true,
    googleMaps: false,
    mpesa: false,
    socialMedia: false
    // Removed email and contactForm - not preferred in Kenyan market
  })
  // Pure AI mode is always enabled - no template options
  const [editInstructions, setEditInstructions] = useState('')

  const [showImageManager, setShowImageManager] = useState(false)
  const [showEditingDashboard, setShowEditingDashboard] = useState(false)
  const [googleMapsUrl, setGoogleMapsUrl] = useState('')
  const [whatsappNumber, setWhatsappNumber] = useState('')
  const [phoneNumber, setPhoneNumber] = useState('')
  const [socialMediaLinks, setSocialMediaLinks] = useState({
    facebook: '',
    instagram: '',
    twitter: '',
    linkedin: '',
    tiktok: '',
    pinterest: '',
    jiji: ''
  })
  const [mpesaData, setMpesaData] = useState({
    sendMoneyNumber: '',
    tillNumber: '',
    paybillNumber: '',
    businessName: '',
    airtelMoneyNumber: '',
    bankDetails: ''
  })
  const [mpesaNumber, setMpesaNumber] = useState('')

  const { user, isAuthenticated, isLoading: authLoading, logout } = useAuth()
  const { isMobile, isTouch } = useDeviceInfo()

  // Dashboard functions (defined early for conditional render)
  const handleCreateNewFromDashboard = () => {
    setShowDashboard(false)
    setCurrentStep('input')
    setGeneratedWebsite(null)
    setError(null)
    setBusinessInput('')
  }

  const handleEditWebsiteFromDashboard = (website: any) => {
    console.log('🔍 Raw website data from dashboard:', website)

    // Extract HTML content with multiple fallbacks
    let htmlContent = ''
    if (website.html) {
      htmlContent = website.html
      console.log('✅ Using website.html')
    } else if (website.htmlOutput) {
      htmlContent = website.htmlOutput
      console.log('✅ Using website.htmlOutput')
    } else if (website.website?.html) {
      htmlContent = website.website.html
      console.log('✅ Using website.website.html')
    } else {
      console.error('❌ No HTML content found in website data')
      setError('No website content found. Please try generating a new website.')
      return
    }

    // Transform dashboard website data to match expected structure
    const transformedWebsite = {
      website: {
        html: htmlContent,
        css: website.css || website.cssOutput || '' // CSS is embedded in HTML for Pure AI
      },
      businessProfile: website.businessProfile || {
        name: website.name || 'Untitled Website',
        type: 'business',
        location: 'Kenya'
      },
      id: website.id,
      name: website.name,
      description: website.description,
      isPublished: website.isPublished,
      createdAt: website.createdAt,
      updatedAt: website.updatedAt,
      conversationHistory: website.conversationHistory || []
    }

    console.log('🔄 Transformed website data for editing:', {
      hasWebsite: !!transformedWebsite.website,
      hasHtml: !!transformedWebsite.website.html,
      htmlLength: transformedWebsite.website.html.length,
      htmlPreview: transformedWebsite.website.html.substring(0, 100)
    })

    setGeneratedWebsite(transformedWebsite)
    setShowDashboard(false)
    setCurrentStep('complete')
  }

  // Show dashboard if user is authenticated and dashboard is requested
  const shouldShowDashboard = isAuthenticated && showDashboard

  if (shouldShowDashboard) {
    return (
      <UserDashboard
        onCreateNew={handleCreateNewFromDashboard}
        onEditWebsite={handleEditWebsiteFromDashboard}
        language={language}
      />
    )
  }

  const handleGenerateWebsite = async () => {
    if (!businessInput.trim()) {
      setError('Please describe your business')
      return
    }

    setIsGenerating(true)
    setError(null)
    setCurrentStep('generating')
    setProgress(0)
    setGenerationLogs([])
    setPreviewContent('')
    setCurrentGenerationStep('')

    try {
      // Pure AI generation steps - optimized for Kenyan businesses
      const steps = [
        { name: 'Understanding Business Context', progress: 20, duration: 2000 },
        { name: 'Crafting Unique Design Strategy', progress: 45, duration: 3000 },
        { name: 'Generating Modern HTML/CSS', progress: 70, duration: 2500 },
        { name: 'Optimizing for Kenyan Market', progress: 90, duration: 2000 },
        { name: 'Finalizing WhatsApp Integration', progress: 98, duration: 1000 }
      ]

      let currentStepIndex = 0
      const updateProgress = () => {
        if (currentStepIndex < steps.length) {
          const step = steps[currentStepIndex]
          setCurrentGenerationStep(step.name)
          setGenerationLogs(prev => [...prev, `${step.name}...`])

          // Animate progress to target
          const startProgress = currentStepIndex === 0 ? 0 : steps[currentStepIndex - 1].progress
          const targetProgress = step.progress
          const duration = step.duration
          const startTime = Date.now()

          const animateProgress = () => {
            const elapsed = Date.now() - startTime
            const progressRatio = Math.min(elapsed / duration, 1)
            const currentProgress = startProgress + (targetProgress - startProgress) * progressRatio

            setProgress(currentProgress)

            if (progressRatio < 1) {
              requestAnimationFrame(animateProgress)
            } else {
              currentStepIndex++
              if (currentStepIndex < steps.length) {
                setTimeout(updateProgress, 500)
              }
            }
          }

          requestAnimationFrame(animateProgress)
        }
      }

      updateProgress()

      console.log('🎨 Starting Pure AI Website Generation...')
      console.log('📝 Business Description:', businessInput)
      console.log('🖼️ Include Images:', includeImages)
      console.log('🔗 Integrations:', selectedIntegrations)

      // Always use Pure AI - no template fallback
      const response = await fetch('/api/generate-website', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          businessDescription: businessInput,
          includeImages: includeImages,
          integrations: selectedIntegrations,
          integrationData: {
            googleMapsUrl: selectedIntegrations.googleMaps ? googleMapsUrl : undefined,
            whatsappNumber: selectedIntegrations.whatsapp ? whatsappNumber : undefined,
            phoneNumber: selectedIntegrations.phone ? phoneNumber : undefined,
            socialMediaLinks: selectedIntegrations.socialMedia ? socialMediaLinks : undefined,
            mpesaData: selectedIntegrations.mpesa ? mpesaData : undefined
          }
        })
      })

      const result = await response.json()

      setProgress(100)
      setCurrentGenerationStep('Complete')
      setGenerationLogs(prev => [...prev, 'Website generation complete!'])

      if (result.success) {
        console.log('✅ Website generated successfully!')
        console.log('🔍 API RESPONSE DATA STRUCTURE:', {
          hasWebsite: !!result.data.website,
          hasHtml: !!result.data.website?.html,
          htmlLength: result.data.website?.html?.length || 0,
          hasCss: !!result.data.website?.css,
          cssLength: result.data.website?.css?.length || 0,
          businessName: result.data.businessProfile?.name,
          fullDataKeys: Object.keys(result.data),
          websiteKeys: result.data.website ? Object.keys(result.data.website) : 'no website'
        })

        // Check if API is returning demo content
        if (result.data.website?.html?.includes('Coastal Fashion Boutique') && result.data.website?.html?.includes('Premium Service in Kenya')) {
          console.log('🚨 API RETURNED DEMO CONTENT! This should not happen with fresh generation.');
          console.log('🔍 HTML preview from API:', result.data.website.html.substring(0, 500));
        }

        console.log('📝 Setting generatedWebsite state with fresh data...')
        setGeneratedWebsite(result.data)
        setOriginalWebsite(result.data) // Store original for revert functionality
        setCurrentStep('complete')

        // Auto-save to user account if authenticated
        if (isAuthenticated) {
          saveWebsiteToAccount(result.data)
        }


      } else {
        console.error('Website generation failed:', result.error)

        // Use enhanced error display
        showError({
          code: result.code,
          message: result.error || 'Failed to generate website',
          userMessage: result.error,
          retryable: result.retryable !== false,
          severity: result.code === 'INVALID_INPUT' ? 'medium' : 'high',
          category: result.code === 'INVALID_INPUT' ? 'validation' : 'generation'
        })

        setCurrentStep('input')
        setGenerationLogs(prev => [...prev, `Error: ${result.error}`])
      }
    } catch (err) {
      console.error('Network error:', err)

      // Use enhanced error display for network errors
      if (err instanceof Error) {
        if (err.message.includes('fetch') || err.name === 'TypeError') {
          showError({
            code: 'NETWORK_UNAVAILABLE',
            message: 'Network error. Please check your internet connection and try again.',
            retryable: true,
            severity: 'high',
            category: 'network'
          })
        } else if (err.message.includes('timeout') || err.name === 'AbortError') {
          showError({
            code: 'NETWORK_TIMEOUT',
            message: 'The request took too long. Please try again.',
            retryable: true,
            severity: 'medium',
            category: 'network'
          })
        } else {
          showError({
            code: 'UNKNOWN_ERROR',
            message: err.message || 'An unexpected error occurred.',
            retryable: true,
            severity: 'high',
            category: 'system'
          })
        }
      } else {
        showError({
          code: 'UNKNOWN_ERROR',
          message: 'An unexpected error occurred. Please try again.',
          retryable: true,
          severity: 'high',
          category: 'system'
        })
      }

      setCurrentStep('input')
      setGenerationLogs(prev => [...prev, `Error: Network error`])
    } finally {
      setIsGenerating(false)
      setProgress(0)
      setCurrentGenerationStep('')
    }
  }

  // Quality testing is now built into Pure AI generation

  const handleTryExample = (example: string) => {
    setBusinessInput(example)
    setGeneratedWebsite(null)
    setError(null)
    setCurrentStep('input')
    setProgress(0)
  }

  // Voice input functionality
  const startVoiceInput = () => {
    if (!('webkitSpeechRecognition' in window) && !('SpeechRecognition' in window)) {
      setError('Voice input is not supported in your browser. Please use Chrome or Edge.')
      return
    }

    const SpeechRecognition = (window as any).SpeechRecognition || (window as any).webkitSpeechRecognition
    const recognition = new SpeechRecognition()

    recognition.lang = language === 'sw' ? 'sw-KE' : 'en-KE'
    recognition.interimResults = true
    recognition.maxAlternatives = 1

    setIsListening(true)
    setError(null)

    recognition.onresult = (event: any) => {
      const transcript = event.results[event.results.length - 1][0].transcript
      setBusinessInput(transcript)
    }

    recognition.onerror = (event: any) => {
      setIsListening(false)
      setError(`Voice input error: ${event.error}. Please try again.`)
    }

    recognition.onend = () => {
      setIsListening(false)
    }

    recognition.start()
  }

  // Photo upload and analysis functionality
  const handlePhotoUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (!file) return

    if (!file.type.startsWith('image/')) {
      setError('Please upload a valid image file.')
      return
    }

    setIsUploading(true)
    setError(null)

    try {
      // Create a simple image analysis prompt
      const reader = new FileReader()
      reader.onload = async (e) => {
        const imageData = e.target?.result as string

        // For now, we'll use a simple prompt to help users describe their business
        const analysisPrompt = language === 'sw'
          ? 'Elezea biashara yako kulingana na picha hii. Ni aina gani ya biashara, iko wapi, na hutoa huduma gani?'
          : 'Describe your business based on this image. What type of business is it, where is it located, and what services do you offer?'

        setBusinessInput(prev => prev + (prev ? ' ' : '') + analysisPrompt)
        setIsUploading(false)
      }

      reader.readAsDataURL(file)
    } catch (error) {
      setIsUploading(false)
      setError('Failed to process image. Please try again.')
    }
  }

  // Language toggle
  const toggleLanguage = () => {
    setLanguage(prev => prev === 'en' ? 'sw' : 'en')
  }



  // Authentication and dashboard functions
  const openAuthModal = () => {
    setShowAuthModal(true)
  }

  const closeAuthModal = () => {
    setShowAuthModal(false)
  }

  const openDashboard = () => {
    setShowDashboard(true)
  }

  const closeDashboard = () => {
    setShowDashboard(false)
  }



  // Auto-save generated website to user account
  const saveWebsiteToAccount = async (websiteData: any) => {
    if (!isAuthenticated) {
      console.log('❌ User not authenticated, skipping website save')
      return
    }

    console.log('💾 Attempting to save website to account...', {
      isAuthenticated,
      hasUser: !!user,
      businessName: websiteData.businessProfile?.name,
      hasHtml: !!websiteData.website?.html
    })

    try {
      const result = await websiteAPI.createWebsite({
        name: websiteData.businessProfile.name,
        description: `Website for ${websiteData.businessProfile.name}`,
        html: websiteData.website.html,
        businessProfile: websiteData.businessProfile,
        isPublished: false
      })

      if (result.success) {
        console.log('✅ Website saved to account successfully:', result.website?.id)
      } else {
        console.error('❌ Failed to save website to account:', result.error)
      }
    } catch (error) {
      console.error('❌ Exception while saving website to account:', error)
    }
  }

  const handleStartOver = () => {
    setBusinessInput('')
    setGeneratedWebsite(null)
    setOriginalWebsite(null)
    setError(null)
    setCurrentStep('input')
    setProgress(0)
    setEditInstructions('')
  }

  const handleRevertChanges = () => {
    if (originalWebsite) {
      setGeneratedWebsite(originalWebsite)
      setEditInstructions('')
      console.log('✅ Reverted to original website')
    }
  }

  const handleChatEdit = async () => {
    if (!editInstructions.trim() || !generatedWebsite?.website?.html) {
      setError('Please provide edit instructions and ensure a website is generated first.')
      return
    }

    setIsGenerating(true)
    setError(null)
    setProgress(0)
    setCurrentGenerationStep('Analyzing changes...')
    setGenerationLogs(['Starting edit process...'])

    // Create AbortController for timeout handling
    const controller = new AbortController()
    const timeoutId = setTimeout(() => {
      controller.abort()
    }, 120000) // 2 minute timeout for edits

    try {
      console.log('💬 Starting chat-based edit...')
      console.log('📝 Edit instructions:', editInstructions)
      console.log('📄 Current website length:', generatedWebsite.website.html.length)

      // Enhanced progress tracking
      const editSteps = [
        { name: 'Analyzing edit request', progress: 15, duration: 1000 },
        { name: 'Processing changes with AI', progress: 40, duration: 8000 },
        { name: 'Applying modifications', progress: 70, duration: 3000 },
        { name: 'Validating quality', progress: 85, duration: 2000 },
        { name: 'Finalizing updates', progress: 95, duration: 1000 }
      ]

      let currentStepIndex = 0
      const updateEditProgress = () => {
        if (currentStepIndex < editSteps.length) {
          const step = editSteps[currentStepIndex]
          setCurrentGenerationStep(step.name)
          setGenerationLogs(prev => [...prev, `${step.name}...`])

          // Animate progress
          const startProgress = currentStepIndex === 0 ? 0 : editSteps[currentStepIndex - 1].progress
          const targetProgress = step.progress
          const duration = step.duration
          const startTime = Date.now()

          const animateProgress = () => {
            const elapsed = Date.now() - startTime
            const progressRatio = Math.min(elapsed / duration, 1)
            const currentProgress = startProgress + (targetProgress - startProgress) * progressRatio

            setProgress(currentProgress)

            if (progressRatio < 1 && !controller.signal.aborted) {
              requestAnimationFrame(animateProgress)
            } else {
              currentStepIndex++
              if (currentStepIndex < editSteps.length && !controller.signal.aborted) {
                setTimeout(updateEditProgress, 200)
              }
            }
          }

          requestAnimationFrame(animateProgress)
        }
      }

      updateEditProgress()

      const response = await fetch('/api/edit-website-chat', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          currentWebsite: generatedWebsite.website.html,
          editInstructions: editInstructions,
          conversationHistory: generatedWebsite.conversationHistory || []
        }),
        signal: controller.signal
      })

      clearTimeout(timeoutId)

      if (!response.ok) {
        const errorText = await response.text()
        throw new Error(`Edit request failed: ${response.status} - ${errorText}`)
      }

      const result = await response.json()

      if (result.success) {
        console.log('✅ Chat edit successful!')
        console.log('🎨 Edit result data:', result.data)

        setProgress(100)
        setCurrentGenerationStep('Edit complete!')
        setGenerationLogs(prev => [...prev, 'Edit completed successfully!'])

        // Handle different response structures with better validation
        let updatedWebsite
        if (result.data?.website?.html) {
          updatedWebsite = {
            ...generatedWebsite,
            website: {
              ...result.data.website,
              isEdited: true,
              lastEditTime: new Date().toISOString()
            },
            conversationHistory: result.data.conversationHistory || [],
            lastEdit: result.data.editSummary || {
              appliedChanges: ['Website updated'],
              timestamp: new Date().toISOString(),
              instruction: editInstructions
            }
          }
        } else if (result.data?.html) {
          updatedWebsite = {
            ...generatedWebsite,
            website: {
              ...generatedWebsite.website,
              html: result.data.html,
              isEdited: true,
              lastEditTime: new Date().toISOString()
            },
            conversationHistory: result.data.conversationHistory || [],
            lastEdit: {
              appliedChanges: ['Website updated'],
              timestamp: new Date().toISOString(),
              instruction: editInstructions
            }
          }
        } else {
          console.error('❌ Invalid response structure:', result.data)
          throw new Error('Invalid response format from edit API')
        }

        setGeneratedWebsite(updatedWebsite)
        setEditInstructions('')

        // Show success message
        setTimeout(() => {
          setProgress(0)
          setCurrentGenerationStep('')
          setGenerationLogs([])
        }, 2000)

        console.log('✅ Website updated with your edits!')
      } else {
        console.error('❌ Chat edit failed:', result.error)
        throw new Error(result.error || 'Failed to apply edits')
      }
    } catch (error) {
      clearTimeout(timeoutId)
      console.error('❌ Chat edit error:', error)

      setGenerationLogs(prev => [...prev, `Error: ${error instanceof Error ? error.message : 'Unknown error'}`])

      if (error instanceof Error) {
        if (error.name === 'AbortError') {
          setError('Edit request timed out. Please try with simpler instructions or try again.')
        } else if (error.message.includes('fetch')) {
          setError('Network error. Please check your connection and try again.')
        } else {
          setError(`Edit failed: ${error.message}`)
        }
      } else {
        setError('Failed to apply edits. Please try again.')
      }
    } finally {
      setIsGenerating(false)
      setProgress(0)
      setCurrentGenerationStep('')
    }
  }

  const downloadFile = (content: string, filename: string, type: string) => {
    const blob = new Blob([content], { type })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = filename
    a.click()
    URL.revokeObjectURL(url)
  }

  // Image management functions
  const handleImageReplace = async (imageId: string, newImageUrl: string) => {
    if (!generatedWebsite?.website?.html) return

    try {
      // Replace the image in the HTML
      const parser = new DOMParser()
      const doc = parser.parseFromString(generatedWebsite.website.html, 'text/html')

      console.log(`🔄 Replacing image with ID: ${imageId}`)

      // Handle regular img tags (img-{index})
      if (imageId.startsWith('img-')) {
        const images = doc.querySelectorAll('img')
        const imageIndex = parseInt(imageId.replace('img-', ''))

        if (images[imageIndex]) {
          const imageElement = images[imageIndex]
          const oldSrc = imageElement.src
          imageElement.src = newImageUrl

          console.log(`✅ Replaced img tag: ${oldSrc} → ${newImageUrl}`)

          // Also check if this image is used as a background image anywhere
          const allElements = doc.querySelectorAll('*')
          allElements.forEach(element => {
            const style = element.getAttribute('style') || ''
            if (style.includes(oldSrc)) {
              const updatedStyle = style.replace(
                new RegExp(oldSrc.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'), 'g'),
                newImageUrl
              )
              element.setAttribute('style', updatedStyle)
              console.log(`✅ Also updated background image reference`)
            }
          })
        }
      }

      // Handle background images (bg-{index})
      else if (imageId.startsWith('bg-')) {
        const allElements = doc.querySelectorAll('*')
        const bgIndex = parseInt(imageId.replace('bg-', ''))
        let currentIndex = 0
        let replaced = false

        allElements.forEach(element => {
          const style = element.getAttribute('style') || ''
          const backgroundImageMatch = style.match(/background-image:\s*url\(['"]?([^'"]+)['"]?\)/)

          if (backgroundImageMatch && backgroundImageMatch[1]) {
            if (currentIndex === bgIndex) {
              const oldSrc = backgroundImageMatch[1]
              const updatedStyle = style.replace(
                new RegExp(`url\\(['"]?${oldSrc.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')}['"]?\\)`, 'g'),
                `url('${newImageUrl}')`
              )
              element.setAttribute('style', updatedStyle)
              console.log(`✅ Replaced background image: ${oldSrc} → ${newImageUrl}`)
              replaced = true
            }
            currentIndex++
          }
        })

        if (!replaced) {
          console.warn(`⚠️ Background image with index ${bgIndex} not found`)
        }
      }

      // Update the website with modified HTML
      const updatedHtml = doc.documentElement.outerHTML
      setGeneratedWebsite((prev: any) => ({
        ...prev,
        website: {
          ...prev.website,
          html: updatedHtml
        }
      }))

      console.log('✅ Image replacement completed')
    } catch (error) {
      console.error('❌ Error replacing image:', error)
      setError('Failed to replace image. Please try again.')
    }
  }

  const handleImageRemove = async (imageId: string) => {
    if (!generatedWebsite?.website?.html) return

    try {
      // Remove the image from the HTML
      const parser = new DOMParser()
      const doc = parser.parseFromString(generatedWebsite.website.html, 'text/html')
      const images = doc.querySelectorAll('img')

      // Find the image by index (imageId format: img-{index})
      const imageIndex = parseInt(imageId.replace('img-', ''))
      if (images[imageIndex]) {
        const imageElement = images[imageIndex]
        const parentContainer = imageElement.closest('.image-container, .gallery-item, .testimonial-image, figure, .img-wrapper')

        if (parentContainer) {
          // Remove the entire container if it exists
          parentContainer.remove()
        } else {
          // Just remove the image if no specific container is found
          imageElement.remove()
        }

        // Update the website with modified HTML
        const updatedHtml = doc.documentElement.outerHTML
        setGeneratedWebsite((prev: any) => ({
          ...prev,
          website: {
            ...prev.website,
            html: updatedHtml
          }
        }))

        console.log('✅ Image removed successfully')
      }
    } catch (error) {
      console.error('❌ Error removing image:', error)
      setError('Failed to remove image. Please try again.')
    }
  }

  return (
    <main className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50">
      {/* Mobile Navigation */}
      <MobileNavigation
        user={user}
        onLogin={openAuthModal}
        onLogout={async () => {
          try {
            await logout()
            // Redirect to home page after logout
            window.location.reload()
          } catch (error) {
            console.error('Logout failed:', error)
          }
        }}
        onDashboard={openDashboard}
        onCreateNew={() => {
          setCurrentStep('input')
          setGeneratedWebsite(null)
          setError(null)
          setBusinessInput('')
        }}
        currentPage={currentStep === 'input' ? 'home' : currentStep === 'complete' ? 'editor' : 'home'}
      />

      {/* Error Display */}
      {displayError && (
        <div className={`fixed ${isMobile ? 'top-20' : 'top-20'} left-1/2 transform -translate-x-1/2 z-40 max-w-md w-full mx-4`}>
          <ErrorDisplay
            error={displayError}
            onRetry={handleGenerateWebsite}
            onDismiss={clearError}
            showDetails={true}
          />
        </div>
      )}

      {/* Clean Navigation Header */}
      <div className="fixed top-0 left-0 right-0 z-50 glass-effect border-b border-gray-100">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            {/* Logo */}
            <div className="flex items-center space-x-3">
              <div className="w-8 h-8 bg-gradient-to-r from-blue-600 to-emerald-600 rounded-lg flex items-center justify-center hover-glow">
                <span className="text-white text-sm font-bold">P</span>
              </div>
              <span className="font-bold text-gray-900 text-xl">PagesLab</span>
            </div>

            {/* Navigation Items */}
            <div className="hidden md:flex items-center space-x-6">
              <button
                onClick={toggleLanguage}
                className="btn-ghost"
                title={language === 'en' ? 'Switch to Swahili' : 'Switch to English'}
              >
                <Globe className="w-4 h-4 mr-1" />
                <span>{language === 'en' ? 'EN' : 'SW'}</span>
              </button>
            </div>

            {/* Authentication Section */}
            <div className="flex items-center space-x-3">
              {authLoading ? (
                <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
              ) : isAuthenticated ? (
                <div className="flex items-center space-x-3">
                  <span className="text-muted hidden sm:inline">
                    {language === 'sw' ? 'Karibu' : 'Welcome'}, {user?.name}
                  </span>
                  <button
                    onClick={openDashboard}
                    className="btn-primary py-2 px-4 text-sm"
                  >
                    {language === 'sw' ? 'Dashibodi' : 'Dashboard'}
                  </button>
                </div>
              ) : (
                <button
                  onClick={openAuthModal}
                  className="btn-primary py-2 px-4 text-sm"
                >
                  {language === 'sw' ? 'Ingia' : 'Sign In'}
                </button>
              )}
            </div>
          </div>
        </div>
      </div>

      <div className={`max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 ${isMobile ? 'py-8 pt-24' : 'py-16 pt-32'}`}>
        {currentStep === 'input' && (
          <>
            {/* Hero Section */}
            <div className="text-center mb-16 animate-fade-in-up">
              <div className="mb-8">
                <div className="inline-flex items-center space-x-2 bg-blue-50 text-blue-700 px-4 py-2 rounded-full text-sm font-medium mb-6 animate-scale-in delay-100">
                  <Sparkles className="w-4 h-4" />
                  <span>{language === 'sw' ? 'AI-Powered Website Generator' : 'AI-Powered Website Generator'}</span>
                </div>

                <h1 className="heading-primary mb-6 animate-fade-in-up delay-200">
                  {language === 'sw'
                    ? 'Tovuti za Biashara za Kenya'
                    : 'Professional Websites for'
                  }
                  <br />
                  <span className="text-gradient">
                    {language === 'sw' ? 'Zimetengenezwa kwa Dakika' : 'Kenyan Businesses'}
                  </span>
                </h1>

                <p className="text-body mb-8 max-w-3xl mx-auto animate-fade-in-up delay-300">
                  {language === 'sw'
                    ? 'Tengeneza tovuti za kibiashara za kisasa kwa kutumia AI. Zina M-Pesa, WhatsApp, na muundo wa simu. Hakuna coding.'
                    : 'Create stunning, mobile-optimized websites in 60 seconds. Built for Kenya with M-Pesa payments, WhatsApp integration, and local business features.'
                  }
                </p>
              </div>

              {/* Value Proposition Cards */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 sm:gap-6 mb-12 max-w-4xl mx-auto">
                <div className="card-modern p-4 sm:p-6 text-center hover-lift animate-fade-in-up delay-100">
                  <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mb-4 mx-auto animate-bounce-gentle">
                    <Zap className="w-6 h-6 text-blue-600" />
                  </div>
                  <h3 className="font-semibold text-gray-900 mb-2 text-base sm:text-lg">
                    {language === 'sw' ? 'Haraka' : '60-Second Setup'}
                  </h3>
                  <p className="text-muted text-sm sm:text-base">
                    {language === 'sw' ? 'Tovuti yako itakuwa tayari kwa dakika moja' : 'Your website will be ready in under a minute'}
                  </p>
                </div>

                <div className="card-elevated p-4 sm:p-6 text-center hover-lift animate-fade-in-up delay-200">
                  <div className="w-12 h-12 bg-emerald-100 rounded-lg flex items-center justify-center mb-4 mx-auto animate-bounce-gentle">
                    <Smartphone className="w-6 h-6 text-emerald-600" />
                  </div>
                  <h3 className="font-semibold text-gray-900 mb-2 text-base sm:text-lg">
                    {language === 'sw' ? 'Simu Kwanza' : 'Mobile-Optimized'}
                  </h3>
                  <p className="text-muted text-sm sm:text-base">
                    {language === 'sw' ? 'Inafanya kazi vizuri kwenye simu zote' : 'Perfect experience on all mobile devices'}
                  </p>
                </div>

                <div className="card-orange p-4 sm:p-6 text-center hover-lift animate-fade-in-up delay-300">
                  <div className="w-12 h-12 bg-orange-100 rounded-lg flex items-center justify-center mb-4 mx-auto animate-bounce-gentle">
                    <DollarSign className="w-6 h-6 text-orange-600" />
                  </div>
                  <h3 className="font-semibold text-gray-900 mb-2 text-base sm:text-lg">
                    {language === 'sw' ? 'M-Pesa Tayari' : 'Kenya-Ready'}
                  </h3>
                  <p className="text-muted text-sm sm:text-base">
                    {language === 'sw' ? 'M-Pesa, WhatsApp na huduma za Kenya' : 'M-Pesa payments, WhatsApp, and local features'}
                  </p>
                </div>
              </div>
            </div>

            {/* Process Flow Section */}
            <div className="mb-16 animate-fade-in-up delay-500">
              <div className="text-center mb-12">
                <h2 className="heading-secondary mb-4">
                  {language === 'sw' ? 'Jinsi Inavyofanya Kazi' : 'How It Works'}
                </h2>
                <p className="text-body max-w-2xl mx-auto">
                  {language === 'sw'
                    ? 'Hatua rahisi tatu tu za kutengeneza tovuti yako ya kibiashara'
                    : 'Just three simple steps to create your professional business website'
                  }
                </p>
              </div>

              {/* Desktop Layout - Horizontal */}
              <div className="hidden md:block">
                <div className="flex items-center justify-between max-w-5xl mx-auto">
                  {/* Step 1 */}
                  <div className="flex-1 text-center animate-slide-in-left delay-100">
                    <div className="relative">
                      <div className="w-16 h-16 bg-gradient-to-r from-blue-600 to-blue-700 rounded-full flex items-center justify-center mx-auto mb-4 shadow-lg">
                        <Edit3 className="w-8 h-8 text-white" />
                      </div>
                      <div className="absolute -top-2 -right-2 w-6 h-6 bg-emerald-500 rounded-full flex items-center justify-center">
                        <span className="text-white text-sm font-bold">1</span>
                      </div>
                    </div>
                    <h3 className="font-semibold text-gray-900 mb-2">
                      {language === 'sw' ? 'Eleza Biashara Yako' : 'Describe Your Business'}
                    </h3>
                    <p className="text-muted text-sm">
                      {language === 'sw'
                        ? 'Tuambie kuhusu biashara yako, mahali, na huduma'
                        : 'Tell us about your business, location, and services'
                      }
                    </p>
                  </div>

                  {/* Arrow 1 */}
                  <div className="flex-shrink-0 mx-8">
                    <ArrowRight className="w-6 h-6 text-gray-400" />
                  </div>

                  {/* Step 2 */}
                  <div className="flex-1 text-center animate-fade-in-up delay-200">
                    <div className="relative">
                      <div className="w-16 h-16 bg-gradient-to-r from-emerald-600 to-emerald-700 rounded-full flex items-center justify-center mx-auto mb-4 shadow-lg">
                        <Settings className="w-8 h-8 text-white" />
                      </div>
                      <div className="absolute -top-2 -right-2 w-6 h-6 bg-blue-500 rounded-full flex items-center justify-center">
                        <span className="text-white text-sm font-bold">2</span>
                      </div>
                    </div>
                    <h3 className="font-semibold text-gray-900 mb-2">
                      {language === 'sw' ? 'Chagua Viunganishi' : 'Choose Features'}
                    </h3>
                    <p className="text-muted text-sm">
                      {language === 'sw'
                        ? 'Ongeza WhatsApp, M-Pesa, na viunganishi vingine'
                        : 'Add WhatsApp, M-Pesa, and other integrations'
                      }
                    </p>
                  </div>

                  {/* Arrow 2 */}
                  <div className="flex-shrink-0 mx-8">
                    <ArrowRight className="w-6 h-6 text-gray-400" />
                  </div>

                  {/* Step 3 */}
                  <div className="flex-1 text-center animate-slide-in-right delay-300">
                    <div className="relative">
                      <div className="w-16 h-16 bg-gradient-to-r from-orange-600 to-orange-700 rounded-full flex items-center justify-center mx-auto mb-4 shadow-lg">
                        <Rocket className="w-8 h-8 text-white" />
                      </div>
                      <div className="absolute -top-2 -right-2 w-6 h-6 bg-orange-500 rounded-full flex items-center justify-center">
                        <span className="text-white text-sm font-bold">3</span>
                      </div>
                    </div>
                    <h3 className="font-semibold text-gray-900 mb-2">
                      {language === 'sw' ? 'Tovuti Tayari!' : 'Website Ready!'}
                    </h3>
                    <p className="text-muted text-sm">
                      {language === 'sw'
                        ? 'AI inaunda tovuti yako ya kibiashara kwa sekunde'
                        : 'AI creates your professional website in seconds'
                      }
                    </p>
                  </div>
                </div>
              </div>

              {/* Mobile Layout - Vertical */}
              <div className="md:hidden space-y-8">
                {/* Step 1 */}
                <div className="flex items-start space-x-4 animate-slide-in-left delay-100">
                  <div className="relative flex-shrink-0">
                    <div className="w-12 h-12 bg-gradient-to-r from-blue-600 to-blue-700 rounded-full flex items-center justify-center shadow-lg">
                      <Edit3 className="w-6 h-6 text-white" />
                    </div>
                    <div className="absolute -top-1 -right-1 w-5 h-5 bg-emerald-500 rounded-full flex items-center justify-center">
                      <span className="text-white text-xs font-bold">1</span>
                    </div>
                  </div>
                  <div className="flex-1">
                    <h3 className="font-semibold text-gray-900 mb-1">
                      {language === 'sw' ? 'Eleza Biashara Yako' : 'Describe Your Business'}
                    </h3>
                    <p className="text-muted text-sm">
                      {language === 'sw'
                        ? 'Tuambie kuhusu biashara yako, mahali, na huduma'
                        : 'Tell us about your business, location, and services'
                      }
                    </p>
                  </div>
                </div>

                {/* Arrow Down */}
                <div className="flex justify-center">
                  <ArrowDown className="w-5 h-5 text-gray-400" />
                </div>

                {/* Step 2 */}
                <div className="flex items-start space-x-4 animate-fade-in-up delay-200">
                  <div className="relative flex-shrink-0">
                    <div className="w-12 h-12 bg-gradient-to-r from-emerald-600 to-emerald-700 rounded-full flex items-center justify-center shadow-lg">
                      <Settings className="w-6 h-6 text-white" />
                    </div>
                    <div className="absolute -top-1 -right-1 w-5 h-5 bg-blue-500 rounded-full flex items-center justify-center">
                      <span className="text-white text-xs font-bold">2</span>
                    </div>
                  </div>
                  <div className="flex-1">
                    <h3 className="font-semibold text-gray-900 mb-1">
                      {language === 'sw' ? 'Chagua Viunganishi' : 'Choose Features'}
                    </h3>
                    <p className="text-muted text-sm">
                      {language === 'sw'
                        ? 'Ongeza WhatsApp, M-Pesa, na viunganishi vingine'
                        : 'Add WhatsApp, M-Pesa, and other integrations'
                      }
                    </p>
                  </div>
                </div>

                {/* Arrow Down */}
                <div className="flex justify-center">
                  <ArrowDown className="w-5 h-5 text-gray-400" />
                </div>

                {/* Step 3 */}
                <div className="flex items-start space-x-4 animate-slide-in-right delay-300">
                  <div className="relative flex-shrink-0">
                    <div className="w-12 h-12 bg-gradient-to-r from-orange-600 to-orange-700 rounded-full flex items-center justify-center shadow-lg">
                      <Rocket className="w-6 h-6 text-white" />
                    </div>
                    <div className="absolute -top-1 -right-1 w-5 h-5 bg-orange-500 rounded-full flex items-center justify-center">
                      <span className="text-white text-xs font-bold">3</span>
                    </div>
                  </div>
                  <div className="flex-1">
                    <h3 className="font-semibold text-gray-900 mb-1">
                      {language === 'sw' ? 'Tovuti Tayari!' : 'Website Ready!'}
                    </h3>
                    <p className="text-muted text-sm">
                      {language === 'sw'
                        ? 'AI inaunda tovuti yako ya kibiashara kwa sekunde'
                        : 'AI creates your professional website in seconds'
                      }
                    </p>
                  </div>
                </div>
              </div>
            </div>

            {/* Social Proof Section */}
            <div className="mb-16 animate-fade-in-up delay-500">
              {/* Trust Indicators */}
              <div className="text-center mb-12">
                <div className="inline-flex items-center space-x-2 bg-emerald-50 text-emerald-700 px-4 py-2 rounded-full text-sm font-medium mb-6">
                  <Star className="w-4 h-4 fill-current" />
                  <span>{language === 'sw' ? 'Imekubalika na Biashara za Kenya' : 'Trusted by Kenyan Businesses'}</span>
                </div>

                <div className="flex flex-col sm:flex-row items-center justify-center space-y-6 sm:space-y-0 sm:space-x-8 mb-8">
                  <div className="text-center">
                    <div className="text-3xl font-bold text-gray-900 mb-1">500+</div>
                    <div className="text-muted text-sm">
                      {language === 'sw' ? 'Tovuti Zimeundwa' : 'Websites Created'}
                    </div>
                  </div>
                  <div className="hidden sm:block w-px h-12 bg-gray-200"></div>
                  <div className="text-center">
                    <div className="text-3xl font-bold text-gray-900 mb-1">98%</div>
                    <div className="text-muted text-sm">
                      {language === 'sw' ? 'Wateja Wameridhika' : 'Happy Customers'}
                    </div>
                  </div>
                  <div className="hidden sm:block w-px h-12 bg-gray-200"></div>
                  <div className="text-center">
                    <div className="text-3xl font-bold text-gray-900 mb-1">60s</div>
                    <div className="text-muted text-sm">
                      {language === 'sw' ? 'Muda wa Kuunda' : 'Average Setup'}
                    </div>
                  </div>
                </div>
              </div>

              {/* Customer Testimonials */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6 max-w-6xl mx-auto">
                <div className="card-modern p-6 hover-lift">
                  <div className="flex items-center space-x-1 mb-4">
                    {[...Array(5)].map((_, i) => (
                      <Star key={i} className="w-4 h-4 text-yellow-400 fill-current" />
                    ))}
                  </div>
                  <p className="text-gray-600 mb-4 text-sm leading-relaxed">
                    {language === 'sw'
                      ? '"PagesLab ilinisaidia kuunda tovuti ya salon yangu kwa dakika chache. Sasa nina wateja wengi zaidi kutoka WhatsApp!"'
                      : '"PagesLab helped me create my salon website in minutes. Now I get more customers through WhatsApp!"'
                    }
                  </p>
                  <div className="border-t border-gray-100 pt-4">
                    <div className="font-semibold text-gray-900 text-sm">Grace Wanjiku</div>
                    <div className="text-muted text-xs">
                      {language === 'sw' ? 'Mmiliki wa Salon, Westlands' : 'Salon Owner, Westlands'}
                    </div>
                  </div>
                </div>

                <div className="card-modern p-6 hover-lift">
                  <div className="flex items-center space-x-1 mb-4">
                    {[...Array(5)].map((_, i) => (
                      <Star key={i} className="w-4 h-4 text-yellow-400 fill-current" />
                    ))}
                  </div>
                  <p className="text-gray-600 mb-4 text-sm leading-relaxed">
                    {language === 'sw'
                      ? '"Tovuti yangu ya mgahawa ina M-Pesa na WhatsApp. Wateja wanaweza kuagiza chakula kwa urahisi!"'
                      : '"My restaurant website has M-Pesa and WhatsApp. Customers can easily order food!"'
                    }
                  </p>
                  <div className="border-t border-gray-100 pt-4">
                    <div className="font-semibold text-gray-900 text-sm">John Kamau</div>
                    <div className="text-muted text-xs">
                      {language === 'sw' ? 'Mmiliki wa Mgahawa, Kibera' : 'Restaurant Owner, Kibera'}
                    </div>
                  </div>
                </div>

                <div className="card-modern p-6 hover-lift">
                  <div className="flex items-center space-x-1 mb-4">
                    {[...Array(5)].map((_, i) => (
                      <Star key={i} className="w-4 h-4 text-yellow-400 fill-current" />
                    ))}
                  </div>
                  <p className="text-gray-600 mb-4 text-sm leading-relaxed">
                    {language === 'sw'
                      ? '"Kama daktari, ninahitaji tovuti rahisi. PagesLab ilinipa kila kitu nilichohitaji!"'
                      : '"As a doctor, I needed a simple website. PagesLab gave me everything I needed!"'
                    }
                  </p>
                  <div className="border-t border-gray-100 pt-4">
                    <div className="font-semibold text-gray-900 text-sm">Dr. Mary Njeri</div>
                    <div className="text-muted text-xs">
                      {language === 'sw' ? 'Daktari, Karen' : 'Medical Doctor, Karen'}
                    </div>
                  </div>
                </div>
              </div>

              {/* Industry Coverage */}
              <div className="mt-12 text-center">
                <p className="text-muted mb-6">
                  {language === 'sw'
                    ? 'Tunatumika na biashara za aina mbalimbali Kenya'
                    : 'Trusted by businesses across all industries in Kenya'
                  }
                </p>
                <div className="flex flex-wrap items-center justify-center gap-4 sm:gap-6 text-sm text-gray-500">
                  <div className="flex items-center space-x-2 py-2">
                    <Scissors className="w-4 h-4" />
                    <span>{language === 'sw' ? 'Saloni' : 'Salons'}</span>
                  </div>
                  <div className="flex items-center space-x-2 py-2">
                    <UtensilsCrossed className="w-4 h-4" />
                    <span>{language === 'sw' ? 'Migahawa' : 'Restaurants'}</span>
                  </div>
                  <div className="flex items-center space-x-2 py-2">
                    <Hospital className="w-4 h-4" />
                    <span>{language === 'sw' ? 'Kliniki' : 'Clinics'}</span>
                  </div>
                  <div className="flex items-center space-x-2 py-2">
                    <Users className="w-4 h-4" />
                    <span>{language === 'sw' ? 'Huduma' : 'Services'}</span>
                  </div>
                  <div className="flex items-center space-x-2 py-2">
                    <BarChart3 className="w-4 h-4" />
                    <span>{language === 'sw' ? 'Biashara' : 'Retail'}</span>
                  </div>
                </div>
              </div>
            </div>
          </>
        )}
      </div>

      {/* Business Input Section */}
        {currentStep === 'input' && (
          <div className="text-center">
            <div className="max-w-4xl mx-auto bg-white rounded-3xl shadow-lg p-8 border border-gray-100">
                <div className="mb-8">
                  <div className="flex items-center justify-center mb-6">
                    <div className="flex items-center space-x-3 text-2xl font-bold text-gray-800">
                      <MessageCircle className="w-8 h-8" />
                      <span>{language === 'sw' ? 'Elezea Biashara Yako' : 'Describe Your Business'}</span>
                    </div>
                  </div>
                  <div className="relative">
                    <textarea
                      id="business-input"
                      value={businessInput}
                      onChange={(e) => setBusinessInput(e.target.value)}
                      placeholder={language === 'sw'
                        ? 'Mfano: Nina salon ya kisasa Westlands inayotoa huduma za kutengeneza nywele, manicure, na urembo. Wasiliana nasi kwa 0712345678 kwa miadi.'
                        : 'Example: I run a modern salon in Westlands offering hair styling, manicures, and beauty treatments. Contact us on 0712345678 for appointments.'
                      }
                      className="w-full p-6 border-2 border-gray-200 rounded-2xl focus:ring-4 focus:ring-blue-100 focus:border-blue-500 resize-none text-lg transition-all duration-200 bg-gray-50 focus:bg-white min-h-[120px]"
                      rows={4}
                    />
                    <div className="absolute bottom-4 right-4 flex items-center space-x-2">
                      <button
                        onClick={startVoiceInput}
                        disabled={isListening}
                        className={`p-2 transition-colors rounded-lg hover:bg-blue-50 ${
                          isListening
                            ? 'text-red-500 animate-pulse'
                            : 'text-gray-400 hover:text-blue-500'
                        }`}
                        title={language === 'sw' ? 'Ongea kuhusu biashara yako' : 'Speak about your business'}
                      >
                        <Mic className="w-5 h-5" />
                      </button>
                      <label className="p-2 text-gray-400 hover:text-blue-500 transition-colors rounded-lg hover:bg-blue-50 cursor-pointer">
                        <Camera className="w-5 h-5" />
                        <input
                          type="file"
                          accept="image/*"
                          onChange={handlePhotoUpload}
                          className="hidden"
                          disabled={isUploading}
                        />
                      </label>
                      {(isListening || isUploading) && (
                        <div className="absolute -top-12 right-0 bg-black text-white px-3 py-1 rounded-lg text-sm">
                          {isListening
                            ? (language === 'sw' ? 'Sikiliza...' : 'Listening...')
                            : (language === 'sw' ? 'Inapakia...' : 'Uploading...')
                          }
                        </div>
                      )}
                    </div>
                  </div>
                  <div className="mt-4 text-sm text-gray-500 text-center">
                    <span className="font-medium flex items-center justify-center space-x-1">
                      <Lightbulb className="w-4 h-4" />
                      <span>{language === 'sw' ? 'Kidokezo:' : 'Tip:'}</span>
                    </span>
                    <span className="block mt-1">
                      {language === 'sw'
                        ? 'Jumuisha aina ya biashara, mahali, mawasiliano, na huduma kwa matokeo bora'
                        : 'Include your business type, location, contact info, and services for best results'
                      }
                    </span>
                  </div>
                </div>

                {error && (
                  <div className="mb-6 p-4 bg-red-50 border border-red-200 rounded-xl">
                    <div className="flex items-center">
                      <span className="text-red-500 mr-2 text-lg">⚠️</span>
                      <span className="text-red-700 font-medium">{error}</span>
                    </div>
                  </div>
                )}

                {/* Image Preference Option */}
                <div className="flex items-center space-x-3 p-4 bg-gray-50 rounded-xl border border-gray-200">
                  <input
                    type="checkbox"
                    id="include-images"
                    checked={includeImages}
                    onChange={(e) => setIncludeImages(e.target.checked)}
                    className="w-5 h-5 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 focus:ring-2"
                  />
                  <label htmlFor="include-images" className="text-sm font-medium text-gray-700 cursor-pointer">
                    {language === 'sw'
                      ? 'Jumuisha picha za biashara kwenye tovuti'
                      : 'Include business images in website'
                    }
                  </label>
                  <div className="text-xs text-gray-500">
                    {language === 'sw'
                      ? '(Tovuti itakuwa na picha za kuvutia zaidi)'
                      : '(Website will be more visually appealing)'
                    }
                  </div>
                </div>

                {/* Modern Integration Selection */}
                <div className="card-elevated overflow-hidden animate-fade-in-up delay-500">
                  <div className="p-6 border-b border-gray-100">
                    <h3 className="heading-secondary mb-2">
                      {language === 'sw' ? 'Chagua Viunganishi vya Tovuti' : 'Choose Website Features'}
                    </h3>
                    <p className="text-body">
                      {language === 'sw' ? 'Ongeza viunganishi vya biashara za Kenya' : 'Add essential features for Kenyan businesses'}
                    </p>
                  </div>

                  <div className="p-6">
                    {/* Essential Integrations */}
                    <div className="mb-8">
                      <div className="flex items-center space-x-2 mb-4">
                        <div className="w-2 h-2 bg-emerald-500 rounded-full"></div>
                        <h4 className="font-semibold text-gray-900">
                          {language === 'sw' ? 'Muhimu kwa Biashara za Kenya' : 'Essential for Kenyan Businesses'}
                        </h4>
                        <span className="bg-emerald-100 text-emerald-700 text-xs font-medium px-2 py-1 rounded-full">
                          {language === 'sw' ? 'Inapendekezwa' : 'Recommended'}
                        </span>
                      </div>

                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        {/* WhatsApp Integration */}
                        <div className={`group relative p-4 rounded-xl border-2 cursor-pointer transition-all duration-200 ${
                          selectedIntegrations.whatsapp
                            ? 'border-emerald-500 bg-emerald-50 shadow-sm'
                            : 'border-gray-200 bg-white hover:border-emerald-300 hover:shadow-sm'
                        }`}
                        onClick={() => setSelectedIntegrations(prev => ({...prev, whatsapp: !prev.whatsapp}))}>
                          <div className="flex items-start space-x-3">
                            <div className="w-10 h-10 bg-emerald-100 rounded-lg flex items-center justify-center flex-shrink-0">
                              <MessageCircle className="w-5 h-5 text-emerald-600" />
                            </div>
                            <div className="flex-1 min-w-0">
                              <h5 className="font-semibold text-gray-900 mb-1">WhatsApp Business</h5>
                              <p className="text-sm text-gray-600 mb-2">
                                {language === 'sw' ? 'Mawasiliano ya moja kwa moja na wateja' : 'Direct customer communication'}
                              </p>
                            </div>
                            <div className={`w-5 h-5 rounded-full border-2 flex items-center justify-center transition-colors ${
                              selectedIntegrations.whatsapp
                                ? 'border-emerald-500 bg-emerald-500'
                                : 'border-gray-300 group-hover:border-emerald-400'
                            }`}>
                              {selectedIntegrations.whatsapp && <CheckCircle className="w-3 h-3 text-white" />}
                            </div>
                          </div>

                          {selectedIntegrations.whatsapp && (
                            <div className="mt-4 p-3 bg-white rounded-lg border border-emerald-200" onClick={(e) => e.stopPropagation()}>
                              <label className="block text-sm font-medium text-gray-700 mb-2">
                                {language === 'sw' ? 'Nambari ya WhatsApp' : 'WhatsApp Number'}
                              </label>
                              <input
                                type="tel"
                                value={whatsappNumber}
                                onChange={(e) => setWhatsappNumber(e.target.value)}
                                placeholder="+254712345678"
                                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500 text-sm"
                              />
                            </div>
                          )}
                        </div>

                        {/* Phone Integration */}
                        <div className={`group relative p-4 rounded-xl border-2 cursor-pointer transition-all duration-200 ${
                          selectedIntegrations.phone
                            ? 'border-blue-500 bg-blue-50 shadow-sm'
                            : 'border-gray-200 bg-white hover:border-blue-300 hover:shadow-sm'
                        }`}
                        onClick={() => setSelectedIntegrations(prev => ({...prev, phone: !prev.phone}))}>
                          <div className="flex items-start space-x-3">
                            <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center flex-shrink-0">
                              <Phone className="w-5 h-5 text-blue-600" />
                            </div>
                            <div className="flex-1 min-w-0">
                              <h5 className="font-semibold text-gray-900 mb-1">
                                {language === 'sw' ? 'Simu ya Biashara' : 'Business Phone'}
                              </h5>
                              <p className="text-sm text-gray-600 mb-2">
                                {language === 'sw' ? 'Bonyeza kupiga simu' : 'Click-to-call functionality'}
                              </p>
                            </div>
                            <div className={`w-5 h-5 rounded-full border-2 flex items-center justify-center transition-colors ${
                              selectedIntegrations.phone
                                ? 'border-blue-500 bg-blue-500'
                                : 'border-gray-300 group-hover:border-blue-400'
                            }`}>
                              {selectedIntegrations.phone && <CheckCircle className="w-3 h-3 text-white" />}
                            </div>
                          </div>

                          {selectedIntegrations.phone && (
                            <div className="mt-4 p-3 bg-white rounded-lg border border-blue-200" onClick={(e) => e.stopPropagation()}>
                              <label className="block text-sm font-medium text-gray-700 mb-2">
                                {language === 'sw' ? 'Nambari ya Simu' : 'Phone Number'}
                              </label>
                              <input
                                type="tel"
                                value={phoneNumber}
                                onChange={(e) => setPhoneNumber(e.target.value)}
                                placeholder="+254712345678"
                                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-sm"
                              />
                            </div>
                          )}
                        </div>
                      </div>
                    </div>

                    {/* Additional Features */}
                    <div>
                      <div className="flex items-center space-x-2 mb-4">
                        <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                        <h4 className="font-semibold text-gray-900">
                          {language === 'sw' ? 'Vipengele vya Ziada' : 'Additional Features'}
                        </h4>
                      </div>

                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        {/* Google Maps */}
                        <div className={`group relative p-4 rounded-xl border-2 cursor-pointer transition-all duration-200 ${
                          selectedIntegrations.googleMaps
                            ? 'border-red-500 bg-red-50 shadow-sm'
                            : 'border-gray-200 bg-white hover:border-red-300 hover:shadow-sm'
                        }`}
                        onClick={() => setSelectedIntegrations(prev => ({...prev, googleMaps: !prev.googleMaps}))}>
                          <div className="flex items-start space-x-3">
                            <div className="w-10 h-10 bg-red-100 rounded-lg flex items-center justify-center flex-shrink-0">
                              <MapPin className="w-5 h-5 text-red-600" />
                            </div>
                            <div className="flex-1 min-w-0">
                              <h5 className="font-semibold text-gray-900 mb-1">Google Maps</h5>
                              <p className="text-sm text-gray-600 mb-2">
                                {language === 'sw' ? 'Onyesha mahali pa biashara yako' : 'Show your business location'}
                              </p>
                            </div>
                            <div className={`w-5 h-5 rounded-full border-2 flex items-center justify-center transition-colors ${
                              selectedIntegrations.googleMaps
                                ? 'border-red-500 bg-red-500'
                                : 'border-gray-300 group-hover:border-red-400'
                            }`}>
                              {selectedIntegrations.googleMaps && <CheckCircle className="w-3 h-3 text-white" />}
                            </div>
                          </div>

                          {selectedIntegrations.googleMaps && (
                            <div className="mt-4 p-3 bg-white rounded-lg border border-red-200" onClick={(e) => e.stopPropagation()}>
                              <label className="block text-sm font-medium text-gray-700 mb-2">
                                {language === 'sw' ? 'Kiungo cha Google Maps' : 'Google Maps Link'}
                              </label>
                              <input
                                type="url"
                                value={googleMapsUrl}
                                onChange={(e) => setGoogleMapsUrl(e.target.value)}
                                placeholder="https://maps.app.goo.gl/..."
                                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-red-500 text-sm"
                              />
                            </div>
                          )}
                        </div>

                        {/* M-Pesa */}
                        <div className={`group relative p-4 rounded-xl border-2 cursor-pointer transition-all duration-200 ${
                          selectedIntegrations.mpesa
                            ? 'border-emerald-500 bg-emerald-50 shadow-sm'
                            : 'border-gray-200 bg-white hover:border-emerald-300 hover:shadow-sm'
                        }`}
                        onClick={() => setSelectedIntegrations(prev => ({...prev, mpesa: !prev.mpesa}))}>
                          <div className="flex items-start space-x-3">
                            <div className="w-10 h-10 bg-emerald-100 rounded-lg flex items-center justify-center flex-shrink-0">
                              <DollarSign className="w-5 h-5 text-emerald-600" />
                            </div>
                            <div className="flex-1 min-w-0">
                              <h5 className="font-semibold text-gray-900 mb-1">M-Pesa</h5>
                              <p className="text-sm text-gray-600 mb-2">
                                {language === 'sw' ? 'Pokea malipo ya M-Pesa' : 'Accept M-Pesa payments'}
                              </p>
                            </div>
                            <div className={`w-5 h-5 rounded-full border-2 flex items-center justify-center transition-colors ${
                              selectedIntegrations.mpesa
                                ? 'border-emerald-500 bg-emerald-500'
                                : 'border-gray-300 group-hover:border-emerald-400'
                            }`}>
                              {selectedIntegrations.mpesa && <CheckCircle className="w-3 h-3 text-white" />}
                            </div>
                          </div>

                          {selectedIntegrations.mpesa && (
                            <div className="mt-4 p-3 bg-white rounded-lg border border-emerald-200" onClick={(e) => e.stopPropagation()}>
                              <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
                                <div>
                                  <label className="block text-sm font-medium text-gray-700 mb-1">
                                    {language === 'sw' ? 'Nambari ya M-Pesa' : 'M-Pesa Number'}
                                  </label>
                                  <input
                                    type="tel"
                                    value={mpesaNumber}
                                    onChange={(e) => setMpesaNumber(e.target.value)}
                                    placeholder="+254712345678"
                                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500 text-sm"
                                  />
                                </div>
                                <div>
                                  <label className="block text-sm font-medium text-gray-700 mb-1">
                                    {language === 'sw' ? 'Nambari ya Airtel Money' : 'Airtel Money Number'}
                                  </label>
                                  <input
                                    type="tel"
                                    value={mpesaData.airtelMoneyNumber}
                                    onChange={(e) => setMpesaData(prev => ({...prev, airtelMoneyNumber: e.target.value}))}
                                    placeholder="+254712345678"
                                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500 text-sm"
                                  />
                                </div>
                                <div>
                                  <label className="block text-sm font-medium text-gray-700 mb-1">
                                    {language === 'sw' ? 'Nambari ya Till' : 'Till Number'}
                                  </label>
                                  <input
                                    type="text"
                                    value={mpesaData.tillNumber}
                                    onChange={(e) => setMpesaData(prev => ({...prev, tillNumber: e.target.value}))}
                                    placeholder="123456"
                                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500 text-sm"
                                  />
                                </div>
                                <div>
                                  <label className="block text-sm font-medium text-gray-700 mb-1">
                                    {language === 'sw' ? 'Jina la Biashara' : 'Business Name'}
                                  </label>
                                  <input
                                    type="text"
                                    value={mpesaData.businessName}
                                    onChange={(e) => setMpesaData(prev => ({...prev, businessName: e.target.value}))}
                                    placeholder="Your Business Name"
                                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500 text-sm"
                                  />
                                </div>
                              </div>
                            </div>
                          )}
                        </div>

                        {/* Social Media */}
                        <div className={`group relative p-4 rounded-xl border-2 cursor-pointer transition-all duration-200 ${
                          selectedIntegrations.socialMedia
                            ? 'border-purple-500 bg-purple-50 shadow-sm'
                            : 'border-gray-200 bg-white hover:border-purple-300 hover:shadow-sm'
                        }`}
                        onClick={() => setSelectedIntegrations(prev => ({...prev, socialMedia: !prev.socialMedia}))}>
                          <div className="flex items-start space-x-3">
                            <div className="w-10 h-10 bg-purple-100 rounded-lg flex items-center justify-center flex-shrink-0">
                              <Users className="w-5 h-5 text-purple-600" />
                            </div>
                            <div className="flex-1 min-w-0">
                              <h5 className="font-semibold text-gray-900 mb-1">
                                {language === 'sw' ? 'Mitandao ya Kijamii' : 'Social Media'}
                              </h5>
                              <p className="text-sm text-gray-600 mb-2">
                                {language === 'sw' ? 'Viungo vya Facebook, Instagram, na mengine' : 'Facebook, Instagram, and other social links'}
                              </p>
                            </div>
                            <div className={`w-5 h-5 rounded-full border-2 flex items-center justify-center transition-colors ${
                              selectedIntegrations.socialMedia
                                ? 'border-purple-500 bg-purple-500'
                                : 'border-gray-300 group-hover:border-purple-400'
                            }`}>
                              {selectedIntegrations.socialMedia && <CheckCircle className="w-3 h-3 text-white" />}
                            </div>
                          </div>

                          {selectedIntegrations.socialMedia && (
                            <div className="mt-4 p-3 bg-white rounded-lg border border-purple-200" onClick={(e) => e.stopPropagation()}>
                              <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
                                <div>
                                  <label className="block text-sm font-medium text-gray-700 mb-1">Facebook</label>
                                  <input
                                    type="url"
                                    value={socialMediaLinks.facebook}
                                    onChange={(e) => setSocialMediaLinks(prev => ({...prev, facebook: e.target.value}))}
                                    placeholder="https://facebook.com/yourbusiness"
                                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500 text-sm"
                                  />
                                </div>
                                <div>
                                  <label className="block text-sm font-medium text-gray-700 mb-1">Instagram</label>
                                  <input
                                    type="url"
                                    value={socialMediaLinks.instagram}
                                    onChange={(e) => setSocialMediaLinks(prev => ({...prev, instagram: e.target.value}))}
                                    placeholder="https://instagram.com/yourbusiness"
                                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500 text-sm"
                                  />
                                </div>
                                <div>
                                  <label className="block text-sm font-medium text-gray-700 mb-1">Twitter</label>
                                  <input
                                    type="url"
                                    value={socialMediaLinks.twitter}
                                    onChange={(e) => setSocialMediaLinks(prev => ({...prev, twitter: e.target.value}))}
                                    placeholder="https://twitter.com/yourbusiness"
                                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500 text-sm"
                                  />
                                </div>
                                <div>
                                  <label className="block text-sm font-medium text-gray-700 mb-1">TikTok</label>
                                  <input
                                    type="url"
                                    value={socialMediaLinks.tiktok}
                                    onChange={(e) => setSocialMediaLinks(prev => ({...prev, tiktok: e.target.value}))}
                                    placeholder="https://tiktok.com/@yourbusiness"
                                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500 text-sm"
                                  />
                                </div>
                              </div>
                            </div>
                          )}
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Generate Button */}
                <div className="mt-8 text-center animate-fade-in-up delay-500">
                  <button
                    onClick={handleGenerateWebsite}
                    disabled={!businessInput.trim() || isGenerating}
                    className={`w-full py-4 px-8 text-lg focus-ring ${
                      !businessInput.trim() || isGenerating
                        ? 'bg-gray-300 text-gray-500 cursor-not-allowed rounded-xl'
                        : 'btn-primary'
                    }`}
                  >
                    {isGenerating ? (
                      <div className="flex items-center justify-center space-x-2">
                        <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white"></div>
                        <span>{language === 'sw' ? 'Inaunda...' : 'Creating...'}</span>
                      </div>
                    ) : (
                      <div className="flex items-center justify-center space-x-2">
                        <Rocket className="w-5 h-5" />
                        <span>{language === 'sw' ? 'Unda Tovuti Yangu' : 'Create My Website'}</span>
                      </div>
                    )}
                  </button>

                  <p className="text-muted mt-3">
                    {language === 'sw'
                      ? 'Tovuti yako itakuwa tayari kwa sekunde chache'
                      : 'Your website will be ready in seconds'
                    }
                  </p>
                </div>
            </div>
          </div>
        )}

        {/* Enhanced Generation Progress */}
        {currentStep === 'generating' && (
            <div className="mt-16 max-w-7xl mx-auto animate-fade-in-up">
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                {/* Progress Panel */}
                <div className="bg-white rounded-3xl shadow-2xl p-8">
                  <div className="text-center mb-8">
                    <div className="inline-flex items-center justify-center w-20 h-20 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full mb-6 animate-pulse">
                      <Sparkles className="w-10 h-10 text-white" />
                    </div>
                    <h3 className="text-3xl font-bold text-gray-900 mb-4">
                      {language === 'sw' ? 'Kutengeneza Tovuti Yako Nzuri' : 'Creating Your Beautiful Website'}
                    </h3>
                    <p className="text-lg text-gray-600 mb-6">
                      {language === 'sw'
                        ? 'AI yetu inaunda tovuti ya kibiashara ya kisasa kwa ajili yako...'
                        : 'Our AI is crafting a stunning, professional website just for you...'
                      }
                    </p>
                  </div>

                  {/* Enhanced Progress Bar */}
                  <div className="mb-8">
                    <div className="flex justify-between items-center mb-2">
                      <span className="text-sm font-medium text-gray-700">
                        {currentGenerationStep || (language === 'sw' ? 'Kuanza...' : 'Starting...')}
                      </span>
                      <span className="text-sm font-bold text-blue-600">{Math.round(progress)}%</span>
                    </div>
                    <div className="bg-gray-200 rounded-full h-3 mb-4 overflow-hidden">
                      <div
                        className="bg-gradient-to-r from-blue-500 via-purple-500 to-green-500 h-3 rounded-full transition-all duration-1000 ease-out relative"
                        style={{ width: `${progress}%` }}
                      >
                        <div className="absolute inset-0 bg-white/30 animate-pulse"></div>
                      </div>
                    </div>
                  </div>

                  {/* Generation Logs */}
                  <div className="mb-8">
                    <h4 className="text-lg font-semibold text-gray-800 mb-4">
                      {language === 'sw' ? 'Maelezo ya Mchakato' : 'Generation Process'}
                    </h4>
                    <div className="bg-gray-50 rounded-xl p-4 max-h-40 overflow-y-auto">
                      {generationLogs.map((log, index) => (
                        <div key={index} className="flex items-center space-x-2 mb-2 animate-fade-in">
                          <CheckCircle className="w-4 h-4 text-green-500 flex-shrink-0" />
                          <span className="text-sm text-gray-700">{log}</span>
                        </div>
                      ))}
                      {generationLogs.length === 0 && (
                        <div className="flex items-center space-x-2 animate-pulse">
                          <div className="w-4 h-4 bg-gray-300 rounded-full"></div>
                          <span className="text-sm text-gray-500">
                            {language === 'sw' ? 'Kuandaa...' : 'Preparing...'}
                          </span>
                        </div>
                      )}
                    </div>
                  </div>
                </div>

                {/* Live Preview Panel */}
                <div className="bg-white rounded-3xl shadow-2xl p-8">
                  <div className="mb-6">
                    <h4 className="text-2xl font-bold text-gray-900 mb-2">
                      {language === 'sw' ? 'Muhtasari wa Tovuti' : 'Live Preview'}
                    </h4>
                    <p className="text-gray-600">
                      {language === 'sw'
                        ? 'Ona tovuti yako inavyotengenezwa kwa wakati halisi'
                        : 'Watch your website being created in real-time'
                      }
                    </p>
                  </div>

                  <div className="border-2 border-gray-200 rounded-2xl overflow-hidden bg-gray-50">
                    <div className="bg-gray-100 px-4 py-3 flex items-center space-x-2 border-b">
                      <div className="flex space-x-2">
                        <div className="w-3 h-3 bg-red-400 rounded-full"></div>
                        <div className="w-3 h-3 bg-yellow-400 rounded-full"></div>
                        <div className="w-3 h-3 bg-green-400 rounded-full"></div>
                      </div>
                      <div className="flex-1 bg-white rounded px-3 py-1 text-sm text-gray-500">
                        {language === 'sw' ? 'tovuti-yako.pageslab.co.ke' : 'your-website.pageslab.co.ke'}
                      </div>
                    </div>

                    <div className="h-96 flex items-center justify-center">
                      {progress < 20 ? (
                        <div className="text-center animate-pulse">
                          <div className="w-16 h-16 bg-gray-300 rounded-lg mb-4 mx-auto"></div>
                          <div className="w-32 h-4 bg-gray-300 rounded mb-2 mx-auto"></div>
                          <div className="w-24 h-3 bg-gray-300 rounded mx-auto"></div>
                        </div>
                      ) : progress < 50 ? (
                        <div className="text-center animate-fade-in">
                          <div className="w-16 h-16 bg-blue-200 rounded-lg mb-4 mx-auto flex items-center justify-center">
                            <Globe className="w-8 h-8 text-blue-600" />
                          </div>
                          <div className="text-lg font-semibold text-gray-800 mb-2">
                            {language === 'sw' ? 'Kuunda Muhtasari...' : 'Building Structure...'}
                          </div>
                          <div className="text-sm text-gray-600">
                            {language === 'sw' ? 'Kuongeza vipengele vya msingi' : 'Adding basic elements'}
                          </div>
                        </div>
                      ) : progress < 80 ? (
                        <div className="text-center animate-fade-in">
                          <div className="w-16 h-16 bg-purple-200 rounded-lg mb-4 mx-auto flex items-center justify-center">
                            <Palette className="w-8 h-8 text-purple-600" />
                          </div>
                          <div className="text-lg font-semibold text-gray-800 mb-2">
                            {language === 'sw' ? 'Kuongeza Rangi na Muundo...' : 'Adding Colors & Design...'}
                          </div>
                          <div className="text-sm text-gray-600">
                            {language === 'sw' ? 'Kutengeneza muundo wa kipekee' : 'Creating unique styling'}
                          </div>
                        </div>
                      ) : (
                        <div className="text-center animate-fade-in">
                          <div className="w-16 h-16 bg-green-200 rounded-lg mb-4 mx-auto flex items-center justify-center">
                            <CheckCircle className="w-8 h-8 text-green-600" />
                          </div>
                          <div className="text-lg font-semibold text-gray-800 mb-2">
                            {language === 'sw' ? 'Kumaliza...' : 'Finalizing...'}
                          </div>
                          <div className="text-sm text-gray-600">
                            {language === 'sw' ? 'Kuongeza vipengele vya mwisho' : 'Adding final touches'}
                          </div>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              </div>

              {/* Estimated Time */}
              <div className="mt-8 text-center">
                <div className="text-sm text-gray-500">
                  {language === 'sw'
                    ? 'Hii kwa kawaida inachukua sekunde 20-30. Subiri tukitengeneza kitu cha ajabu!'
                    : 'This usually takes 20-30 seconds. Please wait while we create something amazing!'
                  }
                </div>
              </div>
            </div>
          )}

          {/* Website Results */}
          {currentStep === 'complete' && generatedWebsite && (
            <div className="mt-16 max-w-7xl mx-auto animate-fade-in-up">
              <div className="text-center mb-12">
                <div className="inline-flex items-center justify-center w-24 h-24 bg-gradient-to-r from-green-500 to-blue-500 rounded-full mb-6">
                  <PartyPopper className="w-12 h-12 text-white" />
                </div>
                <h3 className="text-5xl font-bold text-gray-900 mb-4">
                  Your Website is Ready!
                </h3>
                <p className="text-xl text-gray-600 mb-8">
                  Beautiful, professional, and optimized for Kenyan businesses
                </p>

                {/* Action Buttons */}
                <div className="flex flex-wrap justify-center gap-4 mb-12">

                  <button
                    onClick={() => downloadFile(generatedWebsite.website.html, `${generatedWebsite.businessProfile.name.replace(/\s+/g, '-').toLowerCase()}.html`, 'text/html')}
                    className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white font-bold py-4 px-8 rounded-xl transform hover:scale-105 transition-all duration-200 shadow-lg"
                  >
                    <span className="flex items-center">
                      <Download className="w-5 h-5 mr-2" />
                      {language === 'sw' ? 'Pakua HTML' : 'Download HTML'}
                    </span>
                  </button>

                  <button
                    onClick={() => downloadFile(generatedWebsite.website.css, `${generatedWebsite.businessProfile.name.replace(/\s+/g, '-').toLowerCase()}.css`, 'text/css')}
                    className="bg-gradient-to-r from-green-600 to-teal-600 hover:from-green-700 hover:to-teal-700 text-white font-bold py-4 px-8 rounded-xl transform hover:scale-105 transition-all duration-200 shadow-lg"
                  >
                    <span className="flex items-center">
                      <Palette className="w-5 h-5 mr-2" />
                      {language === 'sw' ? 'Pakua CSS' : 'Download CSS'}
                    </span>
                  </button>

                  <button
                    onClick={handleStartOver}
                    className="bg-gradient-to-r from-gray-600 to-gray-700 hover:from-gray-700 hover:to-gray-800 text-white font-bold py-4 px-8 rounded-xl transform hover:scale-105 transition-all duration-200 shadow-lg"
                  >
                    <span className="flex items-center">
                      <RotateCcw className="w-5 h-5 mr-2" />
                      {language === 'sw' ? 'Tengeneza Nyingine' : 'Create Another'}
                    </span>
                  </button>
                </div>
              </div>

              {/* Website Preview */}
              <div className="bg-white rounded-3xl shadow-2xl p-8">
                <div className="mb-6">
                  <h4 className="text-2xl font-bold text-gray-900 mb-2">Website Preview</h4>
                  <p className="text-gray-600">Your beautiful, mobile-ready website with Kenyan business integrations</p>
                </div>

                <div className="border-2 border-gray-200 rounded-2xl overflow-hidden">
                  <div className="bg-gray-100 px-4 py-3 flex items-center space-x-2">
                    <div className="w-3 h-3 bg-red-500 rounded-full"></div>
                    <div className="w-3 h-3 bg-yellow-500 rounded-full"></div>
                    <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                    <div className="ml-4 text-sm text-gray-600 font-mono">
                      {generatedWebsite.businessProfile.name.replace(/\s+/g, '-').toLowerCase()}.com
                    </div>
                  </div>

                  <div className="bg-white border rounded-lg overflow-hidden">
                    <div className="flex items-center justify-between p-3 bg-gray-50 border-b">
                      <div className="flex items-center space-x-2">
                        <div className="flex space-x-1">
                          <div className="w-3 h-3 bg-red-500 rounded-full"></div>
                          <div className="w-3 h-3 bg-yellow-500 rounded-full"></div>
                          <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                        </div>
                        <span className="text-sm text-gray-600 font-mono">
                          {(generatedWebsite?.businessProfile?.name || 'website').toLowerCase().replace(/\s+/g, '-')}.pageslab.co.ke
                        </span>
                      </div>
                      <div className="flex items-center space-x-3">
                        <button
                          onClick={() => {
                            if (isAuthenticated) {
                              setShowEditingDashboard(true)
                            } else {
                              setShowAuthModal(true)
                            }
                          }}
                          className="px-4 py-2 bg-gradient-to-r from-purple-600 to-blue-600 text-white text-sm rounded-lg hover:from-purple-700 hover:to-blue-700 flex items-center space-x-2 font-medium shadow-md transform hover:scale-105 transition-all duration-200"
                        >
                          <Edit3 className="w-4 h-4" />
                          <span>{language === 'sw' ? 'Hariri Sasa' : 'Edit Now'}</span>
                        </button>

                        <button
                          onClick={() => {
                            console.log('🔍 PREVIEW DEBUG - generatedWebsite structure:', generatedWebsite);
                            console.log('🔍 PREVIEW DEBUG - website property:', generatedWebsite?.website);
                            console.log('🔍 PREVIEW DEBUG - html length:', generatedWebsite?.website?.html?.length);

                            const newWindow = window.open('', '_blank');
                            if (newWindow && generatedWebsite?.website?.html) {
                              // Pure AI generates complete HTML with embedded CSS
                              const htmlContent = generatedWebsite.website.html;
                              console.log('🔍 PREVIEW DEBUG - Writing HTML to new window, length:', htmlContent.length);
                              newWindow.document.write(htmlContent);
                              newWindow.document.close();
                            } else {
                              console.error('❌ PREVIEW ERROR - Missing website data:', {
                                hasWebsite: !!generatedWebsite?.website,
                                hasHtml: !!generatedWebsite?.website?.html
                              });
                              alert('Unable to open preview - website data is missing');
                            }
                          }}
                          className="text-sm text-blue-600 hover:text-blue-800 flex items-center space-x-1"
                        >
                          <ExternalLink className="w-4 h-4" />
                          <span>Open in New Tab</span>
                        </button>
                      </div>
                    </div>
                    <div className="relative">
                      <SecureIframeRenderer
                        html={generatedWebsite?.website?.html || ''}
                        css={''} // Pure AI generates complete HTML with embedded CSS
                        title={`${generatedWebsite?.businessProfile?.name || 'Website'} Preview`}
                        className="w-full rounded-lg border border-gray-200"
                        onLoad={() => {
                          console.log('✅ Secure iframe loaded successfully')
                          console.log('📄 HTML content length:', generatedWebsite?.website?.html?.length)
                          console.log('📄 HTML preview:', generatedWebsite?.website?.html?.substring(0, 200))
                        }}
                        onError={(error) => {
                          console.error('❌ Secure iframe error:', error)
                          console.log('🔍 HTML content for debugging:', generatedWebsite?.website?.html)
                        }}
                      />

                      {/* Performance indicator */}
                      <div className="absolute top-2 left-2 bg-green-100 text-green-800 text-xs px-2 py-1 rounded-full opacity-75">
                        ⚡ Optimized Preview
                      </div>
                    </div>
                  </div>
                </div>





                {/* Features Highlight */}
                <div className="mt-8 grid grid-cols-1 md:grid-cols-3 gap-6">
                  <div className="bg-gradient-to-br from-blue-50 to-blue-100 p-6 rounded-xl">
                    <div className="flex justify-center mb-3">
                      <Smartphone className="w-8 h-8 text-blue-600" />
                    </div>
                    <h5 className="font-bold text-blue-900 mb-2">Mobile-First Design</h5>
                    <p className="text-blue-700 text-sm">Optimized for smartphones and tablets, perfect for Kenyan users</p>
                  </div>

                  <div className="bg-gradient-to-br from-green-50 to-green-100 p-6 rounded-xl">
                    <div className="flex justify-center mb-3">
                      <DollarSign className="w-8 h-8 text-green-600" />
                    </div>
                    <h5 className="font-bold text-green-900 mb-2">M-Pesa Integration</h5>
                    <p className="text-green-700 text-sm">Built-in payment methods including M-Pesa and mobile money</p>
                  </div>

                  <div className="bg-gradient-to-br from-purple-50 to-purple-100 p-6 rounded-xl">
                    <div className="flex justify-center mb-3">
                      <MessageCircle className="w-8 h-8 text-purple-600" />
                    </div>
                    <h5 className="font-bold text-purple-900 mb-2">WhatsApp Ready</h5>
                    <p className="text-purple-700 text-sm">Direct WhatsApp integration for instant customer communication</p>
                  </div>
                </div>
              </div>
            </div>
          )}

      {/* Image Manager Modal */}
      {showImageManager && generatedWebsite?.website?.html && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-xl max-w-6xl w-full max-h-[90vh] overflow-hidden">
            <div className="p-6 border-b border-gray-200 flex items-center justify-between">
              <h2 className="text-xl font-semibold text-gray-900">
                {language === 'sw' ? 'Msimamizi wa Picha' : 'Image Manager'}
              </h2>
              <button
                onClick={() => setShowImageManager(false)}
                className="text-gray-400 hover:text-gray-600 p-2"
              >
                <X className="w-6 h-6" />
              </button>
            </div>
            <div className="overflow-y-auto max-h-[calc(90vh-80px)]">
              <ImageManager
                websiteHtml={generatedWebsite.website.html}
                onImageReplace={handleImageReplace}
                onImageRemove={handleImageRemove}
                language={language}
              />
            </div>
          </div>
        </div>
      )}

      {/* Editing Dashboard */}
      {showEditingDashboard && generatedWebsite && (
        <div className="fixed inset-0 bg-white z-50">
          <EditingDashboard
            websiteData={generatedWebsite}
            onSave={(updatedData) => {
              setGeneratedWebsite(updatedData)
              // Save original if not already saved
              if (!originalWebsite) {
                setOriginalWebsite(generatedWebsite)
              }
            }}
            onClose={() => setShowEditingDashboard(false)}
            language={language}
          />
        </div>
      )}

      {/* Authentication Modal */}
      <AuthModal
        isOpen={showAuthModal}
        onClose={closeAuthModal}
        language={language}
      />
    </main>
  )
}
