@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  html {
    scroll-behavior: smooth;
  }
  
  body {
    @apply bg-white text-gray-900 antialiased;
  }
}

@layer components {
  /* Modern Button System */
  .btn-primary {
    @apply bg-gradient-to-r from-blue-600 to-emerald-600 hover:from-blue-700 hover:to-emerald-700 text-white font-semibold py-3 px-6 rounded-xl transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5;
  }

  .btn-secondary {
    @apply bg-white hover:bg-gray-50 text-gray-900 font-semibold py-3 px-6 rounded-xl border border-gray-200 hover:border-gray-300 transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 shadow-sm hover:shadow-md;
  }

  .btn-ghost {
    @apply bg-transparent hover:bg-gray-100 text-gray-700 hover:text-gray-900 font-medium py-2 px-4 rounded-lg transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2;
  }

  /* Modern Card System */
  .card-modern {
    @apply bg-gradient-to-br from-blue-50 to-white rounded-2xl shadow-sm border border-blue-100 hover:shadow-md transition-all duration-200 hover:border-blue-200;
  }

  .card-elevated {
    @apply bg-gradient-to-br from-emerald-50 to-white rounded-2xl shadow-lg border border-emerald-100 hover:shadow-xl transition-all duration-300 hover:border-emerald-200 hover:-translate-y-1;
  }

  .card-interactive {
    @apply bg-gradient-to-br from-gray-50 to-white rounded-xl border-2 border-gray-200 hover:border-blue-300 cursor-pointer transition-all duration-200 hover:shadow-sm group;
  }

  .card-purple {
    @apply bg-gradient-to-br from-purple-50 to-white rounded-2xl shadow-sm border border-purple-100 hover:shadow-md transition-all duration-200 hover:border-purple-200;
  }

  .card-orange {
    @apply bg-gradient-to-br from-orange-50 to-white rounded-2xl shadow-sm border border-orange-100 hover:shadow-md transition-all duration-200 hover:border-orange-200;
  }

  .card-pink {
    @apply bg-gradient-to-br from-pink-50 to-white rounded-2xl shadow-sm border border-pink-100 hover:shadow-md transition-all duration-200 hover:border-pink-200;
  }

  /* Glass Effect */
  .glass-effect {
    @apply bg-white/95 backdrop-blur-md border border-white/30 shadow-lg;
  }

  /* Integration Cards */
  .integration-card {
    @apply p-4 rounded-xl border-2 cursor-pointer transition-all duration-200;
  }

  .integration-card-active {
    @apply border-emerald-500 bg-emerald-50 shadow-sm;
  }

  .integration-card-inactive {
    @apply border-gray-200 bg-white hover:border-emerald-300 hover:shadow-sm;
  }

  /* Typography Enhancements */
  .heading-primary {
    @apply text-4xl lg:text-5xl xl:text-6xl font-bold text-gray-900 leading-tight;
  }

  .heading-secondary {
    @apply text-2xl lg:text-3xl font-semibold text-gray-900 leading-tight;
  }

  .text-body {
    @apply text-lg text-gray-600 leading-relaxed;
  }

  .text-muted {
    @apply text-sm text-gray-500;
  }
}

@layer utilities {
  .text-balance {
    text-wrap: balance;
  }

  /* Enhanced Animation utilities */
  .animate-fade-in {
    animation: fadeIn 0.6s ease-out;
  }

  .animate-fade-in-up {
    animation: fadeInUp 0.6s ease-out;
  }

  .animate-slide-in-left {
    animation: slideInLeft 0.5s ease-out;
  }

  .animate-slide-in-right {
    animation: slideInRight 0.5s ease-out;
  }

  .animate-scale-in {
    animation: scaleIn 0.4s ease-out;
  }

  .animate-bounce-gentle {
    animation: bounceGentle 2s ease-in-out infinite;
  }

  /* Animation Delays */
  .delay-75 {
    animation-delay: 0.075s;
  }

  .delay-100 {
    animation-delay: 0.1s;
  }

  .delay-200 {
    animation-delay: 0.2s;
  }

  .delay-300 {
    animation-delay: 0.3s;
  }

  .delay-500 {
    animation-delay: 0.5s;
  }

  /* Spacing Utilities */
  .space-y-8 > * + * {
    margin-top: 2rem;
  }

  /* Gradient Text */
  .text-gradient {
    @apply bg-gradient-to-r from-blue-600 to-emerald-600 bg-clip-text text-transparent;
  }

  /* Focus States */
  .focus-ring {
    @apply focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2;
  }

  /* Hover Effects */
  .hover-lift {
    @apply transition-transform duration-200 hover:-translate-y-1;
  }

  .hover-glow {
    @apply transition-shadow duration-200 hover:shadow-lg;
  }
}

/* Enhanced Keyframe animations */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes bounceGentle {
  0%, 100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-5px);
  }
}

/* Enhanced Mobile Optimization */
@media (max-width: 640px) {
  .heading-primary {
    @apply text-3xl leading-tight;
  }

  .heading-secondary {
    @apply text-xl leading-tight;
  }

  .text-body {
    @apply text-base leading-relaxed;
  }

  /* Mobile-specific card adjustments */
  .card-modern {
    @apply p-4;
  }

  .card-elevated {
    @apply p-4;
  }

  /* Mobile touch targets */
  .btn-primary {
    @apply py-4 px-6 text-base;
  }

  .btn-secondary {
    @apply py-4 px-6 text-base;
  }

  /* Mobile integration cards */
  .integration-card {
    @apply p-3;
  }

  /* Mobile spacing adjustments */
  .space-y-mobile > * + * {
    margin-top: 1rem;
  }
}

/* Touch device optimizations */
@media (hover: none) and (pointer: coarse) {
  .hover-lift:hover {
    transform: none;
  }

  .hover-glow:hover {
    box-shadow: none;
  }

  /* Larger touch targets for mobile */
  button, .btn-primary, .btn-secondary, .btn-ghost {
    min-height: 44px;
    min-width: 44px;
  }

  /* Remove hover effects on touch devices */
  .card-modern:hover,
  .card-elevated:hover,
  .integration-card:hover {
    transform: none;
    box-shadow: inherit;
  }
}