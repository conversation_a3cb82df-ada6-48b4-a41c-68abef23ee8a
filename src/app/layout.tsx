import type { Metadata, Viewport } from 'next'
import { Inter } from 'next/font/google'
import './globals.css'
import '../styles/mobile-optimized.css'
import AuthProvider from '@/components/AuthProvider'
import PWAProvider from '@/components/PWAProvider'

const inter = Inter({ subsets: ['latin'] })

export const metadata: Metadata = {
  title: 'PagesLab - AI Website Generator for Kenyan Businesses',
  description: 'Create unique, culturally-aware websites for your Kenyan business in under 60 seconds',
  keywords: 'Kenya, website generator, AI, business websites, M-Pesa, Swahili',
  manifest: '/manifest.json',
  appleWebApp: {
    capable: true,
    statusBarStyle: 'default',
    title: 'PagesLab',
  },
  formatDetection: {
    telephone: false,
  },
  other: {
    'mobile-web-app-capable': 'yes',
    'apple-mobile-web-app-capable': 'yes',
    'apple-mobile-web-app-status-bar-style': 'default',
    'apple-mobile-web-app-title': 'PagesLab',
    'application-name': 'PagesLab',
    'msapplication-TileColor': '#2563eb',
    'theme-color': '#2563eb',
  },
}

export const viewport: Viewport = {
  width: 'device-width',
  initialScale: 1,
  maximumScale: 5,
  userScalable: true,
  viewportFit: 'cover',
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="en">
      <head>
        {/* PWA Icons */}
        <link rel="icon" href="/icons/icon-192x192.png" />
        <link rel="apple-touch-icon" href="/icons/icon-192x192.png" />
        <link rel="apple-touch-icon" sizes="152x152" href="/icons/icon-152x152.png" />
        <link rel="apple-touch-icon" sizes="180x180" href="/icons/icon-192x192.png" />

        {/* Preconnect to external domains for performance */}
        <link rel="preconnect" href="https://api.unsplash.com" />
        <link rel="preconnect" href="https://api.pexels.com" />
        <link rel="preconnect" href="https://fonts.googleapis.com" />
        <link rel="preconnect" href="https://fonts.gstatic.com" crossOrigin="anonymous" />

        {/* DNS prefetch for faster loading */}
        <link rel="dns-prefetch" href="//api.unsplash.com" />
        <link rel="dns-prefetch" href="//api.pexels.com" />
      </head>
      <body className={`${inter.className} safe-all`}>
        <AuthProvider>
          <PWAProvider>
            {children}
          </PWAProvider>
        </AuthProvider>
      </body>
    </html>
  )
}