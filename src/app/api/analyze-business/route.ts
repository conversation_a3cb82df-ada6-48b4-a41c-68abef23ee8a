import { NextRequest, NextResponse } from 'next/server'
import { BusinessAnalysisEngine } from '@/lib/business-analysis'
import { validateCreateWebsiteInput } from '@/lib/validations'

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    
    // Validate input
    const validation = validateCreateWebsiteInput(body)
    if (!validation.success) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Invalid input', 
          details: validation.error.issues 
        },
        { status: 400 }
      )
    }

    const { businessDescription, contactPhone, contactEmail, businessLocation } = validation.data

    // Create enhanced input for analysis
    let enhancedInput = businessDescription
    if (contactPhone) enhancedInput += ` Contact: ${contactPhone}`
    if (contactEmail) enhancedInput += ` Email: ${contactEmail}`
    if (businessLocation) enhancedInput += ` Location: ${businessLocation}`

    // Analyze business input
    const engine = new BusinessAnalysisEngine()
    const businessProfile = await engine.analyzeInput(enhancedInput)
    
    // Validate completeness
    const completenessCheck = engine.validateCompleteness(businessProfile)

    return NextResponse.json({
      success: true,
      data: {
        businessProfile,
        completenessCheck,
        analysisTime: Date.now()
      }
    })

  } catch (error) {
    console.error('Error analyzing business:', error)
    
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to analyze business information',
        message: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}