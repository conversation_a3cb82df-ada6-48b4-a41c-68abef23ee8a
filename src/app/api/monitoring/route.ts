import { NextRequest, NextResponse } from 'next/server'

interface HealthCheck {
  service: string
  status: 'healthy' | 'warning' | 'critical'
  responseTime: number
  lastCheck: string
  details?: string
}

interface SystemMetrics {
  timestamp: string
  uptime: number
  memory: {
    used: number
    total: number
    percentage: number
  }
  cpu: {
    usage: number
  }
  requests: {
    total: number
    successful: number
    failed: number
    rate: number
  }
  errors: {
    total: number
    rate: number
    byCategory: { [key: string]: number }
    bySeverity: { [key: string]: number }
  }
  performance: {
    averageResponseTime: number
    p95ResponseTime: number
    p99ResponseTime: number
  }
}

// In-memory storage for metrics (in production, use Redis or database)
let metricsStore: {
  requests: Array<{ timestamp: number; success: boolean; responseTime: number }>
  errors: Array<{ timestamp: number; code: string; severity: string; category: string }>
  startTime: number
} = {
  requests: [],
  errors: [],
  startTime: Date.now()
}

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const action = searchParams.get('action') || 'health'

    switch (action) {
      case 'health': {
        const healthChecks = await performHealthChecks()
        const overallStatus = healthChecks.every(check => check.status === 'healthy') 
          ? 'healthy' 
          : healthChecks.some(check => check.status === 'critical')
          ? 'critical'
          : 'warning'

        return NextResponse.json({
          success: true,
          data: {
            status: overallStatus,
            timestamp: new Date().toISOString(),
            checks: healthChecks,
            uptime: Math.floor((Date.now() - metricsStore.startTime) / 1000),
            version: '1.0.0'
          }
        })
      }

      case 'metrics': {
        const metrics = calculateMetrics()
        return NextResponse.json({
          success: true,
          data: metrics
        })
      }

      case 'errors': {
        const timeRange = parseInt(searchParams.get('timeRange') || '3600') // Default 1 hour
        const now = Date.now()
        const cutoff = now - (timeRange * 1000)

        const recentErrors = metricsStore.errors
          .filter(error => error.timestamp > cutoff)
          .sort((a, b) => b.timestamp - a.timestamp)

        const errorsByCode = recentErrors.reduce((acc, error) => {
          acc[error.code] = (acc[error.code] || 0) + 1
          return acc
        }, {} as { [key: string]: number })

        const errorsBySeverity = recentErrors.reduce((acc, error) => {
          acc[error.severity] = (acc[error.severity] || 0) + 1
          return acc
        }, {} as { [key: string]: number })

        const errorsByCategory = recentErrors.reduce((acc, error) => {
          acc[error.category] = (acc[error.category] || 0) + 1
          return acc
        }, {} as { [key: string]: number })

        return NextResponse.json({
          success: true,
          data: {
            total: recentErrors.length,
            timeRange,
            byCode: errorsByCode,
            bySeverity: errorsBySeverity,
            byCategory: errorsByCategory,
            recent: recentErrors.slice(0, 10).map(error => ({
              code: error.code,
              severity: error.severity,
              category: error.category,
              timestamp: new Date(error.timestamp).toISOString()
            }))
          }
        })
      }

      case 'performance': {
        const timeRange = parseInt(searchParams.get('timeRange') || '3600')
        const now = Date.now()
        const cutoff = now - (timeRange * 1000)

        const recentRequests = metricsStore.requests.filter(req => req.timestamp > cutoff)
        
        if (recentRequests.length === 0) {
          return NextResponse.json({
            success: true,
            data: {
              averageResponseTime: 0,
              p95ResponseTime: 0,
              p99ResponseTime: 0,
              throughput: 0,
              successRate: 100,
              totalRequests: 0
            }
          })
        }

        const responseTimes = recentRequests.map(req => req.responseTime).sort((a, b) => a - b)
        const successfulRequests = recentRequests.filter(req => req.success).length
        
        const averageResponseTime = responseTimes.reduce((sum, time) => sum + time, 0) / responseTimes.length
        const p95Index = Math.floor(responseTimes.length * 0.95)
        const p99Index = Math.floor(responseTimes.length * 0.99)
        
        return NextResponse.json({
          success: true,
          data: {
            averageResponseTime: Math.round(averageResponseTime),
            p95ResponseTime: responseTimes[p95Index] || 0,
            p99ResponseTime: responseTimes[p99Index] || 0,
            throughput: Math.round((recentRequests.length / timeRange) * 60), // requests per minute
            successRate: Math.round((successfulRequests / recentRequests.length) * 100),
            totalRequests: recentRequests.length,
            timeRange
          }
        })
      }

      case 'alerts': {
        const alerts = await checkAlerts()
        return NextResponse.json({
          success: true,
          data: {
            active: alerts.filter(alert => alert.active),
            resolved: alerts.filter(alert => !alert.active),
            total: alerts.length
          }
        })
      }

      default:
        return NextResponse.json(
          { success: false, error: 'Invalid action. Supported: health, metrics, errors, performance, alerts' },
          { status: 400 }
        )
    }
  } catch (error) {
    console.error('Monitoring API error:', error)
    return NextResponse.json(
      { 
        success: false, 
        error: 'Internal server error',
        details: process.env.NODE_ENV === 'development' ? (error as Error).message : undefined
      },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { type, data } = body

    switch (type) {
      case 'request': {
        // Log request metrics
        const { success, responseTime } = data
        metricsStore.requests.push({
          timestamp: Date.now(),
          success: success !== false,
          responseTime: responseTime || 0
        })

        // Keep only last 10000 requests to prevent memory issues
        if (metricsStore.requests.length > 10000) {
          metricsStore.requests = metricsStore.requests.slice(-5000)
        }

        return NextResponse.json({ success: true })
      }

      case 'error': {
        // Log error metrics
        const { code, severity, category } = data
        metricsStore.errors.push({
          timestamp: Date.now(),
          code: code || 'UNKNOWN_ERROR',
          severity: severity || 'medium',
          category: category || 'system'
        })

        // Keep only last 5000 errors
        if (metricsStore.errors.length > 5000) {
          metricsStore.errors = metricsStore.errors.slice(-2500)
        }

        return NextResponse.json({ success: true })
      }

      case 'custom': {
        // Log custom metrics
        console.log('Custom metric logged:', data)
        return NextResponse.json({ success: true })
      }

      default:
        return NextResponse.json(
          { success: false, error: 'Invalid type. Supported: request, error, custom' },
          { status: 400 }
        )
    }
  } catch (error) {
    console.error('Monitoring POST API error:', error)
    return NextResponse.json(
      { 
        success: false, 
        error: 'Internal server error',
        details: process.env.NODE_ENV === 'development' ? (error as Error).message : undefined
      },
      { status: 500 }
    )
  }
}

// Health check functions
async function performHealthChecks(): Promise<HealthCheck[]> {
  const checks: HealthCheck[] = []

  // API Health Check
  const apiStart = Date.now()
  try {
    // Simulate API health check
    await new Promise(resolve => setTimeout(resolve, Math.random() * 50))
    checks.push({
      service: 'API',
      status: 'healthy',
      responseTime: Date.now() - apiStart,
      lastCheck: new Date().toISOString()
    })
  } catch (error) {
    checks.push({
      service: 'API',
      status: 'critical',
      responseTime: Date.now() - apiStart,
      lastCheck: new Date().toISOString(),
      details: (error as Error).message
    })
  }

  // Database Health Check (simulated)
  const dbStart = Date.now()
  try {
    await new Promise(resolve => setTimeout(resolve, Math.random() * 30))
    checks.push({
      service: 'Database',
      status: 'healthy',
      responseTime: Date.now() - dbStart,
      lastCheck: new Date().toISOString()
    })
  } catch (error) {
    checks.push({
      service: 'Database',
      status: 'critical',
      responseTime: Date.now() - dbStart,
      lastCheck: new Date().toISOString(),
      details: (error as Error).message
    })
  }

  // Cache Health Check (simulated)
  const cacheStart = Date.now()
  try {
    await new Promise(resolve => setTimeout(resolve, Math.random() * 20))
    checks.push({
      service: 'Cache',
      status: 'healthy',
      responseTime: Date.now() - cacheStart,
      lastCheck: new Date().toISOString()
    })
  } catch (error) {
    checks.push({
      service: 'Cache',
      status: 'warning',
      responseTime: Date.now() - cacheStart,
      lastCheck: new Date().toISOString(),
      details: (error as Error).message
    })
  }



  return checks
}

function calculateMetrics(): SystemMetrics {
  const now = Date.now()
  const uptime = Math.floor((now - metricsStore.startTime) / 1000)
  
  // Calculate request metrics
  const totalRequests = metricsStore.requests.length
  const successfulRequests = metricsStore.requests.filter(req => req.success).length
  const failedRequests = totalRequests - successfulRequests
  
  // Calculate error metrics
  const totalErrors = metricsStore.errors.length
  const errorsByCategory = metricsStore.errors.reduce((acc, error) => {
    acc[error.category] = (acc[error.category] || 0) + 1
    return acc
  }, {} as { [key: string]: number })
  
  const errorsBySeverity = metricsStore.errors.reduce((acc, error) => {
    acc[error.severity] = (acc[error.severity] || 0) + 1
    return acc
  }, {} as { [key: string]: number })

  // Calculate performance metrics
  const responseTimes = metricsStore.requests.map(req => req.responseTime).sort((a, b) => a - b)
  const averageResponseTime = responseTimes.length > 0 
    ? responseTimes.reduce((sum, time) => sum + time, 0) / responseTimes.length 
    : 0
  
  const p95Index = Math.floor(responseTimes.length * 0.95)
  const p99Index = Math.floor(responseTimes.length * 0.99)

  return {
    timestamp: new Date().toISOString(),
    uptime,
    memory: {
      used: Math.floor(Math.random() * 500) + 200, // Simulated
      total: 1024,
      percentage: Math.floor(Math.random() * 50) + 20
    },
    cpu: {
      usage: Math.floor(Math.random() * 30) + 10
    },
    requests: {
      total: totalRequests,
      successful: successfulRequests,
      failed: failedRequests,
      rate: uptime > 0 ? Math.round((totalRequests / uptime) * 60) : 0 // requests per minute
    },
    errors: {
      total: totalErrors,
      rate: uptime > 0 ? Math.round((totalErrors / uptime) * 3600) : 0, // errors per hour
      byCategory: errorsByCategory,
      bySeverity: errorsBySeverity
    },
    performance: {
      averageResponseTime: Math.round(averageResponseTime),
      p95ResponseTime: responseTimes[p95Index] || 0,
      p99ResponseTime: responseTimes[p99Index] || 0
    }
  }
}

async function checkAlerts() {
  const metrics = calculateMetrics()
  const alerts = []

  // High error rate alert
  if (metrics.errors.rate > 10) {
    alerts.push({
      id: 'high-error-rate',
      type: 'error_rate',
      severity: 'critical',
      message: `High error rate detected: ${metrics.errors.rate} errors/hour`,
      active: true,
      timestamp: new Date().toISOString()
    })
  }

  // High response time alert
  if (metrics.performance.averageResponseTime > 1000) {
    alerts.push({
      id: 'high-response-time',
      type: 'performance',
      severity: 'warning',
      message: `High response time: ${metrics.performance.averageResponseTime}ms`,
      active: true,
      timestamp: new Date().toISOString()
    })
  }

  // High memory usage alert (simulated)
  if (metrics.memory.percentage > 80) {
    alerts.push({
      id: 'high-memory-usage',
      type: 'resource',
      severity: 'warning',
      message: `High memory usage: ${metrics.memory.percentage}%`,
      active: true,
      timestamp: new Date().toISOString()
    })
  }

  return alerts
}
