import { NextRequest, NextResponse } from 'next/server'
import { createRateLimit, RATE_LIMIT_CONFIGS } from '@/lib/rate-limiting'
import { optionalAuth } from '@/lib/auth-middleware'
import { withErrorHandling, createError } from '@/lib/error-handling'
import { trackUserBehavior, trackError } from '@/lib/analytics-service'

// Rate limiting for file uploads
const uploadRateLimit = createRateLimit({
  windowMs: 5 * 60 * 1000, // 5 minutes
  maxRequests: 10, // 10 uploads per 5 minutes
  message: 'Too many file uploads. Please wait before uploading more files.',
  headers: true
})

// Supported file types and their MIME types
const SUPPORTED_FILE_TYPES = {
  // Images
  'image/jpeg': { extension: 'jpg', category: 'image', maxSize: 10 * 1024 * 1024 }, // 10MB
  'image/jpg': { extension: 'jpg', category: 'image', maxSize: 10 * 1024 * 1024 },
  'image/png': { extension: 'png', category: 'image', maxSize: 10 * 1024 * 1024 },
  'image/webp': { extension: 'webp', category: 'image', maxSize: 10 * 1024 * 1024 },
  'image/gif': { extension: 'gif', category: 'image', maxSize: 5 * 1024 * 1024 }, // 5MB for GIFs
  
  // Documents
  'application/pdf': { extension: 'pdf', category: 'document', maxSize: 25 * 1024 * 1024 }, // 25MB
  'text/plain': { extension: 'txt', category: 'document', maxSize: 1 * 1024 * 1024 }, // 1MB
  
  // Business files
  'text/csv': { extension: 'csv', category: 'data', maxSize: 5 * 1024 * 1024 }, // 5MB
  'application/json': { extension: 'json', category: 'data', maxSize: 1 * 1024 * 1024 } // 1MB
} as const

function generateFileId(): string {
  return `file_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
}

function validateFile(file: File): { valid: boolean; error?: string } {
  // Check file type
  const fileTypeInfo = SUPPORTED_FILE_TYPES[file.type as keyof typeof SUPPORTED_FILE_TYPES]
  if (!fileTypeInfo) {
    return {
      valid: false,
      error: `Unsupported file type: ${file.type}. Supported types: ${Object.keys(SUPPORTED_FILE_TYPES).join(', ')}`
    }
  }

  // Check file size
  if (file.size > fileTypeInfo.maxSize) {
    const maxSizeMB = Math.round(fileTypeInfo.maxSize / (1024 * 1024))
    return {
      valid: false,
      error: `File too large. Maximum size for ${file.type} is ${maxSizeMB}MB`
    }
  }

  // Check filename
  if (!file.name || file.name.length > 255) {
    return {
      valid: false,
      error: 'Invalid filename. Must be between 1 and 255 characters'
    }
  }

  return { valid: true }
}

async function processImageFile(file: File): Promise<{
  processedData: string
  metadata: {
    width?: number
    height?: number
    format: string
    size: number
    optimized: boolean
  }
}> {
  // Convert file to base64 for processing
  const arrayBuffer = await file.arrayBuffer()
  const base64 = Buffer.from(arrayBuffer).toString('base64')
  const dataUrl = `data:${file.type};base64,${base64}`

  // In a real implementation, you would:
  // 1. Use sharp or similar library to get image dimensions
  // 2. Optimize the image (compress, resize, convert to WebP)
  // 3. Store in cloud storage (AWS S3, Cloudinary, etc.)
  
  return {
    processedData: dataUrl,
    metadata: {
      format: file.type,
      size: file.size,
      optimized: false // Would be true after optimization
    }
  }
}

async function processDocumentFile(file: File): Promise<{
  processedData: string
  metadata: {
    format: string
    size: number
    pageCount?: number
    textContent?: string
  }
}> {
  const arrayBuffer = await file.arrayBuffer()
  
  if (file.type === 'text/plain' || file.type === 'text/csv') {
    const textContent = new TextDecoder().decode(arrayBuffer)
    return {
      processedData: textContent,
      metadata: {
        format: file.type,
        size: file.size,
        textContent: textContent.substring(0, 1000) // First 1000 chars for preview
      }
    }
  }

  // For other document types, store as base64
  const base64 = Buffer.from(arrayBuffer).toString('base64')
  return {
    processedData: base64,
    metadata: {
      format: file.type,
      size: file.size
    }
  }
}

export async function POST(request: NextRequest) {
  // Apply rate limiting
  const rateLimitResult = await uploadRateLimit(request)
  if (rateLimitResult) {
    return rateLimitResult
  }

  // Get optional authentication context
  const authContext = await optionalAuth()(request)

  // Parse form data outside of error handling to access variables in metadata
  const formData = await request.formData()
  const files = formData.getAll('files') as File[]
  const purpose = formData.get('purpose') as string || 'general'

  return withErrorHandling(async () => {

    if (!files || files.length === 0) {
      throw createError('INVALID_INPUT', new Error('No files provided'), {
        component: 'UploadAPI',
        action: 'validateInput'
      })
    }

    if (files.length > 5) {
      throw createError('INVALID_INPUT', new Error('Maximum 5 files per upload'), {
        component: 'UploadAPI',
        action: 'validateInput'
      })
    }

    const results = []
    const errors = []

    for (const file of files) {
      try {
        // Validate file
        const validation = validateFile(file)
        if (!validation.valid) {
          errors.push({
            filename: file.name,
            error: validation.error
          })
          continue
        }

        const fileId = generateFileId()
        const fileTypeInfo = SUPPORTED_FILE_TYPES[file.type as keyof typeof SUPPORTED_FILE_TYPES]

        let processedFile
        if (fileTypeInfo.category === 'image') {
          processedFile = await processImageFile(file)
        } else {
          processedFile = await processDocumentFile(file)
        }

        // In production, you would save to cloud storage here
        // const storageUrl = await saveToCloudStorage(processedFile.processedData, fileId, file.type)

        const result = {
          id: fileId,
          filename: file.name,
          type: file.type,
          category: fileTypeInfo.category,
          size: file.size,
          uploadedAt: new Date().toISOString(),
          purpose,
          userId: authContext?.user.id,
          // url: storageUrl, // Would be the cloud storage URL
          url: `data:${file.type};base64,${processedFile.processedData.includes('base64,') ? processedFile.processedData.split('base64,')[1] : processedFile.processedData}`,
          metadata: processedFile.metadata
        }

        results.push(result)

        // Track analytics
        trackUserBehavior({
          action: 'form_submit',
          page: '/api/upload',
          element: 'file_upload',
          duration: 0
        })

      } catch (error) {
        console.error(`Error processing file ${file.name}:`, error)
        errors.push({
          filename: file.name,
          error: error instanceof Error ? error.message : 'Processing failed'
        })

        // Track error
        trackError({
          code: 'FILE_PROCESSING_ERROR',
          message: `Failed to process file: ${file.name}`,
          category: 'file_upload',
          severity: 'medium',
          context: {
            filename: file.name,
            fileType: file.type,
            fileSize: file.size,
            userId: authContext?.user.id
          }
        })
      }
    }

    return NextResponse.json({
      success: true,
      message: `Processed ${results.length} files successfully`,
      data: {
        uploaded: results,
        errors: errors,
        summary: {
          total: files.length,
          successful: results.length,
          failed: errors.length
        }
      }
    })

  }, {
    component: 'UploadAPI',
    action: 'uploadFiles',
    metadata: { purpose, userId: authContext?.user.id }
  })
}

export async function GET(request: NextRequest) {
  // Get optional authentication context
  const authContext = await optionalAuth()(request)

  return NextResponse.json({
    success: true,
    data: {
      supportedTypes: Object.keys(SUPPORTED_FILE_TYPES),
      maxSizes: Object.fromEntries(
        Object.entries(SUPPORTED_FILE_TYPES).map(([type, info]) => [
          type,
          `${Math.round(info.maxSize / (1024 * 1024))}MB`
        ])
      ),
      limits: {
        maxFilesPerUpload: 5,
        rateLimiting: '10 uploads per 5 minutes'
      },
      categories: {
        image: ['image/jpeg', 'image/png', 'image/webp', 'image/gif'],
        document: ['application/pdf', 'text/plain'],
        data: ['text/csv', 'application/json']
      }
    }
  })
}
