/**
 * Enhanced Health Check Endpoint
 * Comprehensive system health monitoring
 */

import { NextRequest, NextResponse } from 'next/server'
import { getCacheStats } from '@/lib/response-caching'
import { getRequestStats } from '@/lib/request-logging'
import { analyticsService } from '@/lib/analytics-service'

interface HealthCheck {
  status: 'healthy' | 'degraded' | 'unhealthy'
  timestamp: string
  uptime: number
  version: string
  environment: string
  checks: {
    database: HealthStatus
    cache: HealthStatus
    ai_services: HealthStatus
    image_services: HealthStatus
    performance: HealthStatus
    memory: HealthStatus
    disk: HealthStatus
  }
  metrics: {
    requests: any
    cache: any
    performance: any
    errors: any
  }
}

interface HealthStatus {
  status: 'healthy' | 'degraded' | 'unhealthy'
  message: string
  responseTime?: number
  details?: any
}

async function checkDatabase(): Promise<HealthStatus> {
  try {
    // In a real app, you'd check actual database connectivity
    // For now, we'll simulate a database check
    const start = Date.now()
    
    // Simulate database query
    await new Promise(resolve => setTimeout(resolve, 10))
    
    const responseTime = Date.now() - start
    
    return {
      status: 'healthy',
      message: 'Database connection successful',
      responseTime,
      details: {
        connectionPool: 'active',
        activeConnections: 5,
        maxConnections: 100
      }
    }
  } catch (error) {
    return {
      status: 'unhealthy',
      message: 'Database connection failed',
      details: { error: error instanceof Error ? error.message : 'Unknown error' }
    }
  }
}

async function checkCache(): Promise<HealthStatus> {
  try {
    const stats = getCacheStats()
    
    const status = stats.hitRate > 0.7 ? 'healthy' : 
                   stats.hitRate > 0.4 ? 'degraded' : 'unhealthy'
    
    return {
      status,
      message: `Cache hit rate: ${(stats.hitRate * 100).toFixed(1)}%`,
      details: stats
    }
  } catch (error) {
    return {
      status: 'unhealthy',
      message: 'Cache system error',
      details: { error: error instanceof Error ? error.message : 'Unknown error' }
    }
  }
}

async function checkAIServices(): Promise<HealthStatus> {
  try {
    const start = Date.now()
    
    // Check OpenAI API availability
    const openaiAvailable = !!process.env.OPENAI_API_KEY
    
    const responseTime = Date.now() - start
    
    if (!openaiAvailable) {
      return {
        status: 'degraded',
        message: 'AI services running in fallback mode',
        responseTime,
        details: {
          openai: 'unavailable',
          fallbackMode: 'template-based'
        }
      }
    }
    
    return {
      status: 'healthy',
      message: 'AI services operational',
      responseTime,
      details: {
        openai: 'available',
        models: ['gpt-3.5-turbo', 'gpt-4']
      }
    }
  } catch (error) {
    return {
      status: 'unhealthy',
      message: 'AI services error',
      details: { error: error instanceof Error ? error.message : 'Unknown error' }
    }
  }
}

async function checkImageServices(): Promise<HealthStatus> {
  try {
    const start = Date.now()
    
    const unsplashAvailable = !!process.env.UNSPLASH_ACCESS_KEY
    const pexelsAvailable = !!process.env.PEXELS_API_KEY
    
    const responseTime = Date.now() - start
    
    const availableServices = []
    if (unsplashAvailable) availableServices.push('unsplash')
    if (pexelsAvailable) availableServices.push('pexels')
    
    const status = availableServices.length >= 2 ? 'healthy' :
                   availableServices.length === 1 ? 'degraded' : 'unhealthy'
    
    return {
      status,
      message: `${availableServices.length} image service(s) available`,
      responseTime,
      details: {
        unsplash: unsplashAvailable ? 'available' : 'unavailable',
        pexels: pexelsAvailable ? 'available' : 'unavailable',
        fallback: 'placeholder images'
      }
    }
  } catch (error) {
    return {
      status: 'unhealthy',
      message: 'Image services error',
      details: { error: error instanceof Error ? error.message : 'Unknown error' }
    }
  }
}

async function checkPerformance(): Promise<HealthStatus> {
  try {
    const stats = getRequestStats('hour')
    
    const avgResponseTime = stats.averageResponseTime
    const errorRate = stats.errorRate
    
    const status = avgResponseTime < 1000 && errorRate < 0.05 ? 'healthy' :
                   avgResponseTime < 3000 && errorRate < 0.1 ? 'degraded' : 'unhealthy'
    
    return {
      status,
      message: `Avg response time: ${avgResponseTime.toFixed(0)}ms, Error rate: ${(errorRate * 100).toFixed(1)}%`,
      details: {
        averageResponseTime: avgResponseTime,
        errorRate,
        totalRequests: stats.totalRequests,
        successRate: stats.successRate
      }
    }
  } catch (error) {
    return {
      status: 'healthy',
      message: 'Performance monitoring unavailable',
      details: { error: 'No recent requests to analyze' }
    }
  }
}

function checkMemory(): HealthStatus {
  try {
    const memUsage = process.memoryUsage()
    const totalMem = memUsage.heapTotal
    const usedMem = memUsage.heapUsed
    const memoryUsagePercent = (usedMem / totalMem) * 100
    
    const status = memoryUsagePercent < 70 ? 'healthy' :
                   memoryUsagePercent < 85 ? 'degraded' : 'unhealthy'
    
    return {
      status,
      message: `Memory usage: ${memoryUsagePercent.toFixed(1)}%`,
      details: {
        heapUsed: Math.round(usedMem / 1024 / 1024),
        heapTotal: Math.round(totalMem / 1024 / 1024),
        external: Math.round(memUsage.external / 1024 / 1024),
        rss: Math.round(memUsage.rss / 1024 / 1024)
      }
    }
  } catch (error) {
    return {
      status: 'unhealthy',
      message: 'Memory check failed',
      details: { error: error instanceof Error ? error.message : 'Unknown error' }
    }
  }
}

function checkDisk(): HealthStatus {
  try {
    // In a real application, you'd check actual disk usage
    // For now, we'll simulate disk health
    return {
      status: 'healthy',
      message: 'Disk space sufficient',
      details: {
        available: '85%',
        used: '15%',
        total: '100GB'
      }
    }
  } catch (error) {
    return {
      status: 'unhealthy',
      message: 'Disk check failed',
      details: { error: error instanceof Error ? error.message : 'Unknown error' }
    }
  }
}

export async function GET(request: NextRequest) {
  const { searchParams } = new URL(request.url)
  const detailed = searchParams.get('detailed') === 'true'
  const format = searchParams.get('format') || 'json'

  try {
    // Perform all health checks
    const [
      database,
      cache,
      aiServices,
      imageServices,
      performance,
      memory,
      disk
    ] = await Promise.all([
      checkDatabase(),
      checkCache(),
      checkAIServices(),
      checkImageServices(),
      checkPerformance(),
      Promise.resolve(checkMemory()),
      Promise.resolve(checkDisk())
    ])

    const checks = {
      database,
      cache,
      ai_services: aiServices,
      image_services: imageServices,
      performance,
      memory,
      disk
    }

    // Determine overall system status
    const statuses = Object.values(checks).map(check => check.status)
    const overallStatus = statuses.includes('unhealthy') ? 'unhealthy' :
                         statuses.includes('degraded') ? 'degraded' : 'healthy'

    const healthCheck: HealthCheck = {
      status: overallStatus,
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
      version: '2.0.0',
      environment: process.env.NODE_ENV || 'development',
      checks,
      metrics: {
        requests: getRequestStats('hour'),
        cache: getCacheStats(),
        performance: {
          uptime: process.uptime(),
          memoryUsage: process.memoryUsage(),
          cpuUsage: process.cpuUsage()
        },
        errors: {
          recent: 0, // Would be populated from error tracking
          rate: 0
        }
      }
    }

    // Return appropriate response based on format
    if (format === 'prometheus') {
      // Return Prometheus metrics format
      const metrics = `
# HELP pageslab_health_status System health status (1=healthy, 0.5=degraded, 0=unhealthy)
# TYPE pageslab_health_status gauge
pageslab_health_status{component="overall"} ${overallStatus === 'healthy' ? 1 : overallStatus === 'degraded' ? 0.5 : 0}
pageslab_health_status{component="database"} ${database.status === 'healthy' ? 1 : database.status === 'degraded' ? 0.5 : 0}
pageslab_health_status{component="cache"} ${cache.status === 'healthy' ? 1 : cache.status === 'degraded' ? 0.5 : 0}

# HELP pageslab_uptime_seconds System uptime in seconds
# TYPE pageslab_uptime_seconds counter
pageslab_uptime_seconds ${process.uptime()}

# HELP pageslab_memory_usage_bytes Memory usage in bytes
# TYPE pageslab_memory_usage_bytes gauge
pageslab_memory_usage_bytes{type="heap_used"} ${process.memoryUsage().heapUsed}
pageslab_memory_usage_bytes{type="heap_total"} ${process.memoryUsage().heapTotal}
      `.trim()

      return new NextResponse(metrics, {
        headers: { 'Content-Type': 'text/plain' }
      })
    }

    // Return JSON response
    const response = detailed ? healthCheck : {
      status: overallStatus,
      timestamp: healthCheck.timestamp,
      uptime: healthCheck.uptime,
      version: healthCheck.version
    }

    const httpStatus = overallStatus === 'healthy' ? 200 :
                      overallStatus === 'degraded' ? 200 : 503

    return NextResponse.json(response, { status: httpStatus })

  } catch (error) {
    console.error('Health check error:', error)
    
    return NextResponse.json({
      status: 'unhealthy',
      timestamp: new Date().toISOString(),
      error: 'Health check failed',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 503 })
  }
}
