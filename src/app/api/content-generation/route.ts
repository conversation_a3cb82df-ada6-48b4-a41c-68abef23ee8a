import { NextRequest, NextResponse } from 'next/server'
import { contentGenerationService } from '@/lib/content-generation'
import type { BusinessProfile, CulturalContext } from '@/types'

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { businessProfile, culturalContext, action } = body

    if (!businessProfile || !culturalContext) {
      return NextResponse.json(
        { success: false, error: 'BusinessProfile and culturalContext are required' },
        { status: 400 }
      )
    }

    // Validate business profile
    if (!businessProfile.name || !businessProfile.type || !businessProfile.location) {
      return NextResponse.json(
        { success: false, error: 'Business name, type, and location are required' },
        { status: 400 }
      )
    }

    // Validate cultural context
    if (!culturalContext.region) {
      return NextResponse.json(
        { success: false, error: 'Cultural context region is required' },
        { status: 400 }
      )
    }

    switch (action) {
      case 'generate-complete':
        const completeContent = await contentGenerationService.generateWebsiteContent(
          businessProfile,
          culturalContext
        )
        
        return NextResponse.json({
          success: true,
          data: completeContent
        })

      case 'generate-headlines':
        const headlines = await contentGenerationService.generateHeadlines(
          businessProfile,
          culturalContext
        )
        
        return NextResponse.json({
          success: true,
          data: headlines
        })

      case 'generate-descriptions':
        const descriptions = await contentGenerationService.generateDescriptions(
          businessProfile,
          culturalContext
        )
        
        return NextResponse.json({
          success: true,
          data: descriptions
        })

      case 'generate-services':
        const services = await contentGenerationService.generateServices(
          businessProfile,
          culturalContext
        )
        
        return NextResponse.json({
          success: true,
          data: services
        })

      case 'generate-ctas':
        const callToActions = contentGenerationService.generateCallToActions(
          businessProfile,
          culturalContext
        )
        
        return NextResponse.json({
          success: true,
          data: callToActions
        })

      default:
        return NextResponse.json(
          { 
            success: false, 
            error: 'Invalid action. Use: generate-complete, generate-headlines, generate-descriptions, generate-services, or generate-ctas' 
          },
          { status: 400 }
        )
    }
  } catch (error) {
    console.error('Error in content generation API:', error)
    
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to generate content',
        message: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const action = searchParams.get('action')

    switch (action) {
      case 'status':
        const isAIAvailable = contentGenerationService.isAIAvailable()
        
        return NextResponse.json({
          success: true,
          data: {
            aiAvailable: isAIAvailable,
            fallbackMode: !isAIAvailable ? 'template-based' : null,
            supportedActions: [
              'generate-complete',
              'generate-headlines', 
              'generate-descriptions',
              'generate-services',
              'generate-ctas'
            ]
          }
        })

      case 'clear-cache':
        contentGenerationService.clearCache()
        
        return NextResponse.json({
          success: true,
          message: 'Content generation cache cleared'
        })

      default:
        return NextResponse.json(
          { 
            success: false, 
            error: 'Invalid action. Use: status or clear-cache' 
          },
          { status: 400 }
        )
    }
  } catch (error) {
    console.error('Error in content generation GET API:', error)
    
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to process request',
        message: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}