import { NextRequest, NextResponse } from 'next/server'
import { designGenerationService } from '@/lib/design-generation'
import type { BusinessProfile, CulturalContext, GeneratedContent } from '@/types'

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { businessProfile, culturalContext, generatedContent, action } = body

    console.log('🎨 Design Generation API called with action:', action)
    console.log('📋 Business Profile:', businessProfile)
    console.log('🌍 Cultural Context:', culturalContext)

    // Validate required fields
    if (!businessProfile || !culturalContext || !generatedContent) {
      return NextResponse.json({
        success: false,
        error: 'Missing required fields: businessProfile, culturalContext, and generatedContent are required'
      }, { status: 400 })
    }

    let result

    switch (action) {
      case 'generate-design':
        console.log('🎨 Generating complete design specification...')
        result = await designGenerationService.generateDesignSpec(
          businessProfile as BusinessProfile,
          culturalContext as CulturalContext,
          generatedContent as GeneratedContent
        )
        console.log('✅ Design specification generated successfully')
        break

      case 'generate-colors':
        console.log('🎨 Generating color scheme...')
        const regionalStyle = { 
          primaryColors: ['#0891b2'], 
          secondaryColors: ['#f97316'], 
          accentColors: ['#06b6d4'],
          culturalMotifs: ['modern', 'professional']
        }
        result = designGenerationService.generateColorScheme(
          culturalContext.region,
          businessProfile.type,
          regionalStyle
        )
        console.log('✅ Color scheme generated successfully')
        break

      case 'generate-layout':
        console.log('🎨 Generating layout structure...')
        result = designGenerationService.generateLayoutStructure(
          businessProfile.type,
          generatedContent
        )
        console.log('✅ Layout structure generated successfully')
        break

      case 'generate-typography':
        console.log('🎨 Generating typography specification...')
        result = designGenerationService.generateTypographySpec(
          culturalContext.region,
          businessProfile.type
        )
        console.log('✅ Typography specification generated successfully')
        break

      default:
        return NextResponse.json({
          success: false,
          error: `Unknown action: ${action}. Supported actions: generate-design, generate-colors, generate-layout, generate-typography`
        }, { status: 400 })
    }

    return NextResponse.json({
      success: true,
      data: result,
      metadata: {
        action,
        timestamp: new Date().toISOString(),
        region: culturalContext.region,
        businessType: businessProfile.type
      }
    })

  } catch (error) {
    console.error('❌ Design generation error:', error)
    
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Design generation failed',
      details: process.env.NODE_ENV === 'development' ? error : undefined
    }, { status: 500 })
  }
}

export async function GET() {
  return NextResponse.json({
    message: 'Design Generation API',
    version: '1.0.0',
    endpoints: {
      POST: {
        description: 'Generate design specifications for Kenyan businesses',
        actions: [
          'generate-design - Generate complete design specification',
          'generate-colors - Generate color scheme only',
          'generate-layout - Generate layout structure only', 
          'generate-typography - Generate typography specification only'
        ],
        requiredFields: ['businessProfile', 'culturalContext', 'generatedContent', 'action']
      }
    }
  })
}
