/**
 * API Logs Endpoint
 * View and manage API request logs
 */

import { NextRequest, NextResponse } from 'next/server'
import { getLogs, getRequestStats, exportLogs, clearLogs } from '@/lib/request-logging'
import { requireAuth } from '@/lib/auth-middleware'
import { createRateLimit, RATE_LIMIT_CONFIGS } from '@/lib/rate-limiting'

// Create rate limiter for logs access
const logsRateLimit = createRateLimit(RATE_LIMIT_CONFIGS.GENERAL)

export async function GET(request: NextRequest) {
  // Apply rate limiting
  const rateLimitResult = await logsRateLimit(request)
  if (rateLimitResult) {
    return rateLimitResult
  }

  // Require authentication for logs access
  const authResult = await requireAuth()(request)
  if (authResult instanceof NextResponse) {
    return authResult
  }

  const { searchParams } = new URL(request.url)
  const action = searchParams.get('action') || 'list'

  try {
    switch (action) {
      case 'list': {
        const filters = {
          method: searchParams.get('method') || undefined,
          path: searchParams.get('path') || undefined,
          status: searchParams.get('status') ? parseInt(searchParams.get('status')!) : undefined,
          userId: searchParams.get('userId') || undefined,
          tags: searchParams.get('tags')?.split(',') || undefined,
          startTime: searchParams.get('startTime') || undefined,
          endTime: searchParams.get('endTime') || undefined,
          limit: searchParams.get('limit') ? parseInt(searchParams.get('limit')!) : 100
        }

        const logs = getLogs(filters)

        return NextResponse.json({
          success: true,
          data: {
            logs,
            total: logs.length,
            filters: Object.fromEntries(
              Object.entries(filters).filter(([, value]) => value !== undefined)
            )
          }
        })
      }

      case 'stats': {
        const timeRange = (searchParams.get('timeRange') as 'hour' | 'day' | 'week') || 'hour'
        const stats = getRequestStats(timeRange)

        return NextResponse.json({
          success: true,
          data: {
            stats,
            timeRange,
            timestamp: new Date().toISOString()
          }
        })
      }

      case 'export': {
        const format = (searchParams.get('format') as 'json' | 'csv') || 'json'
        const exportData = exportLogs(format)

        const contentType = format === 'csv' ? 'text/csv' : 'application/json'
        const filename = `pageslab-logs-${new Date().toISOString().split('T')[0]}.${format}`

        return new NextResponse(exportData, {
          headers: {
            'Content-Type': contentType,
            'Content-Disposition': `attachment; filename="${filename}"`
          }
        })
      }

      case 'search': {
        const query = searchParams.get('query')
        if (!query) {
          return NextResponse.json({
            success: false,
            error: 'Search query is required'
          }, { status: 400 })
        }

        const logs = getLogs({
          limit: 1000
        }).filter(log => {
          const searchText = `${log.method} ${log.path} ${log.userAgent} ${log.error || ''}`.toLowerCase()
          return searchText.includes(query.toLowerCase())
        })

        return NextResponse.json({
          success: true,
          data: {
            logs,
            query,
            total: logs.length
          }
        })
      }

      case 'summary': {
        const recentLogs = getLogs({ limit: 1000 })
        const stats = getRequestStats('day')

        // Calculate additional metrics
        const errorLogs = recentLogs.filter(log => log.responseStatus >= 400)
        const slowRequests = recentLogs.filter(log => log.responseTime > 3000)
        const uniqueUsers = new Set(recentLogs.map(log => log.userId).filter(Boolean)).size
        const uniqueIPs = new Set(recentLogs.map(log => log.ip)).size

        const topErrors = errorLogs.reduce((acc, log) => {
          const error = log.error || 'Unknown error'
          acc[error] = (acc[error] || 0) + 1
          return acc
        }, {} as Record<string, number>)

        const topUserAgents = recentLogs.reduce((acc, log) => {
          if (log.userAgent) {
            const ua = log.userAgent.split(' ')[0] // Get first part of user agent
            acc[ua] = (acc[ua] || 0) + 1
          }
          return acc
        }, {} as Record<string, number>)

        return NextResponse.json({
          success: true,
          data: {
            overview: {
              totalRequests: stats.totalRequests,
              successRate: stats.successRate,
              errorRate: stats.errorRate,
              averageResponseTime: stats.averageResponseTime,
              uniqueUsers,
              uniqueIPs
            },
            performance: {
              slowRequests: slowRequests.length,
              slowRequestsPercentage: recentLogs.length > 0 ? (slowRequests.length / recentLogs.length) * 100 : 0,
              averageResponseTime: stats.averageResponseTime,
              p95ResponseTime: recentLogs.length > 0 ? 
                recentLogs.sort((a, b) => a.responseTime - b.responseTime)[Math.floor(recentLogs.length * 0.95)]?.responseTime || 0 : 0
            },
            errors: {
              totalErrors: errorLogs.length,
              errorRate: stats.errorRate,
              topErrors: Object.entries(topErrors)
                .sort(([, a], [, b]) => b - a)
                .slice(0, 10)
                .map(([error, count]) => ({ error, count }))
            },
            traffic: {
              topEndpoints: stats.topEndpoints,
              methodDistribution: stats.methodDistribution,
              statusCodes: stats.statusCodes,
              topUserAgents: Object.entries(topUserAgents)
                .sort(([, a], [, b]) => b - a)
                .slice(0, 10)
                .map(([userAgent, count]) => ({ userAgent, count }))
            },
            timestamp: new Date().toISOString()
          }
        })
      }

      default:
        return NextResponse.json({
          success: false,
          error: 'Invalid action',
          availableActions: ['list', 'stats', 'export', 'search', 'summary']
        }, { status: 400 })
    }
  } catch (error) {
    console.error('Logs API error:', error)
    return NextResponse.json({
      success: false,
      error: 'Failed to retrieve logs',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}

export async function DELETE(request: NextRequest) {
  // Apply rate limiting
  const rateLimitResult = await logsRateLimit(request)
  if (rateLimitResult) {
    return rateLimitResult
  }

  // Require authentication for logs management
  const authResult = await requireAuth()(request)
  if (authResult instanceof NextResponse) {
    return authResult
  }

  const { searchParams } = new URL(request.url)
  const confirm = searchParams.get('confirm')

  if (confirm !== 'true') {
    return NextResponse.json({
      success: false,
      error: 'Confirmation required',
      message: 'Add ?confirm=true to clear all logs'
    }, { status: 400 })
  }

  try {
    clearLogs()

    return NextResponse.json({
      success: true,
      message: 'All logs cleared successfully',
      timestamp: new Date().toISOString()
    })
  } catch (error) {
    console.error('Clear logs error:', error)
    return NextResponse.json({
      success: false,
      error: 'Failed to clear logs',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}

export async function OPTIONS() {
  return NextResponse.json({
    success: true,
    methods: ['GET', 'DELETE'],
    endpoints: {
      'GET /api/logs': {
        description: 'Retrieve API request logs',
        parameters: {
          action: 'list | stats | export | search | summary',
          method: 'Filter by HTTP method',
          path: 'Filter by request path',
          status: 'Filter by response status code',
          userId: 'Filter by user ID',
          tags: 'Filter by tags (comma-separated)',
          startTime: 'Filter by start time (ISO string)',
          endTime: 'Filter by end time (ISO string)',
          limit: 'Limit number of results',
          timeRange: 'Time range for stats (hour|day|week)',
          format: 'Export format (json|csv)',
          query: 'Search query for text search'
        }
      },
      'DELETE /api/logs': {
        description: 'Clear all logs',
        parameters: {
          confirm: 'Must be "true" to confirm deletion'
        }
      }
    },
    authentication: 'Required',
    rateLimit: 'Applied'
  })
}
