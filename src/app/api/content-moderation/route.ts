import { NextRequest, NextResponse } from 'next/server'
import { contentModerator, analyzeContent, isContentAppropriate } from '@/lib/content-moderation'

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { content, language = 'auto', context = 'business', action = 'analyze' } = body

    if (!content || typeof content !== 'string') {
      return NextResponse.json(
        { success: false, error: 'Content is required and must be a string' },
        { status: 400 }
      )
    }

    if (content.length > 50000) {
      return NextResponse.json(
        { success: false, error: 'Content too long. Maximum 50,000 characters allowed.' },
        { status: 400 }
      )
    }

    switch (action) {
      case 'analyze': {
        const analysis = await analyzeContent(content, language, context)
        
        return NextResponse.json({
          success: true,
          data: analysis,
          metadata: {
            contentLength: content.length,
            wordCount: content.split(/\s+/).length,
            timestamp: new Date().toISOString()
          }
        })
      }

      case 'check': {
        const isAppropriate = await isContentAppropriate(content)
        const quickAnalysis = await analyzeContent(content, language, context)
        
        return NextResponse.json({
          success: true,
          data: {
            appropriate: isAppropriate,
            score: quickAnalysis.score,
            summary: {
              totalIssues: quickAnalysis.issues.length,
              culturalScore: quickAnalysis.culturalSensitivity.score,
              readabilityScore: quickAnalysis.readability.score,
              seoScore: quickAnalysis.seoScore.score
            }
          }
        })
      }

      case 'suggestions': {
        const analysis = await analyzeContent(content, language, context)
        
        return NextResponse.json({
          success: true,
          data: {
            suggestions: analysis.suggestions,
            culturalRecommendations: analysis.culturalSensitivity.recommendations,
            readabilityRecommendations: analysis.readability.recommendations,
            seoRecommendations: analysis.seoScore.recommendations
          }
        })
      }



      case 'cultural-check': {
        const analysis = await analyzeContent(content, language, context)
        
        return NextResponse.json({
          success: true,
          data: {
            culturalSensitivity: analysis.culturalSensitivity,
            appropriate: analysis.culturalSensitivity.kenyanContext.appropriate,
            recommendations: analysis.culturalSensitivity.recommendations
          }
        })
      }

      default:
        return NextResponse.json(
          { success: false, error: 'Invalid action. Supported actions: analyze, check, suggestions, cultural-check' },
          { status: 400 }
        )
    }
  } catch (error) {
    console.error('Content moderation API error:', error)
    
    return NextResponse.json(
      { 
        success: false, 
        error: 'Internal server error during content analysis',
        details: process.env.NODE_ENV === 'development' ? (error as Error).message : undefined
      },
      { status: 500 }
    )
  }
}

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const action = searchParams.get('action')

    switch (action) {
      case 'health': {
        return NextResponse.json({
          success: true,
          data: {
            status: 'healthy',
            version: '1.0.0',
            features: [
              'content-analysis',
              'cultural-sensitivity',
              'readability-analysis',
              'seo-analysis'
            ],
            supportedLanguages: ['en', 'sw', 'mixed'],
            supportedContexts: ['business', 'general']
          }
        })
      }

      case 'stats': {
        // In a real application, you would track usage statistics
        return NextResponse.json({
          success: true,
          data: {
            totalAnalyses: 0, // Would be tracked in database
            averageScore: 0,
            commonIssues: [
              'Missing cultural context',
              'Poor readability',
              'SEO optimization needed'
            ],
            topSuggestions: [
              'Add Kenyan cultural references',
              'Simplify language for better readability',
              'Include proper headings for SEO',
              'Use consistent language throughout'
            ]
          }
        })
      }

      case 'dictionaries': {
        return NextResponse.json({
          success: true,
          data: {
            english: {
              wordCount: 1000, // Simplified for demo
              lastUpdated: '2024-01-01'
            },
            swahili: {
              wordCount: 500,
              lastUpdated: '2024-01-01'
            },
            kenyanTerms: {
              wordCount: 100,
              lastUpdated: '2024-01-01'
            }
          }
        })
      }

      default:
        return NextResponse.json(
          { success: false, error: 'Invalid action. Supported actions: health, stats, dictionaries' },
          { status: 400 }
        )
    }
  } catch (error) {
    console.error('Content moderation API GET error:', error)
    
    return NextResponse.json(
      { 
        success: false, 
        error: 'Internal server error',
        details: process.env.NODE_ENV === 'development' ? (error as Error).message : undefined
      },
      { status: 500 }
    )
  }
}

// Batch content analysis endpoint
export async function PUT(request: NextRequest) {
  try {
    const body = await request.json()
    const { contents, language = 'auto', context = 'business' } = body

    if (!Array.isArray(contents)) {
      return NextResponse.json(
        { success: false, error: 'Contents must be an array' },
        { status: 400 }
      )
    }

    if (contents.length > 10) {
      return NextResponse.json(
        { success: false, error: 'Maximum 10 contents can be analyzed in batch' },
        { status: 400 }
      )
    }

    const results = await Promise.all(
      contents.map(async (content, index) => {
        try {
          if (typeof content !== 'string') {
            return {
              index,
              success: false,
              error: 'Content must be a string'
            }
          }

          if (content.length > 10000) {
            return {
              index,
              success: false,
              error: 'Content too long for batch processing (max 10,000 characters per item)'
            }
          }

          const analysis = await analyzeContent(content, language, context)
          
          return {
            index,
            success: true,
            data: analysis,
            metadata: {
              contentLength: content.length,
              wordCount: content.split(/\s+/).length
            }
          }
        } catch (error) {
          return {
            index,
            success: false,
            error: 'Analysis failed for this content'
          }
        }
      })
    )

    const successful = results.filter(r => r.success)
    const failed = results.filter(r => !r.success)

    return NextResponse.json({
      success: true,
      data: {
        results,
        summary: {
          total: contents.length,
          successful: successful.length,
          failed: failed.length,
          averageScore: successful.length > 0 
            ? successful.reduce((sum, r) => sum + (r.data?.score || 0), 0) / successful.length 
            : 0
        }
      }
    })
  } catch (error) {
    console.error('Batch content moderation API error:', error)
    
    return NextResponse.json(
      { 
        success: false, 
        error: 'Internal server error during batch analysis',
        details: process.env.NODE_ENV === 'development' ? (error as Error).message : undefined
      },
      { status: 500 }
    )
  }
}

// Content improvement suggestions endpoint
export async function PATCH(request: NextRequest) {
  try {
    const body = await request.json()
    const { content, targetScore = 80, language = 'auto', context = 'business' } = body

    if (!content || typeof content !== 'string') {
      return NextResponse.json(
        { success: false, error: 'Content is required and must be a string' },
        { status: 400 }
      )
    }

    const analysis = await analyzeContent(content, language, context)
    
    // Generate specific improvement recommendations based on current score
    const improvements = []
    
    if (analysis.score < targetScore) {
      // Priority improvements based on biggest impact

      if (analysis.culturalSensitivity.score < 70) {
        improvements.push({
          priority: 'high',
          category: 'cultural',
          message: 'Add Kenyan cultural context and local references',
          impact: 'high',
          effort: 'medium',
          examples: [
            'Mention specific Kenyan locations (Nairobi, Mombasa, etc.)',
            'Include local business practices or cultural elements',
            'Use appropriate Swahili greetings or terms'
          ]
        })
      }

      if (analysis.readability.score < 60) {
        improvements.push({
          priority: 'high',
          category: 'readability',
          message: 'Simplify language and sentence structure',
          impact: 'high',
          effort: 'medium',
          examples: [
            'Use shorter sentences (under 20 words)',
            'Replace complex words with simpler alternatives',
            'Break up long paragraphs'
          ]
        })
      }

      if (analysis.seoScore.score < 60) {
        improvements.push({
          priority: 'medium',
          category: 'seo',
          message: 'Improve SEO structure and keywords',
          impact: 'medium',
          effort: 'low',
          examples: [
            'Add proper H1 and H2 headings',
            'Include relevant keywords naturally',
            'Ensure proper content length (150+ words)'
          ]
        })
      }


    }

    return NextResponse.json({
      success: true,
      data: {
        currentScore: analysis.score,
        targetScore,
        gap: targetScore - analysis.score,
        improvements,
        estimatedNewScore: Math.min(100, analysis.score + improvements.length * 10),
        priorityOrder: improvements.sort((a, b) => {
          const priorityOrder: Record<string, number> = { critical: 4, high: 3, medium: 2, low: 1 }
          return priorityOrder[b.priority] - priorityOrder[a.priority]
        })
      }
    })
  } catch (error) {
    console.error('Content improvement API error:', error)
    
    return NextResponse.json(
      { 
        success: false, 
        error: 'Internal server error during improvement analysis',
        details: process.env.NODE_ENV === 'development' ? (error as Error).message : undefined
      },
      { status: 500 }
    )
  }
}
