import { NextRequest, NextResponse } from 'next/server'
import jwt from 'jsonwebtoken'
import { z } from 'zod'
import {
  getUserWebsites,
  createWebsite as dbCreateWebsite,
  updateWebsite as dbUpdateWebsite,
  deleteWebsite as dbDeleteWebsite,
  getUserByEmail
} from '@/lib/database'

// No more mock database - using Prisma only

const websiteSchema = z.object({
  name: z.string().min(1, 'Website name is required'),
  description: z.string().optional(),
  html: z.string().min(1, 'HTML content is required'),
  // CSS is embedded in HTML for Pure AI - no separate CSS field needed
  businessProfile: z.object({
    name: z.string(),
    type: z.string().optional(),
    location: z.union([
      z.string(), // Simple string format
      z.object({
        area: z.string().optional(),
        county: z.string().optional()
      })
    ]).optional(),
    contactInfo: z.object({
      phone: z.string().optional(),
      email: z.string().optional()
    }).optional()
  }),
  isPublished: z.boolean().default(false)
})

async function verifyAuth(request: NextRequest) {
  const token = request.cookies.get('auth-token')?.value ||
                request.headers.get('Authorization')?.replace('Bearer ', '')

  if (!token) {
    throw new Error('No authentication token provided')
  }

  const decoded = jwt.verify(
    token,
    process.env.JWT_SECRET || 'fallback-secret-key'
  ) as { userId: string; email: string }

  // Find user in Prisma database
  const user = await getUserByEmail(decoded.email.toLowerCase())
  if (!user) {
    throw new Error('User not found')
  }

  return {
    id: user.id,
    email: user.email,
    name: user.name || 'User'
  }
}

// GET /api/websites - Get all websites for the authenticated user
export async function GET(request: NextRequest) {
  try {
    const user = await verifyAuth(request)

    // Get user's websites from database
    const userWebsites = await getUserWebsites(user.id)

    return NextResponse.json({
      success: true,
      websites: userWebsites
    })

  } catch (error) {
    console.error('Get websites error:', error)
    
    if (error instanceof jwt.JsonWebTokenError || (error as Error).message.includes('token') || (error as Error).message.includes('User not found')) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// POST /api/websites - Create a new website
export async function POST(request: NextRequest) {
  try {
    const user = await verifyAuth(request)
    const body = await request.json()
    
    // Validate input
    const validatedData = websiteSchema.parse(body)
    
    // Create website in database with simplified business profile
    const subdomain = `${validatedData.name.toLowerCase().replace(/[^a-z0-9]/g, '-')}-${Date.now().toString().slice(-4)}`

    // Create a complete BusinessProfile from the simplified data
    const fullBusinessProfile = {
      name: validatedData.businessProfile.name,
      type: (validatedData.businessProfile.type || 'OTHER') as any,
      location: {
        county: typeof validatedData.businessProfile.location === 'string'
          ? validatedData.businessProfile.location
          : validatedData.businessProfile.location?.county || 'Unknown',
        area: typeof validatedData.businessProfile.location === 'string'
          ? validatedData.businessProfile.location
          : validatedData.businessProfile.location?.area || 'Unknown',
        region: 'CENTRAL' as any
      },
      services: ['General Services'], // Default services
      description: validatedData.description || `Website for ${validatedData.businessProfile.name}`,
      targetAudience: 'General Public',
      contactInfo: {
        phone: validatedData.businessProfile.contactInfo?.phone || '',
        email: validatedData.businessProfile.contactInfo?.email || '',
        address: typeof validatedData.businessProfile.location === 'string'
          ? validatedData.businessProfile.location
          : validatedData.businessProfile.location?.area || 'Kenya'
      },
      culturalContext: {
        region: 'CENTRAL' as any,
        language: 'ENGLISH' as any,
        businessPractices: {
          paymentMethods: [{ type: 'MPESA' as any, displayName: 'M-Pesa', isActive: true }],
          communicationPreferences: ['WhatsApp'],
          businessHours: 'Mon-Fri: 8AM-5PM',
          culturalConsiderations: []
        },
        culturalElements: []
      }
    }

    const website = await dbCreateWebsite({
      userId: user.id,
      name: validatedData.name,
      businessProfile: fullBusinessProfile,
      generatedContent: {
        headline: validatedData.businessProfile.name,
        subheadline: validatedData.description || '',
        aboutSection: validatedData.description || '',
        services: [],
        callToActions: [],
        contactSection: {
          title: 'Contact Us',
          description: 'Get in touch',
          methods: []
        }
      },
      designSpec: {
        colorScheme: {
          primary: '#6C9A17',
          secondary: '#F4D03F',
          accent: '#E57F3D',
          background: '#F7F7F7',
          text: '#333333',
          muted: '#666666',
          success: '#10B981',
          warning: '#F59E0B',
          error: '#EF4444'
        },
        typography: {
          headingFont: 'Inter',
          bodyFont: 'Inter',
          headingSizes: { h1: '2rem', h2: '1.5rem', h3: '1.25rem' },
          bodySize: '1rem',
          lineHeight: '1.6'
        },
        layout: {
          sections: [],
          navigation: {
            type: 'HORIZONTAL' as any,
            items: [],
            styling: {
              backgroundColor: '#ffffff',
              textColor: '#333333',
              hoverColor: '#6C9A17',
              position: 'STATIC' as any
            }
          },
          footer: {
            content: {
              businessInfo: validatedData.businessProfile.name,
              contactInfo: fullBusinessProfile.contactInfo,
              copyright: `© 2024 ${validatedData.businessProfile.name}`
            },
            styling: {
              backgroundColor: '#333333',
              textColor: '#ffffff',
              layout: 'SIMPLE' as any
            }
          }
        },
        culturalElements: [],
        responsiveBreakpoints: {
          mobile: '768px',
          tablet: '1024px',
          desktop: '1280px',
          largeDesktop: '1536px'
        }
      },
      htmlOutput: validatedData.html,
      cssOutput: '', // Pure AI embeds CSS in HTML
      subdomain
    })

    return NextResponse.json({
      success: true,
      website,
      message: 'Website created successfully'
    })

  } catch (error) {
    console.error('Create website error:', error)
    
    if (error instanceof jwt.JsonWebTokenError || (error as Error).message.includes('token') || (error as Error).message.includes('User not found')) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Invalid input data', details: error.errors },
        { status: 400 }
      )
    }

    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// PUT /api/websites - Update an existing website
export async function PUT(request: NextRequest) {
  try {
    const user = await verifyAuth(request)
    const body = await request.json()
    
    const { id, ...updateData } = body
    
    if (!id) {
      return NextResponse.json(
        { error: 'Website ID is required' },
        { status: 400 }
      )
    }

    // Validate update data
    const validatedData = websiteSchema.partial().parse(updateData)

    // Update website in database
    const updatedWebsite = await dbUpdateWebsite(id, {
      name: validatedData.name,
      htmlOutput: validatedData.html,
      isPublished: validatedData.isPublished
      // Skip businessProfile update for now to avoid type issues
      // CSS is embedded in HTML for Pure AI - no separate CSS field needed
    })

    return NextResponse.json({
      success: true,
      website: updatedWebsite,
      message: 'Website updated successfully'
    })

  } catch (error) {
    console.error('Update website error:', error)
    
    if (error instanceof jwt.JsonWebTokenError || (error as Error).message.includes('token') || (error as Error).message.includes('User not found')) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Invalid input data', details: error.errors },
        { status: 400 }
      )
    }

    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// DELETE /api/websites - Delete a website
export async function DELETE(request: NextRequest) {
  try {
    const user = await verifyAuth(request)
    const { searchParams } = new URL(request.url)
    const id = searchParams.get('id')
    
    if (!id) {
      return NextResponse.json(
        { error: 'Website ID is required' },
        { status: 400 }
      )
    }

    // Delete website from database (includes ownership check)
    await dbDeleteWebsite(id)

    return NextResponse.json({
      success: true,
      message: 'Website deleted successfully'
    })

  } catch (error) {
    console.error('Delete website error:', error)
    
    if (error instanceof jwt.JsonWebTokenError || (error as Error).message.includes('token') || (error as Error).message.includes('User not found')) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
