/**
 * API Documentation Endpoint
 * Provides comprehensive documentation for all PagesLab API endpoints
 */

import { NextRequest, NextResponse } from 'next/server'
import { getVersionDocumentation } from '@/lib/api-versioning'
import { getCacheStats } from '@/lib/response-caching'
import { RATE_LIMIT_CONFIGS } from '@/lib/rate-limiting'

export async function GET(request: NextRequest) {
  const { searchParams } = new URL(request.url)
  const format = searchParams.get('format') || 'json'
  const section = searchParams.get('section')

  const documentation = {
    info: {
      title: 'PagesLab API',
      description: 'AI-powered website generator for Kenyan businesses',
      version: '2.0.0',
      contact: {
        name: 'PagesLab Support',
        email: '<EMAIL>',
        url: 'https://pageslab.co.ke'
      },
      license: {
        name: 'MIT',
        url: 'https://opensource.org/licenses/MIT'
      }
    },

    servers: [
      {
        url: 'https://pageslab.co.ke/api',
        description: 'Production server'
      },
      {
        url: 'http://localhost:3000/api',
        description: 'Development server'
      }
    ],

    versioning: getVersionDocumentation(),

    authentication: {
      types: {
        jwt: {
          type: 'http',
          scheme: 'bearer',
          bearerFormat: 'JWT',
          description: 'JWT token obtained from /api/auth/login'
        },
        apiKey: {
          type: 'apiKey',
          in: 'header',
          name: 'X-API-Key',
          description: 'API key for external integrations'
        },
        cookie: {
          type: 'apiKey',
          in: 'cookie',
          name: 'auth-token',
          description: 'HTTP-only authentication cookie'
        }
      },
      examples: {
        jwt: 'Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...',
        apiKey: 'X-API-Key: your-api-key-here',
        cookie: 'Cookie: auth-token=your-jwt-token'
      }
    },

    rateLimiting: {
      description: 'All endpoints are rate limited to ensure fair usage',
      configs: RATE_LIMIT_CONFIGS,
      headers: {
        'X-RateLimit-Limit': 'Maximum requests allowed in the time window',
        'X-RateLimit-Remaining': 'Number of requests remaining in the current window',
        'X-RateLimit-Reset': 'Time when the rate limit window resets (Unix timestamp)',
        'Retry-After': 'Seconds to wait before making another request (when rate limited)'
      }
    },

    endpoints: {
      '/api/generate-website': {
        post: {
          summary: 'Generate a complete website',
          description: 'Creates a stunning, mobile-responsive website from business description',
          tags: ['Website Generation'],
          requestBody: {
            required: true,
            content: {
              'application/json': {
                schema: {
                  type: 'object',
                  required: ['businessDescription'],
                  properties: {
                    businessDescription: {
                      type: 'string',
                      minLength: 10,
                      maxLength: 1000,
                      description: 'Detailed description of the business',
                      example: 'I run a beauty salon in Westlands offering hair styling, manicures, and pedicures. Contact us on 0741152263'
                    },
                    includeImages: {
                      type: 'boolean',
                      default: true,
                      description: 'Whether to include AI-generated images'
                    },
                    selectedIntegrations: {
                      type: 'object',
                      properties: {
                        whatsapp: { type: 'boolean' },
                        phone: { type: 'boolean' },
                        email: { type: 'boolean' },
                        maps: { type: 'boolean' },
                        mpesa: { type: 'boolean' },
                        social: { type: 'boolean' }
                      }
                    }
                  }
                }
              }
            }
          },
          responses: {
            200: {
              description: 'Website generated successfully',
              content: {
                'application/json': {
                  schema: {
                    type: 'object',
                    properties: {
                      success: { type: 'boolean' },
                      data: {
                        type: 'object',
                        properties: {
                          html: { type: 'string', description: 'Complete HTML code' },
                          businessProfile: { type: 'object' },
                          generatedContent: { type: 'object' },
                          designSpec: { type: 'object' },
                          performanceMetrics: { type: 'object' }
                        }
                      }
                    }
                  }
                }
              }
            },
            400: { description: 'Invalid input data' },
            429: { description: 'Rate limit exceeded' },
            500: { description: 'Internal server error' }
          },
          security: [{ jwt: [] }, { cookie: [] }]
        }
      },

      '/api/analyze-business': {
        post: {
          summary: 'Analyze business description',
          description: 'Extract business type, location, services, and cultural context',
          tags: ['Business Analysis'],
          requestBody: {
            required: true,
            content: {
              'application/json': {
                schema: {
                  type: 'object',
                  required: ['businessDescription'],
                  properties: {
                    businessDescription: { type: 'string', minLength: 10 },
                    contactPhone: { type: 'string' },
                    contactEmail: { type: 'string', format: 'email' },
                    businessLocation: { type: 'string' }
                  }
                }
              }
            }
          },
          responses: {
            200: {
              description: 'Business analysis completed',
              content: {
                'application/json': {
                  schema: {
                    type: 'object',
                    properties: {
                      success: { type: 'boolean' },
                      data: {
                        type: 'object',
                        properties: {
                          businessProfile: { type: 'object' },
                          completenessCheck: { type: 'object' },
                          analysisTime: { type: 'number' }
                        }
                      }
                    }
                  }
                }
              }
            }
          }
        }
      },

      '/api/content-generation': {
        post: {
          summary: 'Generate website content',
          description: 'Create AI-powered content for different website sections',
          tags: ['Content Generation'],
          parameters: [
            {
              name: 'action',
              in: 'query',
              required: true,
              schema: {
                type: 'string',
                enum: ['generate-complete', 'generate-headlines', 'generate-descriptions', 'generate-services', 'generate-ctas']
              }
            }
          ]
        }
      },

      '/api/design-generation': {
        post: {
          summary: 'Generate design specifications',
          description: 'Create modern design specs with colors, typography, and layouts',
          tags: ['Design Generation']
        }
      },

      '/api/analytics': {
        get: {
          summary: 'Get analytics data',
          description: 'Retrieve website generation and user behavior analytics',
          tags: ['Analytics'],
          security: [{ jwt: [] }],
          parameters: [
            {
              name: 'action',
              in: 'query',
              schema: {
                type: 'string',
                enum: ['summary', 'events', 'export', 'health', 'clear']
              }
            }
          ]
        },
        post: {
          summary: 'Track analytics event',
          description: 'Record user behavior and system events',
          tags: ['Analytics'],
          security: [{ jwt: [] }]
        }
      },

      '/api/upload': {
        post: {
          summary: 'Upload files',
          description: 'Upload images, documents, or data files',
          tags: ['File Upload'],
          requestBody: {
            required: true,
            content: {
              'multipart/form-data': {
                schema: {
                  type: 'object',
                  properties: {
                    files: {
                      type: 'array',
                      items: { type: 'string', format: 'binary' }
                    },
                    purpose: {
                      type: 'string',
                      enum: ['website-assets', 'user-content', 'business-documents']
                    }
                  }
                }
              }
            }
          }
        }
      },

      '/api/auth/login': {
        post: {
          summary: 'User authentication',
          description: 'Login with email and password',
          tags: ['Authentication'],
          requestBody: {
            required: true,
            content: {
              'application/json': {
                schema: {
                  type: 'object',
                  required: ['email', 'password'],
                  properties: {
                    email: { type: 'string', format: 'email' },
                    password: { type: 'string', minLength: 6 }
                  }
                }
              }
            }
          }
        }
      },

      '/api/performance': {
        get: {
          summary: 'Performance metrics',
          description: 'Get system performance data and optimization reports',
          tags: ['Performance']
        }
      }
    },

    errorCodes: {
      'VALIDATION_ERROR': 'Input validation failed',
      'UNAUTHORIZED': 'Authentication required',
      'FORBIDDEN': 'Insufficient permissions',
      'RATE_LIMIT_EXCEEDED': 'Too many requests',
      'BUSINESS_ANALYSIS_FAILED': 'Could not analyze business description',
      'CONTENT_GENERATION_FAILED': 'AI content generation failed',
      'DESIGN_GENERATION_FAILED': 'Design generation failed',
      'WEBSITE_GENERATION_FAILED': 'Website generation failed',
      'FILE_UPLOAD_FAILED': 'File upload failed',
      'INTERNAL_ERROR': 'Internal server error'
    },

    examples: {
      businessDescriptions: [
        'I run a beauty salon in Westlands offering hair styling, manicures, and pedicures. Contact us on 0741152263',
        'Mama Njeri\'s Restaurant in Kibera serving traditional Kenyan food like ugali, nyama choma, and sukuma wiki',
        'Tech repair shop in CBD fixing phones, laptops, and electronics. WhatsApp 0722123456',
        'Children\'s clothing store in Karen selling quality clothes for kids aged 0-12 years'
      ]
    },

    sdks: {
      javascript: {
        installation: 'npm install @pageslab/api-client',
        example: `
import { PagesLabClient } from '@pageslab/api-client'

const client = new PagesLabClient({
  apiKey: 'your-api-key',
  baseUrl: 'https://pageslab.co.ke/api'
})

const website = await client.generateWebsite({
  businessDescription: 'My salon in Westlands...'
})
        `
      }
    },

    status: {
      cache: getCacheStats(),
      uptime: process.uptime(),
      version: '2.0.0',
      environment: process.env.NODE_ENV || 'development'
    }
  }

  // Return specific section if requested
  if (section) {
    const sectionData = (documentation as any)[section]
    if (!sectionData) {
      return NextResponse.json({
        success: false,
        error: `Documentation section '${section}' not found`,
        availableSections: Object.keys(documentation)
      }, { status: 404 })
    }

    return NextResponse.json({
      success: true,
      data: sectionData
    })
  }

  // Return full documentation
  if (format === 'html') {
    // Return HTML documentation (could be enhanced with a proper template)
    const html = `
<!DOCTYPE html>
<html>
<head>
  <title>PagesLab API Documentation</title>
  <style>
    body { font-family: Arial, sans-serif; margin: 40px; }
    .endpoint { margin: 20px 0; padding: 15px; border: 1px solid #ddd; }
    .method { font-weight: bold; color: #007bff; }
    pre { background: #f8f9fa; padding: 10px; overflow-x: auto; }
  </style>
</head>
<body>
  <h1>PagesLab API Documentation</h1>
  <p>AI-powered website generator for Kenyan businesses</p>
  <pre>${JSON.stringify(documentation, null, 2)}</pre>
</body>
</html>
    `
    
    return new NextResponse(html, {
      headers: { 'Content-Type': 'text/html' }
    })
  }

  return NextResponse.json({
    success: true,
    data: documentation
  })
}
