import { NextRequest, NextResponse } from 'next/server'
import { PureAIWebsiteGenerator } from '@/lib/pure-ai-generator'

// Main website generation endpoint - Pure AI only (no templates)
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { businessDescription, includeImages, integrations, integrationData } = body

    if (!businessDescription) {
      return NextResponse.json(
        { success: false, error: 'Business description is required' },
        { status: 400 }
      )
    }

    console.log('🎨 Starting Pure AI Website Generation...')
    console.log('📝 Business:', businessDescription)
    console.log('🖼️ Include Images:', includeImages)
    console.log('🔗 Integrations:', Object.keys(integrations || {}))

    const generator = new PureAIWebsiteGenerator()
    
    const startTime = Date.now()
    
    // Generate unique website using Pure AI
    const website = await generator.generateUniqueWebsite({
      businessDescription,
      includeImages: includeImages || false,
      integrations: integrations || {},
      integrationData: integrationData || {}
    })

    // Validate quality
    const quality = await generator.validateWebsiteQuality(website)
    
    const generationTime = Date.now() - startTime

    console.log('✅ Pure AI Generation Complete!')
    console.log('⏱️ Generation Time:', generationTime, 'ms')
    console.log('📊 Quality Score:', quality.score, '/10')
    console.log('🎨 Unique Features:', website.uniqueFeatures)
    console.log('🇰🇪 Kenya Elements:', website.kenyaSpecificElements)

    // Extract business profile from description
    const businessProfile = {
      name: extractBusinessName(businessDescription),
      description: businessDescription,
      type: inferBusinessType(businessDescription),
      location: extractLocation(businessDescription) || 'Kenya'
    }

    // Add cache-busting headers
    const response = NextResponse.json({
      success: true,
      data: {
        website: {
          html: website.html,
          css: '', // CSS is embedded in HTML
          isProduction: true,
          isPureAI: true
        },
        businessProfile,
        designRationale: website.designRationale,
        uniqueFeatures: website.uniqueFeatures,
        kenyaSpecificElements: website.kenyaSpecificElements,
        quality: quality,
        generationTime,
        metadata: {
          generatedAt: new Date().toISOString(),
          model: 'gpt-4o',
          version: '2.0-pure-ai'
        }
      }
    })

    // Aggressive cache busting
    response.headers.set('Cache-Control', 'no-store, no-cache, must-revalidate, proxy-revalidate')
    response.headers.set('Pragma', 'no-cache')
    response.headers.set('Expires', '0')
    response.headers.set('Surrogate-Control', 'no-store')
    response.headers.set('X-Unique-ID', `pure-ai-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`)

    return response

  } catch (error) {
    console.error('❌ Pure AI Generation Error:', error)
    
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to generate website with Pure AI',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}

/**
 * Extract business name from description
 */
function extractBusinessName(description: string): string {
  // Enhanced extraction patterns for Kenyan businesses
  const patterns = [
    /(?:called|named|known as)\s+([A-Z][a-zA-Z\s&]+)/i,
    /^([A-Z][a-zA-Z\s&]+)(?:\s+is|\s+offers|\s+provides|\s+supplies)/i,
    /([A-Z][a-zA-Z\s&]+)(?:\s+salon|\s+restaurant|\s+clinic|\s+shop|\s+hotel|\s+farm)/i,
    /([A-Z][a-zA-Z\s&]+)(?:\s+in\s+[A-Z][a-zA-Z]+)/i
  ]

  for (const pattern of patterns) {
    const match = description.match(pattern)
    if (match && match[1]) {
      return match[1].trim()
    }
  }

  // Fallback: use first few words
  const words = description.split(' ').slice(0, 3)
  return words.join(' ') + ' Business'
}

/**
 * Extract location from description
 */
function extractLocation(description: string): string | null {
  const kenyanCities = [
    'Nairobi', 'Mombasa', 'Kisumu', 'Nakuru', 'Eldoret', 'Thika', 'Malindi', 
    'Kitale', 'Garissa', 'Kakamega', 'Machakos', 'Meru', 'Nyeri', 'Kericho'
  ]
  
  for (const city of kenyanCities) {
    if (description.toLowerCase().includes(city.toLowerCase())) {
      return city
    }
  }
  
  return null
}

/**
 * Infer business type from description
 */
function inferBusinessType(description: string): string {
  const lowerDesc = description.toLowerCase()
  
  const typeMap: Record<string, string> = {
    'salon': 'salon',
    'hair': 'salon',
    'beauty': 'salon',
    'restaurant': 'restaurant',
    'food': 'restaurant',
    'cafe': 'restaurant',
    'clinic': 'clinic',
    'medical': 'clinic',
    'health': 'clinic',
    'shop': 'retail',
    'store': 'retail',
    'boutique': 'retail',
    'hotel': 'hospitality',
    'lodge': 'hospitality',
    'school': 'education',
    'training': 'education',
    'gym': 'fitness',
    'fitness': 'fitness',
    'farm': 'agriculture',
    'farming': 'agriculture',
    'supplies': 'supplier',
    'wholesale': 'supplier'
  }

  for (const [keyword, type] of Object.entries(typeMap)) {
    if (lowerDesc.includes(keyword)) {
      return type
    }
  }

  return 'service'
}

export async function GET() {
  return NextResponse.json({
    message: 'PagesLab Pure AI Website Generator',
    version: '2.0',
    features: [
      'Pure AI generation - no templates',
      'Visually stunning modern designs',
      'Kenyan business optimization',
      'WhatsApp/SMS focused contact',
      'Mobile-first responsive design',
      'Chat-based editing',
      'Quality validation'
    ],
    status: 'active'
  })
}
