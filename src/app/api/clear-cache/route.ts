import { NextRequest, NextResponse } from 'next/server'
import { websiteCache } from '@/lib/cache-service'
import { contentGenerationService } from '@/lib/content-generation'

export async function POST(request: NextRequest) {
  try {
    console.log('🗑️ Clearing all caches for fresh testing...')

    // Clear website cache
    websiteCache.clearAll()

    // Clear content generation cache
    contentGenerationService.clearCache()

    console.log('✅ All server caches cleared successfully')
    console.log('💡 Note: Please also clear browser localStorage manually or refresh the page')

    return NextResponse.json({
      success: true,
      message: 'All server caches cleared successfully. Please refresh your browser to clear localStorage cache.',
      timestamp: new Date().toISOString(),
      instructions: 'Open browser dev tools (F12) → Application tab → Storage → Local Storage → Clear All, then refresh the page'
    })
  } catch (error) {
    console.error('❌ Error clearing caches:', error)
    return NextResponse.json({
      success: false,
      error: 'Failed to clear caches'
    }, { status: 500 })
  }
}
