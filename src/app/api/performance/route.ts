import { NextRequest, NextResponse } from 'next/server'
import { 
  performanceOptimizer, 
  performanceTester, 
  getPerformanceMetrics,
  generatePerformanceReport 
} from '@/lib/performance-optimizer'
import { performanceMonitor } from '@/lib/caching'

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const action = searchParams.get('action') || 'metrics'

    switch (action) {
      case 'metrics':
        const metrics = getPerformanceMetrics()
        return NextResponse.json({
          success: true,
          data: metrics
        })

      case 'report':
        const report = generatePerformanceReport()
        return NextResponse.json({
          success: true,
          data: report
        })

      case 'monitor':
        const monitoringData = performanceMonitor.getAllMetrics()
        return NextResponse.json({
          success: true,
          data: monitoringData
        })

      default:
        return NextResponse.json({
          success: false,
          error: 'Invalid action. Use: metrics, report, or monitor'
        }, { status: 400 })
    }
  } catch (error) {
    console.error('Performance API error:', error)
    return NextResponse.json({
      success: false,
      error: 'Failed to get performance data'
    }, { status: 500 })
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { action, url, websiteData } = body

    switch (action) {
      case 'test':
        if (!url) {
          return NextResponse.json({
            success: false,
            error: 'URL is required for performance testing'
          }, { status: 400 })
        }

        const testResults = await performanceTester.runPerformanceTest(url)
        return NextResponse.json({
          success: true,
          data: testResults
        })

      case 'lighthouse':
        if (!url) {
          return NextResponse.json({
            success: false,
            error: 'URL is required for Lighthouse audit'
          }, { status: 400 })
        }

        const auditResults = await performanceTester.runLighthouseAudit(url)
        return NextResponse.json({
          success: true,
          data: auditResults
        })

      case 'optimize':
        if (!websiteData) {
          return NextResponse.json({
            success: false,
            error: 'Website data is required for optimization'
          }, { status: 400 })
        }

        const optimizedData = await performanceOptimizer.optimizeWebsite(websiteData)
        return NextResponse.json({
          success: true,
          data: optimizedData
        })

      default:
        return NextResponse.json({
          success: false,
          error: 'Invalid action. Use: test, lighthouse, or optimize'
        }, { status: 400 })
    }
  } catch (error) {
    console.error('Performance API error:', error)
    return NextResponse.json({
      success: false,
      error: 'Performance operation failed'
    }, { status: 500 })
  }
}
