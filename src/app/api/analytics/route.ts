import { NextRequest, NextResponse } from 'next/server'
import { analyticsService } from '@/lib/analytics-service'
import { createRateLimit, RATE_LIMIT_CONFIGS } from '@/lib/rate-limiting'
import { requireAuth, requireRole } from '@/lib/auth-middleware'
import { withErrorHandling } from '@/lib/error-handling'

// Rate limiting for analytics endpoints
const analyticsRateLimit = createRateLimit(RATE_LIMIT_CONFIGS.GENERAL)

export async function GET(request: NextRequest) {
  // Apply rate limiting
  const rateLimitResult = await analyticsRateLimit(request)
  if (rateLimitResult) {
    return rateLimitResult
  }

  // Require authentication for analytics access
  const authResult = await requireAuth()(request)
  if (authResult instanceof NextResponse) {
    return authResult
  }

  return withErrorHandling(async () => {
    const { searchParams } = new URL(request.url)
    const action = searchParams.get('action') || 'summary'
    const timeRange = {
      start: parseInt(searchParams.get('start') || '0') || Date.now() - 24 * 60 * 60 * 1000,
      end: parseInt(searchParams.get('end') || '0') || Date.now()
    }

    switch (action) {
      case 'summary':
        const summary = analyticsService.getSummary(timeRange)
        return NextResponse.json({
          success: true,
          data: summary,
          timeRange
        })

      case 'events':
        const eventType = searchParams.get('type') as any
        const limit = parseInt(searchParams.get('limit') || '100')
        
        if (eventType) {
          const events = analyticsService.getEventsByType(eventType, limit)
          return NextResponse.json({
            success: true,
            data: events,
            count: events.length
          })
        } else {
          return NextResponse.json({
            success: false,
            error: 'Event type is required for events action'
          }, { status: 400 })
        }

      case 'export':
        const format = searchParams.get('format') as 'json' | 'csv' || 'json'
        const exportData = analyticsService.exportData(format)
        
        const response = new NextResponse(exportData)
        response.headers.set('Content-Type', format === 'csv' ? 'text/csv' : 'application/json')
        response.headers.set('Content-Disposition', `attachment; filename="pageslab-analytics.${format}"`)
        
        return response

      case 'health':
        return NextResponse.json({
          success: true,
          data: {
            status: 'healthy',
            version: '1.0.0',
            features: [
              'website-generation-tracking',
              'user-behavior-tracking',
              'performance-monitoring',
              'error-tracking',
              'business-metrics'
            ],
            supportedActions: ['summary', 'events', 'export', 'health', 'clear']
          }
        })

      case 'clear':
        // Only allow admins to clear analytics data
        const adminAuthResult = await requireRole(['admin'])(request)
        if (adminAuthResult instanceof NextResponse) {
          return adminAuthResult
        }

        analyticsService.clear()
        return NextResponse.json({
          success: true,
          message: 'Analytics data cleared successfully'
        })

      default:
        return NextResponse.json({
          success: false,
          error: 'Invalid action. Supported actions: summary, events, export, health, clear'
        }, { status: 400 })
    }

  }, {
    component: 'AnalyticsAPI',
    action: 'getAnalytics'
  })
}

export async function POST(request: NextRequest) {
  // Apply rate limiting
  const rateLimitResult = await analyticsRateLimit(request)
  if (rateLimitResult) {
    return rateLimitResult
  }

  // Optional authentication for tracking events
  const authResult = await requireAuth()(request)
  if (authResult instanceof NextResponse) {
    return authResult
  }

  return withErrorHandling(async () => {
    const body = await request.json()
    const { type, data } = body

    if (!type || !data) {
      return NextResponse.json({
        success: false,
        error: 'Type and data are required'
      }, { status: 400 })
    }

    // Set user ID from auth context
    if (authResult && typeof authResult === 'object' && 'user' in authResult) {
      analyticsService.setUserId(authResult.user.id)
    }

    switch (type) {
      case 'website_generation':
        analyticsService.trackWebsiteGeneration(data)
        break

      case 'user_behavior':
        analyticsService.trackUserBehavior(data)
        break

      case 'performance':
        analyticsService.trackPerformance(data)
        break

      case 'error':
        analyticsService.trackError(data)
        break

      case 'business_metric':
        analyticsService.trackBusinessMetric(data)
        break

      default:
        return NextResponse.json({
          success: false,
          error: 'Invalid event type'
        }, { status: 400 })
    }

    return NextResponse.json({
      success: true,
      message: 'Event tracked successfully'
    })

  }, {
    component: 'AnalyticsAPI',
    action: 'trackEvent'
  })
}

// Batch analytics endpoint for multiple events
export async function PUT(request: NextRequest) {
  // Apply rate limiting
  const rateLimitResult = await analyticsRateLimit(request)
  if (rateLimitResult) {
    return rateLimitResult
  }

  // Optional authentication
  const authResult = await requireAuth()(request)
  if (authResult instanceof NextResponse) {
    return authResult
  }

  return withErrorHandling(async () => {
    const body = await request.json()
    const { events } = body

    if (!Array.isArray(events)) {
      return NextResponse.json({
        success: false,
        error: 'Events must be an array'
      }, { status: 400 })
    }

    if (events.length > 100) {
      return NextResponse.json({
        success: false,
        error: 'Maximum 100 events per batch'
      }, { status: 400 })
    }

    // Set user ID from auth context
    if (authResult && typeof authResult === 'object' && 'user' in authResult) {
      analyticsService.setUserId(authResult.user.id)
    }

    let successCount = 0
    let errorCount = 0

    for (const event of events) {
      try {
        const { type, data } = event

        switch (type) {
          case 'website_generation':
            analyticsService.trackWebsiteGeneration(data)
            break
          case 'user_behavior':
            analyticsService.trackUserBehavior(data)
            break
          case 'performance':
            analyticsService.trackPerformance(data)
            break
          case 'error':
            analyticsService.trackError(data)
            break
          case 'business_metric':
            analyticsService.trackBusinessMetric(data)
            break
          default:
            throw new Error(`Invalid event type: ${type}`)
        }

        successCount++
      } catch (error) {
        console.error('Error processing event:', error)
        errorCount++
      }
    }

    return NextResponse.json({
      success: true,
      message: 'Batch events processed',
      results: {
        total: events.length,
        successful: successCount,
        failed: errorCount
      }
    })

  }, {
    component: 'AnalyticsAPI',
    action: 'batchTrackEvents'
  })
}
