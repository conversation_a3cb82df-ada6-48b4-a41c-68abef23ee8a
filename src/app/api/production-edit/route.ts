/**
 * Production-Grade Website Editing API
 * 
 * Multi-stage validation pipeline for safe, precise website editing
 */

import { NextRequest, NextResponse } from 'next/server'
import { ProductionEditingSystem, EditProposal, ValidationResult } from '@/lib/production-editing-system'

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { currentWebsite, editInstructions, stage = 'analyze' } = body

    if (!editInstructions) {
      return NextResponse.json(
        { success: false, error: 'Edit instructions are required' },
        { status: 400 }
      )
    }

    if ((stage === 'propose' || stage === 'validate' || stage === 'apply') && !currentWebsite) {
      return NextResponse.json(
        { success: false, error: 'Current website is required for this stage' },
        { status: 400 }
      )
    }

    const editingSystem = new ProductionEditingSystem()

    switch (stage) {
      case 'analyze':
        return await handleAnalyzeStage(editingSystem, editInstructions)
      
      case 'propose':
        return await handleProposeStage(editingSystem, currentWebsite, editInstructions)
      
      case 'validate':
        const { proposalId } = body
        return await handleValidateStage(editingSystem, currentWebsite, proposalId, body.proposals)
      
      case 'apply':
        const { selectedProposal } = body
        return await handleApplyStage(currentWebsite, selectedProposal)
      
      default:
        return NextResponse.json(
          { success: false, error: 'Invalid stage. Use: analyze, propose, validate, or apply' },
          { status: 400 }
        )
    }

  } catch (error) {
    console.error('❌ Production Edit Error:', error)
    
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to process edit request',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}

/**
 * Stage 1: Analyze edit intent
 */
async function handleAnalyzeStage(
  editingSystem: ProductionEditingSystem,
  editInstructions: string
) {
  console.log('🔍 Stage 1: Analyzing edit intent...')
  
  const intent = await editingSystem.parseEditIntent(editInstructions)
  
  return NextResponse.json({
    success: true,
    stage: 'analyze',
    data: {
      intent,
      nextStage: 'propose',
      message: 'Edit intent analyzed successfully'
    }
  })
}

/**
 * Stage 2: Generate edit proposals
 */
async function handleProposeStage(
  editingSystem: ProductionEditingSystem,
  currentWebsite: string,
  editInstructions: string
) {
  console.log('💡 Stage 2: Generating edit proposals...')
  
  const intent = await editingSystem.parseEditIntent(editInstructions)
  const proposals = await editingSystem.generateEditProposals(
    currentWebsite,
    editInstructions,
    intent
  )
  
  // Sort proposals by confidence and risk level
  const sortedProposals = proposals.sort((a, b) => {
    const scoreA = a.confidence * (a.riskLevel === 'low' ? 1.2 : a.riskLevel === 'high' ? 0.8 : 1.0)
    const scoreB = b.confidence * (b.riskLevel === 'low' ? 1.2 : b.riskLevel === 'high' ? 0.8 : 1.0)
    return scoreB - scoreA
  })
  
  return NextResponse.json({
    success: true,
    stage: 'propose',
    data: {
      intent,
      proposals: sortedProposals,
      nextStage: 'validate',
      message: `Generated ${proposals.length} edit proposals`
    }
  })
}

/**
 * Stage 3: Validate selected proposal
 */
async function handleValidateStage(
  editingSystem: ProductionEditingSystem,
  currentWebsite: string,
  proposalId: string,
  proposals: EditProposal[]
) {
  console.log('✅ Stage 3: Validating proposal...')
  
  const selectedProposal = proposals.find(p => p.id === proposalId)
  if (!selectedProposal) {
    return NextResponse.json(
      { success: false, error: 'Proposal not found' },
      { status: 404 }
    )
  }
  
  const validation = await editingSystem.validateProposal(currentWebsite, selectedProposal)
  const diffPreview = editingSystem.generateDiffPreview(currentWebsite, selectedProposal.previewHtml)
  
  return NextResponse.json({
    success: true,
    stage: 'validate',
    data: {
      proposal: selectedProposal,
      validation,
      diffPreview,
      nextStage: validation.isValid ? 'apply' : 'propose',
      message: validation.isValid 
        ? 'Proposal validated successfully'
        : 'Proposal validation failed - please select another proposal'
    }
  })
}

/**
 * Stage 4: Apply validated proposal
 */
async function handleApplyStage(
  currentWebsite: string,
  selectedProposal: EditProposal
) {
  console.log('🚀 Stage 4: Applying validated proposal...')
  
  // Final safety check
  if (selectedProposal.previewHtml.length < currentWebsite.length * 0.3) {
    return NextResponse.json(
      { success: false, error: 'Final safety check failed - proposal would cause significant content loss' },
      { status: 400 }
    )
  }
  
  // Create backup for rollback
  const backup = {
    html: currentWebsite,
    timestamp: new Date().toISOString(),
    backupId: `backup-${Date.now()}`
  }
  
  return NextResponse.json({
    success: true,
    stage: 'apply',
    data: {
      website: {
        html: selectedProposal.previewHtml,
        css: '', // CSS is embedded in HTML
        isProduction: true,
        isPureAI: true,
        isEdited: true
      },
      appliedChanges: selectedProposal.changes,
      backup,
      editSummary: {
        description: selectedProposal.description,
        riskLevel: selectedProposal.riskLevel,
        confidence: selectedProposal.confidence,
        impact: selectedProposal.estimatedImpact
      },
      message: 'Edit applied successfully'
    }
  })
}

/**
 * GET endpoint for API documentation
 */
export async function GET() {
  return NextResponse.json({
    message: 'Production-Grade Website Editing API',
    version: '2.0.0',
    description: 'Multi-stage validation pipeline for safe, precise website editing',
    stages: [
      {
        name: 'analyze',
        description: 'Parse and understand edit intent',
        input: { editInstructions: 'string' },
        output: { intent: 'EditIntent' }
      },
      {
        name: 'propose',
        description: 'Generate multiple edit proposals',
        input: { currentWebsite: 'string', editInstructions: 'string' },
        output: { proposals: 'EditProposal[]' }
      },
      {
        name: 'validate',
        description: 'Validate selected proposal',
        input: { currentWebsite: 'string', proposalId: 'string', proposals: 'EditProposal[]' },
        output: { validation: 'ValidationResult', diffPreview: 'DiffPreview' }
      },
      {
        name: 'apply',
        description: 'Apply validated proposal',
        input: { selectedProposal: 'EditProposal' },
        output: { website: 'Website', backup: 'Backup' }
      }
    ],
    features: [
      'Multi-stage validation pipeline',
      'Multiple edit proposals with risk assessment',
      'Comprehensive safety checks',
      'Diff preview before applying changes',
      'Automatic backup and rollback capability',
      'Confidence scoring and risk assessment',
      'Content preservation validation'
    ],
    usage: {
      workflow: [
        '1. POST /api/production-edit with stage=analyze',
        '2. POST /api/production-edit with stage=propose',
        '3. POST /api/production-edit with stage=validate',
        '4. POST /api/production-edit with stage=apply'
      ],
      example: {
        analyze: {
          editInstructions: 'Change the header color to blue'
        },
        propose: {
          currentWebsite: '<html>...</html>',
          editInstructions: 'Change the header color to blue'
        },
        validate: {
          currentWebsite: '<html>...</html>',
          proposalId: 'conservative-1234567890',
          proposals: ['...']
        },
        apply: {
          selectedProposal: { id: 'conservative-1234567890', /* ... */ }
        }
      }
    }
  })
}
