import { NextRequest, NextResponse } from 'next/server'
import { PureAIWebsiteGenerator } from '@/lib/pure-ai-generator'

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { currentWebsite, editInstructions, conversationHistory } = body

    if (!currentWebsite || !editInstructions) {
      return NextResponse.json(
        { success: false, error: 'Current website and edit instructions are required' },
        { status: 400 }
      )
    }

    console.log('💬 Starting Chat-Based Website Editing...')
    console.log('📝 Edit Instructions:', editInstructions)
    console.log('📄 Website Length:', currentWebsite.length, 'characters')

    const generator = new PureAIWebsiteGenerator()
    
    const startTime = Date.now()
    
    // Edit website through chat
    const editedWebsite = await generator.editWebsiteWithChat(
      currentWebsite,
      editInstructions
    )

    // Validate quality of edited website
    const quality = await generator.validateWebsiteQuality(editedWebsite)
    
    const editTime = Date.now() - startTime

    console.log('✅ Chat Edit Complete!')
    console.log('⏱️ Edit Time:', editTime, 'ms')
    console.log('📊 Quality Score:', quality.score, '/10')

    // Add to conversation history
    const updatedHistory = [
      ...(conversationHistory || []),
      {
        timestamp: new Date().toISOString(),
        userMessage: editInstructions,
        changes: editedWebsite.uniqueFeatures,
        qualityScore: quality.score
      }
    ]

    const response = NextResponse.json({
      success: true,
      data: {
        website: {
          html: editedWebsite.html,
          css: '', // CSS is embedded in HTML
          isProduction: true,
          isPureAI: true,
          isEdited: true
        },
        designRationale: editedWebsite.designRationale,
        uniqueFeatures: editedWebsite.uniqueFeatures,
        kenyaSpecificElements: editedWebsite.kenyaSpecificElements,
        quality: quality,
        editTime,
        conversationHistory: updatedHistory,
        editSummary: {
          instruction: editInstructions,
          appliedChanges: editedWebsite.uniqueFeatures,
          qualityImprovement: quality.score
        }
      }
    })

    // Cache busting for edited content
    response.headers.set('Cache-Control', 'no-store, no-cache, must-revalidate')
    response.headers.set('X-Edit-ID', `edit-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`)

    return response

  } catch (error) {
    console.error('❌ Chat Edit Error:', error)
    
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to edit website through chat',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}

export async function GET() {
  return NextResponse.json({
    message: 'Chat-Based Website Editor API',
    version: '1.0.0',
    capabilities: [
      'Natural language editing instructions',
      'Contextual website modifications',
      'Quality validation after edits',
      'Conversation history tracking',
      'Kenyan business context preservation'
    ],
    examples: [
      'Make the colors more vibrant and African-inspired',
      'Add a testimonials section with Kenyan customer reviews',
      'Change the layout to be more modern and mobile-friendly',
      'Include more call-to-action buttons for WhatsApp contact',
      'Make the pricing section more prominent'
    ]
  })
}
