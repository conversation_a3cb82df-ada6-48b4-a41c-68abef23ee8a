// Core Business Types
export type BusinessType = 
  | 'SALON'
  | 'RESTAURANT' 
  | 'SHOP'
  | 'CLINIC'
  | 'GARAGE'
  | 'HOTEL'
  | 'SCHOOL'
  | 'CHURCH'
  | 'CONSULTANCY'
  | 'TECH_SERVICES'
  | 'AGRICULTURE'
  | 'TRANSPORT'
  | 'CONSTRUCTION'
  | 'ENTERTAINMENT'
  | 'OTHER'

export type KenyanRegion = 'COASTAL' | 'HIGHLAND' | 'WESTERN' | 'NORTHERN' | 'CENTRAL' | 'EASTERN'

export type Language = 'ENGLISH' | 'SWAHILI' | 'KIKUYU' | 'LUO' | 'LUHYA' | 'KAMBA'

export type SubscriptionTier = 'starter' | 'premium'

// Location Types
export interface KenyanLocation {
  county: string
  area: string
  region: KenyanRegion
  coordinates?: {
    lat: number
    lng: number
  }
}

// Contact Information
export interface ContactInfo {
  phone: string
  whatsapp?: string
  email?: string
  address: string
  businessHours?: string
  website?: string
}

// Cultural Context
export interface CulturalContext {
  region: KenyanRegion
  language: Language
  businessPractices: BusinessPractices
  culturalElements: CulturalElement[]
}

export interface BusinessPractices {
  paymentMethods: PaymentMethod[]
  communicationPreferences: string[]
  businessHours: string
  culturalConsiderations: string[]
}

export interface CulturalElement {
  type: 'COLOR' | 'PATTERN' | 'IMAGERY' | 'LANGUAGE' | 'SYMBOL'
  value: string
  description: string
}

// Payment Methods
export interface PaymentMethod {
  type: 'MPESA' | 'AIRTEL_MONEY' | 'TKASH' | 'BANK_TRANSFER' | 'CASH' | 'CARD'
  displayName: string
  icon?: string
  isActive: boolean
}

// Business Profile
export interface BusinessProfile {
  name: string
  type: BusinessType
  location: KenyanLocation
  services: string[]
  description: string
  targetAudience: string
  contactInfo: ContactInfo
  culturalContext: CulturalContext
}

// Content Generation
export interface Service {
  name: string
  description: string
  price?: string
  duration?: string
}

export interface CallToAction {
  text: string
  type: 'PRIMARY' | 'SECONDARY' | 'CONTACT' | 'WHATSAPP'
  action: string
}

export interface ContactSection {
  title: string
  description: string
  methods: ContactMethod[]
  location?: LocationInfo
}

export interface ContactMethod {
  type: 'PHONE' | 'WHATSAPP' | 'EMAIL' | 'SMS'
  value: string
  label: string
  icon: string
}

export interface LocationInfo {
  address: string
  googleMapsUrl?: string
  directions?: string
  landmarks?: string[]
}

export interface GeneratedContent {
  headline: string
  subheadline: string
  aboutSection: string
  services: Service[]
  callToActions: CallToAction[]
  contactSection: ContactSection

  // Additional properties for enhanced features and backward compatibility
  headlines?: {
    main: string
    tagline: string
    about: string
    services: string
  }
  descriptions?: {
    about: string
    hero: string
  }
  heroSection?: {
    headline: string
    subheadline: string
    ctaText: string
  }
  servicesSection?: {
    title: string
    subtitle: string
    services: Service[]
  }
}

// Design Specifications
export interface ColorScheme {
  primary: string
  secondary: string
  accent: string
  background: string
  text: string
  muted: string
  success: string
  warning: string
  error: string
  // Premium gradient combinations
  gradients?: {
    hero: string
    accent: string
    subtle: string
    dark: string
    animated: {
      hero: string
      floating: string
    }
    glass: {
      light: string
      dark: string
    }
  }
  // Glass-morphism colors
  glassMorphism?: {
    background: string
    border: string
    shadow: string
  }
  // Advanced color variations
  variations?: {
    primaryLight: string
    primaryDark: string
    secondaryLight: string
    secondaryDark: string
  }
}

export interface TypographySpec {
  headingFont: string
  bodyFont: string
  headingSizes: {
    h1: string
    h2: string
    h3: string
    h4?: string
    h5?: string
    h6?: string
  }
  bodySize: string
  lineHeight: string
  // Premium typography features
  fontWeights?: {
    light: string
    normal: string
    medium: string
    semibold: string
    bold: string
    extrabold: string
  }
  letterSpacing?: {
    tight: string
    normal: string
    wide: string
    wider: string
  }
  // Modern text effects
  textEffects?: {
    gradient: boolean
    shadow: boolean
    glow: boolean
  }
}

export interface LayoutStructure {
  sections: LayoutSection[]
  navigation: NavigationSpec
  footer: FooterSpec
}

export interface LayoutSection {
  id: string
  type: 'HERO' | 'ABOUT' | 'SERVICES' | 'CONTACT' | 'GALLERY' | 'TESTIMONIALS'
  order: number
  content: any
  styling: SectionStyling
}

export interface SectionStyling {
  backgroundColor: string
  textColor: string
  padding: string
  margin: string
  alignment: 'LEFT' | 'CENTER' | 'RIGHT'
}

export interface NavigationSpec {
  type: 'HORIZONTAL' | 'VERTICAL' | 'HAMBURGER'
  items: NavItem[]
  styling: NavigationStyling
}

export interface NavItem {
  label: string
  href: string
  isActive?: boolean
}

export interface NavigationStyling {
  backgroundColor: string
  textColor: string
  hoverColor: string
  position: 'FIXED' | 'STATIC' | 'STICKY'
}

export interface FooterSpec {
  content: FooterContent
  styling: FooterStyling
}

export interface FooterContent {
  businessInfo: string
  contactInfo: ContactInfo
  socialLinks?: SocialLink[]
  copyright: string
}

export interface SocialLink {
  platform: string
  url: string
  icon: string
}

export interface FooterStyling {
  backgroundColor: string
  textColor: string
  layout: 'SIMPLE' | 'MULTI_COLUMN'
}

export interface ResponsiveBreakpoints {
  mobile: string
  tablet: string
  desktop: string
  largeDesktop: string
}

export interface DesignSpec {
  colorScheme: ColorScheme
  typography: TypographySpec
  layout: LayoutStructure
  culturalElements: CulturalElement[]
  responsiveBreakpoints: ResponsiveBreakpoints
}

// Website Model
export interface Website {
  id: string
  userId: string
  name: string
  businessProfile: BusinessProfile
  generatedContent: GeneratedContent
  designSpec: DesignSpec
  htmlOutput: string
  cssOutput: string
  subdomain: string
  isPublished: boolean
  createdAt: Date
  updatedAt: Date
}

// User Model
export interface User {
  id: string
  email: string
  subscription: SubscriptionTier
  websites: Website[]
  createdAt: Date
  updatedAt: Date
}

// API Response Types
export interface ApiResponse<T> {
  success: boolean
  data?: T
  error?: string
  message?: string
}

// Generation Process Types
export interface GenerationProgress {
  step: GenerationStep
  progress: number
  message: string
  isComplete: boolean
}

export type GenerationStep = 
  | 'ANALYZING_BUSINESS'
  | 'GENERATING_CONTENT' 
  | 'CREATING_DESIGN'
  | 'BUILDING_WEBSITE'
  | 'OPTIMIZING'
  | 'COMPLETE'

// Error Types
export interface ValidationError {
  type: 'VALIDATION_ERROR'
  field: string
  message: string
  suggestions?: string[]
}

export interface GenerationError {
  type: 'GENERATION_ERROR'
  stage: GenerationStep
  message: string
  retryable: boolean
}

export interface SystemError {
  type: 'SYSTEM_ERROR'
  code: string
  message: string
  timestamp: Date
}

export type AppError = ValidationError | GenerationError | SystemError