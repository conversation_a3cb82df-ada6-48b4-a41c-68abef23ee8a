/**
 * Unit Tests for Business Analysis Engine
 * Tests the core business logic for analyzing business descriptions
 */

describe('Business Analysis Engine', () => {
  // Test business type extraction logic
  describe('Business Type Classification', () => {
    test('should classify restaurant keywords correctly', () => {
      const restaurantKeywords = ['restaurant', 'food', 'dining', 'kitchen', 'cafe']
      const testDescriptions = [
        'I run a restaurant in Nairobi',
        'Local food joint in Kibera',
        'Fine dining establishment'
      ]

      testDescriptions.forEach(description => {
        const hasRestaurantKeyword = restaurantKeywords.some(keyword =>
          description.toLowerCase().includes(keyword)
        )
        expect(hasRestaurantKeyword).toBe(true)
      })
    })

    test('should classify salon keywords correctly', () => {
      const salonKeywords = ['salon', 'beauty', 'hair', 'barber', 'spa']
      const testDescriptions = [
        'Beauty salon in Westlands',
        'Hair salon for women',
        'Barber shop and salon'
      ]

      testDescriptions.forEach(description => {
        const hasSalonKeyword = salonKeywords.some(keyword =>
          description.toLowerCase().includes(keyword)
        )
        expect(hasSalonKeyword).toBe(true)
      })
    })
  })

  // Test location extraction logic
  describe('Location Extraction', () => {
    test('should identify Nairobi areas correctly', () => {
      const nairobiAreas = ['westlands', 'cbd', 'kibera', 'karen', 'eastleigh']
      const testLocations = [
        'Restaurant in Westlands',
        'Shop in CBD',
        'Business in Kibera'
      ]

      testLocations.forEach(location => {
        const hasNairobiArea = nairobiAreas.some(area =>
          location.toLowerCase().includes(area)
        )
        expect(hasNairobiArea).toBe(true)
      })
    })

    test('should identify coastal areas correctly', () => {
      const coastalAreas = ['mombasa', 'diani', 'malindi', 'kilifi']
      const testLocations = [
        'Hotel in Mombasa',
        'Restaurant in Diani',
        'Shop in Malindi'
      ]

      testLocations.forEach(location => {
        const hasCoastalArea = coastalAreas.some(area =>
          location.toLowerCase().includes(area)
        )
        expect(hasCoastalArea).toBe(true)
      })
    })
  })

  // Test contact information extraction
  describe('Contact Information Extraction', () => {
    test('should identify Kenyan phone number patterns', () => {
      const phonePatterns = [
        /0[17]\d{8}/,  // 0712345678, 0722345678
        /\+254[17]\d{8}/, // +254712345678
        /254[17]\d{8}/ // 254712345678
      ]

      const testNumbers = [
        '0712345678',
        '+254712345678',
        '254712345678',
        '0722345678'
      ]

      testNumbers.forEach(number => {
        const isValidKenyanNumber = phonePatterns.some(pattern =>
          pattern.test(number)
        )
        expect(isValidKenyanNumber).toBe(true)
      })
    })

    test('should identify email patterns', () => {
      const emailPattern = /[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}/
      const testEmails = [
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>'
      ]

      testEmails.forEach(email => {
        expect(emailPattern.test(email)).toBe(true)
      })
    })
  })

  // Test service extraction logic
  describe('Service Extraction', () => {
    test('should extract restaurant services', () => {
      const restaurantServices = ['catering', 'takeaway', 'delivery', 'dine-in']
      const description = 'Restaurant offering catering, takeaway, and delivery services'

      const extractedServices = restaurantServices.filter(service =>
        description.toLowerCase().includes(service)
      )

      expect(extractedServices.length).toBeGreaterThan(0)
      expect(extractedServices).toContain('catering')
      expect(extractedServices).toContain('takeaway')
    })

    test('should extract salon services', () => {
      const salonServices = ['cuts', 'styling', 'coloring', 'manicure', 'pedicure']
      const description = 'Hair salon offering cuts, styling, and manicure services'

      const extractedServices = salonServices.filter(service =>
        description.toLowerCase().includes(service)
      )

      expect(extractedServices.length).toBeGreaterThan(0)
      expect(extractedServices).toContain('cuts')
      expect(extractedServices).toContain('styling')
    })
  })

  // Test cultural context
  describe('Cultural Context Analysis', () => {
    test('should identify Swahili terms', () => {
      const swahiliTerms = ['mama', 'baba', 'nyama', 'ugali', 'sukuma', 'karibu']
      const testDescriptions = [
        'Mama Njeri\'s business',
        'Nyama choma restaurant',
        'Karibu to our shop'
      ]

      testDescriptions.forEach(description => {
        const hasSwahiliTerm = swahiliTerms.some(term =>
          description.toLowerCase().includes(term)
        )
        expect(hasSwahiliTerm).toBe(true)
      })
    })

    test('should identify regional food preferences', () => {
      const coastalFoods = ['fish', 'coconut', 'seafood', 'samosa']
      const highlandFoods = ['nyama choma', 'ugali', 'sukuma wiki']

      const coastalDescription = 'Seafood restaurant serving fish and coconut rice'
      const highlandDescription = 'Local restaurant serving nyama choma and ugali'

      const hasCoastalFood = coastalFoods.some(food =>
        coastalDescription.toLowerCase().includes(food)
      )
      const hasHighlandFood = highlandFoods.some(food =>
        highlandDescription.toLowerCase().includes(food)
      )

      expect(hasCoastalFood).toBe(true)
      expect(hasHighlandFood).toBe(true)
    })
  })

  // Test business name generation logic
  describe('Business Name Generation', () => {
    test('should generate appropriate business names', () => {
      const businessTypes = ['restaurant', 'salon', 'shop', 'store']
      const locations = ['westlands', 'cbd', 'kibera']
      const descriptors = ['modern', 'traditional', 'premium', 'local']

      businessTypes.forEach(type => {
        locations.forEach(location => {
          descriptors.forEach(descriptor => {
            const generatedName = `${descriptor} ${type} ${location}`
            expect(generatedName.length).toBeGreaterThan(5)
            expect(generatedName.toLowerCase()).toContain(type)
          })
        })
      })
    })
  })

  // Test error handling
  describe('Error Handling', () => {
    test('should handle empty input gracefully', () => {
      const emptyInputs = ['', '   ', null, undefined]

      emptyInputs.forEach(input => {
        // Test that processing empty input doesn't throw errors
        expect(() => {
          const processed = input ? input.toString().trim() : ''
          return processed.length > 0 ? 'VALID' : 'GENERAL'
        }).not.toThrow()
      })
    })

    test('should handle invalid characters gracefully', () => {
      const invalidInputs = ['!@#$%^&*()', '123456789', 'a'.repeat(1000)]

      invalidInputs.forEach(input => {
        expect(() => {
          // Basic validation logic
          const cleaned = input.replace(/[^a-zA-Z0-9\s]/g, '').trim()
          return cleaned.length > 0 ? 'PROCESSED' : 'INVALID'
        }).not.toThrow()
      })
    })
  })
})
