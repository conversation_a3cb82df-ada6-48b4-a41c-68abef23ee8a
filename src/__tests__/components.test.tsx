/**
 * Component Tests for PagesLab
 * Tests React components and their functionality
 */

import React from 'react'
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import '@testing-library/jest-dom'
import ErrorDisplay from '../components/ErrorDisplay'
import MobileNavigation from '../components/MobileNavigation'
import PWAProvider, { usePWA, useDeviceInfo } from '../components/PWAProvider'

// Mock hooks
jest.mock('../components/PWAProvider', () => ({
  ...jest.requireActual('../components/PWAProvider'),
  useDeviceInfo: jest.fn(),
  usePWA: jest.fn(),
}))

// Mock mobile optimization
jest.mock('../lib/mobile-optimization', () => ({
  mobileOptimization: {
    init: jest.fn(),
    getDeviceInfo: jest.fn().mockReturnValue({
      isMobile: true,
      isTablet: false,
      isDesktop: false,
      isTouch: true,
      orientation: 'portrait',
      screenSize: { width: 375, height: 667 }
    })
  }
}))

describe('Component Tests', () => {
  beforeEach(() => {
    jest.clearAllMocks()
  })

  describe('ErrorDisplay Component', () => {
    const mockError = {
      message: 'Test error message',
      code: 'TEST_ERROR',
      timestamp: new Date(),
      context: { component: 'test' },
      userMessage: 'Something went wrong. Please try again.',
      technicalDetails: 'Error in test component',
      retryable: true
    }

    test('should render error message correctly', () => {
      render(
        <ErrorDisplay
          error={mockError}
          onRetry={jest.fn()}
          onDismiss={jest.fn()}
        />
      )

      expect(screen.getByText('Something went wrong. Please try again.')).toBeInTheDocument()
      expect(screen.getByText('TEST_ERROR')).toBeInTheDocument()
    })

    test('should show retry button when error is retryable', () => {
      const mockRetry = jest.fn()
      
      render(
        <ErrorDisplay
          error={mockError}
          onRetry={mockRetry}
          onDismiss={jest.fn()}
        />
      )

      const retryButton = screen.getByText('Try Again')
      expect(retryButton).toBeInTheDocument()

      fireEvent.click(retryButton)
      expect(mockRetry).toHaveBeenCalled()
    })

    test('should not show retry button when error is not retryable', () => {
      const nonRetryableError = { ...mockError, retryable: false }
      
      render(
        <ErrorDisplay
          error={nonRetryableError}
          onRetry={jest.fn()}
          onDismiss={jest.fn()}
        />
      )

      expect(screen.queryByText('Try Again')).not.toBeInTheDocument()
    })

    test('should show technical details when requested', () => {
      render(
        <ErrorDisplay
          error={mockError}
          onRetry={jest.fn()}
          onDismiss={jest.fn()}
          showDetails={true}
        />
      )

      expect(screen.getByText('Error in test component')).toBeInTheDocument()
    })

    test('should call onDismiss when dismiss button is clicked', () => {
      const mockDismiss = jest.fn()
      
      render(
        <ErrorDisplay
          error={mockError}
          onRetry={jest.fn()}
          onDismiss={mockDismiss}
        />
      )

      const dismissButton = screen.getByLabelText('Dismiss error')
      fireEvent.click(dismissButton)
      expect(mockDismiss).toHaveBeenCalled()
    })

    test('should categorize network errors correctly', () => {
      const networkError = {
        ...mockError,
        code: 'NETWORK_ERROR',
        userMessage: 'Network connection failed'
      }
      
      render(
        <ErrorDisplay
          error={networkError}
          onRetry={jest.fn()}
          onDismiss={jest.fn()}
        />
      )

      expect(screen.getByText('Network connection failed')).toBeInTheDocument()
      expect(screen.getByText('Check your internet connection')).toBeInTheDocument()
    })

    test('should show copy functionality for error details', () => {
      // Mock clipboard API
      Object.assign(navigator, {
        clipboard: {
          writeText: jest.fn().mockResolvedValue(undefined)
        }
      })

      render(
        <ErrorDisplay
          error={mockError}
          onRetry={jest.fn()}
          onDismiss={jest.fn()}
          showDetails={true}
        />
      )

      const copyButton = screen.getByText('Copy Error Details')
      fireEvent.click(copyButton)
      
      expect(navigator.clipboard.writeText).toHaveBeenCalledWith(
        expect.stringContaining('TEST_ERROR')
      )
    })
  })

  describe('MobileNavigation Component', () => {
    const mockUser = {
      id: '1',
      name: 'John Doe',
      email: '<EMAIL>'
    }

    beforeEach(() => {
      (useDeviceInfo as jest.Mock).mockReturnValue({
        isMobile: true,
        isTablet: false,
        isDesktop: false,
        isTouch: true,
        orientation: 'portrait'
      })
    })

    test('should render mobile navigation for mobile devices', () => {
      render(
        <MobileNavigation
          user={mockUser}
          onLogin={jest.fn()}
          onLogout={jest.fn()}
          onDashboard={jest.fn()}
          onCreateNew={jest.fn()}
        />
      )

      expect(screen.getByText('PagesLab')).toBeInTheDocument()
      expect(screen.getByLabelText('Toggle menu')).toBeInTheDocument()
    })

    test('should render desktop navigation for desktop devices', () => {
      (useDeviceInfo as jest.Mock).mockReturnValue({
        isMobile: false,
        isTablet: false,
        isDesktop: true,
        isTouch: false,
        orientation: 'landscape'
      })

      render(
        <MobileNavigation
          user={mockUser}
          onLogin={jest.fn()}
          onLogout={jest.fn()}
          onDashboard={jest.fn()}
          onCreateNew={jest.fn()}
        />
      )

      expect(screen.getByText('Dashboard')).toBeInTheDocument()
      expect(screen.getByText('Create New')).toBeInTheDocument()
    })

    test('should toggle mobile menu when menu button is clicked', () => {
      render(
        <MobileNavigation
          user={mockUser}
          onLogin={jest.fn()}
          onLogout={jest.fn()}
          onDashboard={jest.fn()}
          onCreateNew={jest.fn()}
        />
      )

      const menuButton = screen.getByLabelText('Toggle menu')
      fireEvent.click(menuButton)

      expect(screen.getByText('My Dashboard')).toBeInTheDocument()
      expect(screen.getByText('Create New Website')).toBeInTheDocument()
    })

    test('should show login button when user is not authenticated', () => {
      render(
        <MobileNavigation
          user={null}
          onLogin={jest.fn()}
          onLogout={jest.fn()}
          onDashboard={jest.fn()}
          onCreateNew={jest.fn()}
        />
      )

      const menuButton = screen.getByLabelText('Toggle menu')
      fireEvent.click(menuButton)

      expect(screen.getByText('Login / Sign Up')).toBeInTheDocument()
    })

    test('should call appropriate callbacks when menu items are clicked', () => {
      const mockDashboard = jest.fn()
      const mockCreateNew = jest.fn()
      const mockLogout = jest.fn()

      render(
        <MobileNavigation
          user={mockUser}
          onLogin={jest.fn()}
          onLogout={mockLogout}
          onDashboard={mockDashboard}
          onCreateNew={mockCreateNew}
        />
      )

      // Open menu
      const menuButton = screen.getByLabelText('Toggle menu')
      fireEvent.click(menuButton)

      // Click dashboard
      fireEvent.click(screen.getByText('My Dashboard'))
      expect(mockDashboard).toHaveBeenCalled()

      // Reopen menu and click create new
      fireEvent.click(menuButton)
      fireEvent.click(screen.getByText('Create New Website'))
      expect(mockCreateNew).toHaveBeenCalled()

      // Reopen menu and click logout
      fireEvent.click(menuButton)
      fireEvent.click(screen.getByText('Logout'))
      expect(mockLogout).toHaveBeenCalled()
    })

    test('should highlight current page in navigation', () => {
      render(
        <MobileNavigation
          user={mockUser}
          onLogin={jest.fn()}
          onLogout={jest.fn()}
          onDashboard={jest.fn()}
          onCreateNew={jest.fn()}
          currentPage="dashboard"
        />
      )

      const menuButton = screen.getByLabelText('Toggle menu')
      fireEvent.click(menuButton)

      const dashboardButton = screen.getByText('My Dashboard')
      expect(dashboardButton).toHaveClass('bg-blue-100', 'text-blue-700')
    })

    test('should show bottom navigation for authenticated mobile users', () => {
      render(
        <MobileNavigation
          user={mockUser}
          onLogin={jest.fn()}
          onLogout={jest.fn()}
          onDashboard={jest.fn()}
          onCreateNew={jest.fn()}
        />
      )

      expect(screen.getByText('Home')).toBeInTheDocument()
      expect(screen.getByText('Dashboard')).toBeInTheDocument()
      expect(screen.getByText('Create')).toBeInTheDocument()
    })
  })

  describe('PWAProvider Component', () => {
    beforeEach(() => {
      // Mock service worker
      Object.defineProperty(navigator, 'serviceWorker', {
        writable: true,
        configurable: true,
        value: {
          register: jest.fn().mockResolvedValue({}),
        },
      })

      // Mock online status
      Object.defineProperty(navigator, 'onLine', {
        writable: true,
        configurable: true,
        value: true,
      })
    })

    test('should render children correctly', () => {
      render(
        <PWAProvider>
          <div>Test Content</div>
        </PWAProvider>
      )

      expect(screen.getByText('Test Content')).toBeInTheDocument()
    })

    test('should register service worker on mount', async () => {
      render(
        <PWAProvider>
          <div>Test</div>
        </PWAProvider>
      )

      await waitFor(() => {
        expect(navigator.serviceWorker.register).toHaveBeenCalledWith('/sw.js')
      })
    })

    test('should show install prompt when available', async () => {
      render(
        <PWAProvider>
          <div>Test</div>
        </PWAProvider>
      )

      // Simulate beforeinstallprompt event
      const mockEvent = {
        preventDefault: jest.fn(),
        prompt: jest.fn().mockResolvedValue({ outcome: 'accepted' }),
        userChoice: Promise.resolve({ outcome: 'accepted' })
      }

      fireEvent(window, new CustomEvent('beforeinstallprompt', { detail: mockEvent }))

      await waitFor(() => {
        expect(screen.getByText('Install PagesLab')).toBeInTheDocument()
      })
    })

    test('should show offline indicator when offline', async () => {
      render(
        <PWAProvider>
          <div>Test</div>
        </PWAProvider>
      )

      // Simulate going offline
      Object.defineProperty(navigator, 'onLine', { value: false })
      fireEvent(window, new Event('offline'))

      await waitFor(() => {
        expect(screen.getByText("You're offline. Some features may be limited.")).toBeInTheDocument()
      })
    })

    test('should show back online message when reconnected', async () => {
      render(
        <PWAProvider>
          <div>Test</div>
        </PWAProvider>
      )

      // Simulate going offline then online
      Object.defineProperty(navigator, 'onLine', { value: false })
      fireEvent(window, new Event('offline'))

      Object.defineProperty(navigator, 'onLine', { value: true })
      fireEvent(window, new Event('online'))

      await waitFor(() => {
        expect(screen.getByText('Back online!')).toBeInTheDocument()
      })
    })
  })

  describe('PWA Hooks', () => {
    test('usePWA hook should return correct values', () => {
      const TestComponent = () => {
        const { isInstalled, isOnline, canInstall, isMobile, isTouch } = usePWA()
        return (
          <div>
            <span data-testid="installed">{isInstalled.toString()}</span>
            <span data-testid="online">{isOnline.toString()}</span>
            <span data-testid="can-install">{canInstall.toString()}</span>
            <span data-testid="mobile">{isMobile.toString()}</span>
            <span data-testid="touch">{isTouch.toString()}</span>
          </div>
        )
      }

      // Mock the hook implementation
      (usePWA as jest.Mock).mockReturnValue({
        isInstalled: false,
        isOnline: true,
        canInstall: true,
        isMobile: true,
        isTouch: true
      })

      render(<TestComponent />)

      expect(screen.getByTestId('installed')).toHaveTextContent('false')
      expect(screen.getByTestId('online')).toHaveTextContent('true')
      expect(screen.getByTestId('can-install')).toHaveTextContent('true')
      expect(screen.getByTestId('mobile')).toHaveTextContent('true')
      expect(screen.getByTestId('touch')).toHaveTextContent('true')
    })

    test('useDeviceInfo hook should return device information', () => {
      const TestComponent = () => {
        const { isMobile, isTablet, isDesktop, orientation } = useDeviceInfo()
        return (
          <div>
            <span data-testid="mobile">{isMobile.toString()}</span>
            <span data-testid="tablet">{isTablet.toString()}</span>
            <span data-testid="desktop">{isDesktop.toString()}</span>
            <span data-testid="orientation">{orientation}</span>
          </div>
        )
      }

      render(<TestComponent />)

      expect(screen.getByTestId('mobile')).toHaveTextContent('true')
      expect(screen.getByTestId('tablet')).toHaveTextContent('false')
      expect(screen.getByTestId('desktop')).toHaveTextContent('false')
      expect(screen.getByTestId('orientation')).toHaveTextContent('portrait')
    })
  })

  describe('Component Error Boundaries', () => {
    test('should handle component errors gracefully', () => {
      const ThrowError = () => {
        throw new Error('Test component error')
      }

      const consoleSpy = jest.spyOn(console, 'error').mockImplementation()

      expect(() => {
        render(
          <PWAProvider>
            <ThrowError />
          </PWAProvider>
        )
      }).toThrow('Test component error')

      consoleSpy.mockRestore()
    })
  })

  describe('Accessibility', () => {
    test('should have proper ARIA labels', () => {
      render(
        <MobileNavigation
          user={null}
          onLogin={jest.fn()}
          onLogout={jest.fn()}
          onDashboard={jest.fn()}
          onCreateNew={jest.fn()}
        />
      )

      const menuButton = screen.getByLabelText('Toggle menu')
      expect(menuButton).toHaveAttribute('aria-expanded', 'false')

      fireEvent.click(menuButton)
      expect(menuButton).toHaveAttribute('aria-expanded', 'true')
    })

    test('should support keyboard navigation', () => {
      render(
        <MobileNavigation
          user={null}
          onLogin={jest.fn()}
          onLogout={jest.fn()}
          onDashboard={jest.fn()}
          onCreateNew={jest.fn()}
        />
      )

      const menuButton = screen.getByLabelText('Toggle menu')
      
      // Test Enter key
      fireEvent.keyDown(menuButton, { key: 'Enter', code: 'Enter' })
      // Menu should open (implementation would need to handle this)
      
      // Test Escape key
      fireEvent.keyDown(menuButton, { key: 'Escape', code: 'Escape' })
      // Menu should close (implementation would need to handle this)
    })
  })
})
