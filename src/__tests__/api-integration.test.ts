/**
 * Integration Tests for API Endpoints
 * Tests the main API routes and their functionality
 */

import { NextRequest } from 'next/server'
import { POST as generateWebsiteHandler } from '../app/api/generate-website/route'
import { GET as analyticsGetHandler, POST as analyticsPostHandler } from '../app/api/analytics/route'
import { POST as uploadHandler } from '../app/api/upload/route'

// Mock external dependencies
jest.mock('../lib/business-analysis')
jest.mock('../lib/content-generation')
// html-css-generation removed - using Pure AI only
jest.mock('../lib/image-service')
jest.mock('../lib/rate-limiting')
jest.mock('../lib/analytics-service')

describe('API Integration Tests', () => {
  describe('/api/generate-website', () => {
    test('should generate website successfully with valid input', async () => {
      // Mock the dependencies
      const mockBusinessAnalysis = require('../lib/business-analysis')
      const mockContentGeneration = require('../lib/content-generation')
      // mockHtmlGeneration removed - using Pure AI only
      const mockImageService = require('../lib/image-service')
      const mockRateLimit = require('../lib/rate-limiting')

      mockRateLimit.websiteGenerationRateLimit.mockResolvedValue(null)
      mockBusinessAnalysis.BusinessAnalysisEngine.mockImplementation(() => ({
        analyzeBusinessDescription: jest.fn().mockReturnValue({
          businessProfile: {
            name: 'Test Business',
            type: 'RESTAURANT',
            location: { county: 'Nairobi', area: 'CBD', region: 'HIGHLAND' },
            services: ['Food Service'],
            description: 'Test restaurant',
            targetAudience: 'Local customers',
            contactInfo: { phone: '+254712345678' },
            paymentMethods: ['M-PESA', 'CASH']
          },
          culturalContext: {
            language: 'en',
            region: 'HIGHLAND',
            elements: ['local']
          }
        })
      }))

      mockContentGeneration.ContentGenerationService.mockImplementation(() => ({
        generateCompleteContent: jest.fn().mockResolvedValue({
          hero: { headline: 'Test Headline', subheadline: 'Test Sub', ctaText: 'Contact', ctaUrl: 'tel:+254712345678' },
          services: { title: 'Services', services: [] },
          about: { title: 'About', content: 'Test content', mission: 'Test mission', values: [] },
          testimonials: { title: 'Testimonials', testimonials: [] },
          contact: { title: 'Contact', subtitle: 'Get in touch', contactMethods: [], operatingHours: { title: 'Hours', hours: '9-5', days: 'Mon-Fri' } }
        })
      }))

      // mockHtmlGeneration removed - Pure AI handles generation

      mockImageService.fetchBusinessImages.mockResolvedValue([
        { url: 'https://example.com/image1.jpg', alt: 'Test image 1' },
        { url: 'https://example.com/image2.jpg', alt: 'Test image 2' }
      ])

      const request = new NextRequest('http://localhost:3000/api/generate-website', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          businessInput: 'Test restaurant in Nairobi serving local food',
          includeImages: true,
          integrations: {
            whatsapp: true,
            phone: true,
            email: false,
            googleMaps: false,
            mpesa: true,
            socialMedia: false
          }
        })
      })

      const response = await generateWebsiteHandler(request)
      const data = await response.json()

      expect(response.status).toBe(200)
      expect(data.success).toBe(true)
      expect(data.website).toBeDefined()
      expect(data.website.html).toContain('Test Website')
      expect(data.website.businessProfile.name).toBe('Test Business')
      expect(data.website.images).toHaveLength(2)
    })

    test('should handle rate limiting', async () => {
      const mockRateLimit = require('../lib/rate-limiting')
      mockRateLimit.websiteGenerationRateLimit.mockResolvedValue(
        new Response('Rate limit exceeded', { status: 429 })
      )

      const request = new NextRequest('http://localhost:3000/api/generate-website', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          businessInput: 'Test business',
          includeImages: false,
          integrations: {}
        })
      })

      const response = await generateWebsiteHandler(request)

      expect(response.status).toBe(429)
    })

    test('should handle invalid input', async () => {
      const mockRateLimit = require('../lib/rate-limiting')
      mockRateLimit.websiteGenerationRateLimit.mockResolvedValue(null)

      const request = new NextRequest('http://localhost:3000/api/generate-website', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          // Missing required fields
        })
      })

      const response = await generateWebsiteHandler(request)
      const data = await response.json()

      expect(response.status).toBe(400)
      expect(data.success).toBe(false)
      expect(data.error).toBeDefined()
    })

    test('should handle service errors gracefully', async () => {
      const mockRateLimit = require('../lib/rate-limiting')
      const mockBusinessAnalysis = require('../lib/business-analysis')

      mockRateLimit.websiteGenerationRateLimit.mockResolvedValue(null)
      mockBusinessAnalysis.BusinessAnalysisEngine.mockImplementation(() => ({
        analyzeBusinessDescription: jest.fn().mockImplementation(() => {
          throw new Error('Service unavailable')
        })
      }))

      const request = new NextRequest('http://localhost:3000/api/generate-website', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          businessInput: 'Test business',
          includeImages: false,
          integrations: {}
        })
      })

      const response = await generateWebsiteHandler(request)
      const data = await response.json()

      expect(response.status).toBe(500)
      expect(data.success).toBe(false)
      expect(data.error).toContain('Service unavailable')
    })
  })

  describe('/api/analytics', () => {
    test('should retrieve analytics summary', async () => {
      const mockAnalytics = require('../lib/analytics-service')
      mockAnalytics.getAnalyticsSummary.mockReturnValue({
        totalGenerations: 150,
        successRate: 95.5,
        averageGenerationTime: 12.3,
        popularBusinessTypes: [
          { type: 'RESTAURANT', count: 45 },
          { type: 'SALON', count: 32 }
        ],
        regionDistribution: [
          { region: 'HIGHLAND', count: 89 },
          { region: 'COASTAL', count: 34 }
        ]
      })

      const request = new NextRequest('http://localhost:3000/api/analytics?action=summary')

      const response = await analyticsGetHandler(request)
      const data = await response.json()

      expect(response.status).toBe(200)
      expect(data.success).toBe(true)
      expect(data.data.totalGenerations).toBe(150)
      expect(data.data.successRate).toBe(95.5)
    })

    test('should track analytics events', async () => {
      const mockAnalytics = require('../lib/analytics-service')
      mockAnalytics.trackWebsiteGeneration.mockReturnValue(undefined)

      const request = new NextRequest('http://localhost:3000/api/analytics', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          eventType: 'website_generation',
          data: {
            businessType: 'RESTAURANT',
            region: 'HIGHLAND',
            includeImages: true,
            integrations: ['whatsapp', 'mpesa'],
            generationTime: 15.2,
            success: true
          }
        })
      })

      const response = await analyticsPostHandler(request)
      const data = await response.json()

      expect(response.status).toBe(200)
      expect(data.success).toBe(true)
      expect(mockAnalytics.trackWebsiteGeneration).toHaveBeenCalledWith(
        expect.objectContaining({
          businessType: 'RESTAURANT',
          region: 'HIGHLAND',
          success: true
        })
      )
    })

    test('should handle invalid analytics requests', async () => {
      const request = new NextRequest('http://localhost:3000/api/analytics', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          // Missing required fields
        })
      })

      const response = await analyticsPostHandler(request)
      const data = await response.json()

      expect(response.status).toBe(400)
      expect(data.success).toBe(false)
    })
  })

  describe('/api/upload', () => {
    test('should handle file upload successfully', async () => {
      // Create a mock file
      const mockFile = new File(['test content'], 'test.jpg', { type: 'image/jpeg' })
      const formData = new FormData()
      formData.append('file', mockFile)

      const request = new NextRequest('http://localhost:3000/api/upload', {
        method: 'POST',
        body: formData
      })

      // Mock file processing
      const mockProcessing = jest.fn().mockResolvedValue({
        filename: 'test.jpg',
        size: 1024,
        type: 'image/jpeg',
        url: '/uploads/test.jpg'
      })

      const response = await uploadHandler(request)
      const data = await response.json()

      expect(response.status).toBe(200)
      expect(data.success).toBe(true)
      expect(data.files).toBeDefined()
    })

    test('should reject unsupported file types', async () => {
      const mockFile = new File(['test content'], 'test.exe', { type: 'application/exe' })
      const formData = new FormData()
      formData.append('file', mockFile)

      const request = new NextRequest('http://localhost:3000/api/upload', {
        method: 'POST',
        body: formData
      })

      const response = await uploadHandler(request)
      const data = await response.json()

      expect(response.status).toBe(400)
      expect(data.success).toBe(false)
      expect(data.error).toContain('Unsupported file type')
    })

    test('should reject files that are too large', async () => {
      // Create a mock large file (simulate 50MB)
      const largeContent = 'x'.repeat(50 * 1024 * 1024)
      const mockFile = new File([largeContent], 'large.jpg', { type: 'image/jpeg' })
      const formData = new FormData()
      formData.append('file', mockFile)

      const request = new NextRequest('http://localhost:3000/api/upload', {
        method: 'POST',
        body: formData
      })

      const response = await uploadHandler(request)
      const data = await response.json()

      expect(response.status).toBe(400)
      expect(data.success).toBe(false)
      expect(data.error).toContain('File too large')
    })
  })

  describe('Error Handling Across APIs', () => {
    test('should handle network timeouts', async () => {
      const mockRateLimit = require('../lib/rate-limiting')
      const mockBusinessAnalysis = require('../lib/business-analysis')

      mockRateLimit.websiteGenerationRateLimit.mockResolvedValue(null)
      mockBusinessAnalysis.BusinessAnalysisEngine.mockImplementation(() => ({
        analyzeBusinessDescription: jest.fn().mockImplementation(() => {
          return new Promise((_, reject) => {
            setTimeout(() => reject(new Error('Request timeout')), 100)
          })
        })
      }))

      const request = new NextRequest('http://localhost:3000/api/generate-website', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          businessInput: 'Test business',
          includeImages: false,
          integrations: {}
        })
      })

      const response = await generateWebsiteHandler(request)
      const data = await response.json()

      expect(response.status).toBe(500)
      expect(data.success).toBe(false)
    })

    test('should handle malformed JSON requests', async () => {
      const request = new NextRequest('http://localhost:3000/api/generate-website', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: 'invalid json{'
      })

      const response = await generateWebsiteHandler(request)
      const data = await response.json()

      expect(response.status).toBe(400)
      expect(data.success).toBe(false)
      expect(data.error).toContain('Invalid JSON')
    })
  })

  describe('Performance Tests', () => {
    test('should complete website generation within time limit', async () => {
      const mockRateLimit = require('../lib/rate-limiting')
      const mockBusinessAnalysis = require('../lib/business-analysis')
      const mockContentGeneration = require('../lib/content-generation')
      // mockHtmlGeneration removed - using Pure AI only

      // Mock fast responses
      mockRateLimit.websiteGenerationRateLimit.mockResolvedValue(null)
      mockBusinessAnalysis.BusinessAnalysisEngine.mockImplementation(() => ({
        analyzeBusinessDescription: jest.fn().mockReturnValue({
          businessProfile: { name: 'Test', type: 'RESTAURANT', location: {}, services: [], contactInfo: {} },
          culturalContext: { language: 'en', region: 'HIGHLAND', elements: [] }
        })
      }))
      mockContentGeneration.ContentGenerationService.mockImplementation(() => ({
        generateCompleteContent: jest.fn().mockResolvedValue({
          hero: {}, services: {}, about: {}, testimonials: {}, contact: {}
        })
      }))
      // Pure AI handles HTML generation directly

      const startTime = Date.now()

      const request = new NextRequest('http://localhost:3000/api/generate-website', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          businessInput: 'Test business',
          includeImages: false,
          integrations: {}
        })
      })

      const response = await generateWebsiteHandler(request)
      const endTime = Date.now()
      const duration = endTime - startTime

      expect(response.status).toBe(200)
      expect(duration).toBeLessThan(30000) // Should complete within 30 seconds
    })
  })
})
