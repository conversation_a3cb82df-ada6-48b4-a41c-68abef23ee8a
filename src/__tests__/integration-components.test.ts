import { describe, it, expect } from '@jest/globals'
import { 
  formatKenyanPhone,
  PaymentMethodDisplay 
} from '../components/PaymentMethods'
import { 
  formatKenyanAddress,
  validateKenyanPostalCode,
  formatKenyanPostalCode,
  getCountyInfo,
  createGoogleMapsUrl,
  normalizeKenyanAddress
} from '../lib/kenyan-address-formatter'

describe('Payment Methods', () => {
  describe('formatKenyanPhone', () => {
    it('should format phone numbers starting with 254', () => {
      expect(formatKenyanPhone('254712345678')).toBe('+254712345678')
    })

    it('should format phone numbers starting with 0', () => {
      expect(formatKenyanPhone('0712345678')).toBe('+254712345678')
    })

    it('should format 9-digit phone numbers', () => {
      expect(formatKenyanPhone('712345678')).toBe('+254712345678')
    })

    it('should handle phone numbers with spaces and dashes', () => {
      expect(formatKenyanPhone('0712-345-678')).toBe('+254712345678')
      expect(formatKenyanPhone('0712 345 678')).toBe('+254712345678')
    })

    it('should return original if format is unrecognized', () => {
      expect(formatKenyanPhone('123')).toBe('123')
      expect(formatKenyanPhone('invalid')).toBe('invalid')
    })
  })
})

describe('Kenyan Address Formatter', () => {
  const sampleAddress = {
    businessName: 'Test Business',
    area: 'Westlands',
    county: 'Nairobi',
    postalCode: '00100',
    landmark: 'Near Sarit Centre'
  }

  describe('formatKenyanAddress', () => {
    it('should format a complete address correctly', () => {
      const formatted = formatKenyanAddress(sampleAddress)
      
      expect(formatted.singleLine).toContain('Test Business')
      expect(formatted.singleLine).toContain('Westlands')
      expect(formatted.singleLine).toContain('Nairobi County')
      expect(formatted.singleLine).toContain('Kenya')
      
      expect(formatted.multiLine).toEqual([
        'Test Business',
        'Westlands',
        'Nairobi County',
        '00100',
        'Kenya'
      ])
      
      expect(formatted.shortForm).toBe('Westlands, Nairobi')
      expect(formatted.postal).toBe('00100')
    })

    it('should handle minimal address information', () => {
      const minimalAddress = {
        area: 'CBD',
        county: 'Nairobi'
      }
      
      const formatted = formatKenyanAddress(minimalAddress)
      
      expect(formatted.singleLine).toBe('CBD, Nairobi County, Kenya')
      expect(formatted.shortForm).toBe('CBD, Nairobi')
    })

    it('should include P.O. Box when provided', () => {
      const addressWithPOBox = {
        ...sampleAddress,
        poBox: '12345'
      }
      
      const formatted = formatKenyanAddress(addressWithPOBox)
      
      expect(formatted.singleLine).toContain('P.O. Box 12345')
    })
  })

  describe('validateKenyanPostalCode', () => {
    it('should validate correct 5-digit postal codes', () => {
      expect(validateKenyanPostalCode('00100')).toBe(true)
      expect(validateKenyanPostalCode('20100')).toBe(true)
      expect(validateKenyanPostalCode('80100')).toBe(true)
    })

    it('should reject invalid postal codes', () => {
      expect(validateKenyanPostalCode('123')).toBe(false)
      expect(validateKenyanPostalCode('123456')).toBe(false)
      expect(validateKenyanPostalCode('abcde')).toBe(false)
      expect(validateKenyanPostalCode('')).toBe(false)
    })

    it('should handle postal codes with non-digit characters', () => {
      expect(validateKenyanPostalCode('00-100')).toBe(true)
      expect(validateKenyanPostalCode('00 100')).toBe(true)
    })
  })

  describe('formatKenyanPostalCode', () => {
    it('should format valid postal codes', () => {
      expect(formatKenyanPostalCode('00-100')).toBe('00100')
      expect(formatKenyanPostalCode('00 100')).toBe('00100')
    })

    it('should return original for invalid codes', () => {
      expect(formatKenyanPostalCode('123')).toBe('123')
      expect(formatKenyanPostalCode('invalid')).toBe('invalid')
    })
  })

  describe('getCountyInfo', () => {
    it('should return correct info for major counties', () => {
      const nairobiInfo = getCountyInfo('Nairobi')
      
      expect(nairobiInfo.name).toBe('Nairobi')
      expect(nairobiInfo.region).toBe('Central')
      expect(nairobiInfo.capital).toBe('Nairobi')
      expect(nairobiInfo.description).toContain('Capital')
      expect(nairobiInfo.majorTowns).toContain('Nairobi')
      expect(nairobiInfo.postalCodes).toContain('00100')
    })

    it('should return default info for unknown counties', () => {
      const unknownInfo = getCountyInfo('Unknown County')
      
      expect(unknownInfo.name).toBe('Unknown County')
      expect(unknownInfo.region).toBe('Unknown')
      expect(unknownInfo.description).toContain('County in Kenya')
    })
  })

  describe('createGoogleMapsUrl', () => {
    it('should create search URL by default', () => {
      const url = createGoogleMapsUrl(sampleAddress)
      
      expect(url).toContain('google.com/maps/search')
      expect(url).toContain('Test%20Business')
      expect(url).toContain('Westlands')
      expect(url).toContain('Nairobi')
    })

    it('should create directions URL when specified', () => {
      const url = createGoogleMapsUrl(sampleAddress, 'directions')
      
      expect(url).toContain('google.com/maps/dir')
      expect(url).toContain('destination=')
    })
  })

  describe('normalizeKenyanAddress', () => {
    it('should trim whitespace from all fields', () => {
      const messyAddress = {
        businessName: '  Test Business  ',
        area: '  Westlands  ',
        county: '  Nairobi  ',
        postalCode: '  00100  '
      }
      
      const normalized = normalizeKenyanAddress(messyAddress)
      
      expect(normalized.businessName).toBe('Test Business')
      expect(normalized.area).toBe('Westlands')
      expect(normalized.county).toBe('Nairobi')
      expect(normalized.postalCode).toBe('00100')
    })

    it('should handle missing fields gracefully', () => {
      const partialAddress = {
        area: 'CBD'
      }
      
      const normalized = normalizeKenyanAddress(partialAddress)
      
      expect(normalized.area).toBe('CBD')
      expect(normalized.county).toBe('')
      expect(normalized.businessName).toBeUndefined()
    })
  })
})

describe('Contact Methods Integration', () => {
  const businessProfile = {
    name: 'Test Business',
    contactInfo: {
      phone: '0712345678',
      email: '<EMAIL>'
    },
    location: {
      area: 'Westlands',
      county: 'Nairobi'
    }
  }

  it('should generate correct WhatsApp URL', () => {
    const phone = '0712345678'
    const businessName = 'Test Business'
    const expectedUrl = `https://wa.me/254712345678?text=Hello%20Test%20Business,%20I%20would%20like%20to%20inquire%20about%20your%20services.`
    
    // This would be tested in the component
    expect(phone.replace(/[^0-9]/g, '')).toBe('0712345678')
  })

  it('should generate correct call URL', () => {
    const phone = '0712345678'
    const expectedUrl = `tel:${phone}`
    
    expect(expectedUrl).toBe('tel:0712345678')
  })

  it('should generate correct SMS URL', () => {
    const phone = '0712345678'
    const businessName = 'Test Business'
    const expectedUrl = `sms:${phone}?body=Hello%20Test%20Business,%20I%20would%20like%20to%20inquire%20about%20your%20services.`
    
    expect(expectedUrl).toContain('sms:0712345678')
    expect(expectedUrl).toContain('Test%20Business')
  })
})

describe('Location Services Integration', () => {
  const location = {
    area: 'Westlands',
    county: 'Nairobi',
    region: 'Central',
    landmark: 'Near Sarit Centre'
  }

  it('should format location display correctly', () => {
    const formatted = formatKenyanAddress(location)
    
    expect(formatted.shortForm).toBe('Westlands, Nairobi')
    expect(formatted.singleLine).toContain('Nairobi County')
  })

  it('should generate correct Google Maps URLs', () => {
    const searchUrl = createGoogleMapsUrl(location, 'search')
    const directionsUrl = createGoogleMapsUrl(location, 'directions')
    
    expect(searchUrl).toContain('google.com/maps/search')
    expect(directionsUrl).toContain('google.com/maps/dir')
    expect(directionsUrl).toContain('destination=')
  })
})

// Mock data for testing
export const mockBusinessProfiles = {
  restaurant: {
    name: 'Mama Njeri\'s Kitchen',
    type: 'RESTAURANT',
    contactInfo: {
      phone: '0712345678',
      email: '<EMAIL>'
    },
    location: {
      area: 'Westlands',
      county: 'Nairobi',
      region: 'Central',
      landmark: 'Near Sarit Centre'
    },
    services: ['Local cuisine', 'Catering', 'Takeaway']
  },
  salon: {
    name: 'Beauty Palace Salon',
    type: 'SALON',
    contactInfo: {
      phone: '**********',
      email: '<EMAIL>'
    },
    location: {
      area: 'CBD',
      county: 'Nairobi',
      region: 'Central',
      landmark: 'Near Kencom House'
    },
    services: ['Hair styling', 'Manicure', 'Pedicure', 'Facial treatments']
  },
  clinic: {
    name: 'Afya Medical Clinic',
    type: 'CLINIC',
    contactInfo: {
      phone: '**********',
      email: '<EMAIL>'
    },
    location: {
      area: 'Karen',
      county: 'Nairobi',
      region: 'Central',
      landmark: 'Near Karen Shopping Centre'
    },
    services: ['General consultation', 'Laboratory tests', 'Pharmacy', 'Vaccination']
  }
}
