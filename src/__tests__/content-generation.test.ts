/**
 * Unit Tests for Content Generation Service
 * Tests AI-powered content generation for websites
 */

import { ContentGenerationService } from '../lib/content-generation'
import type { BusinessProfile, GeneratedContent } from '../types'

// Mock OpenAI
jest.mock('openai', () => ({
  OpenAI: jest.fn().mockImplementation(() => ({
    chat: {
      completions: {
        create: jest.fn()
      }
    }
  }))
}))

describe('ContentGenerationService', () => {
  let service: ContentGenerationService
  let mockOpenAI: any

  const mockBusinessProfile: BusinessProfile = {
    name: 'Mama Njeri\'s Salon',
    type: 'SALON',
    location: {
      county: 'Nairobi',
      area: 'Westlands',
      region: 'HIGHLAND'
    },
    services: ['Hair Styling', 'Manicures', 'Pedicures'],
    description: 'Professional beauty salon offering quality hair and nail services',
    targetAudience: 'Women aged 18-45 in Westlands area',
    contactInfo: {
      phone: '+254712345678',
      email: '<EMAIL>',
      address: 'Westlands Shopping Centre, Nairobi'
    },
    paymentMethods: ['M-PESA', 'CASH', 'CARD'],
    operatingHours: '9:00 AM - 6:00 PM',
    socialMedia: {
      facebook: 'MamaNjeriSalon',
      instagram: '@mamanjeri_salon'
    }
  }

  beforeEach(() => {
    service = new ContentGenerationService()
    mockOpenAI = require('openai').OpenAI()
  })

  afterEach(() => {
    jest.clearAllMocks()
  })

  describe('generateHeroContent', () => {
    test('should generate compelling hero content', async () => {
      const mockResponse = {
        choices: [{
          message: {
            content: JSON.stringify({
              headline: 'Transform Your Look at Mama Njeri\'s Salon',
              subheadline: 'Professional beauty services in the heart of Westlands',
              ctaText: 'Book Your Appointment',
              ctaUrl: 'tel:+254712345678'
            })
          }
        }]
      }

      mockOpenAI.chat.completions.create.mockResolvedValue(mockResponse)

      const result = await service.generateHeroContent(mockBusinessProfile)

      expect(result.headline).toContain('Mama Njeri')
      expect(result.subheadline).toContain('Westlands')
      expect(result.ctaText).toBeTruthy()
      expect(result.ctaUrl).toContain('+254712345678')
    })

    test('should handle different business types', async () => {
      const restaurantProfile = {
        ...mockBusinessProfile,
        name: 'Nyama Choma Palace',
        type: 'RESTAURANT' as const,
        services: ['Grilled Meat', 'Local Dishes', 'Catering']
      }

      const mockResponse = {
        choices: [{
          message: {
            content: JSON.stringify({
              headline: 'Authentic Kenyan Flavors at Nyama Choma Palace',
              subheadline: 'Experience the best grilled meat in Westlands',
              ctaText: 'Order Now',
              ctaUrl: 'tel:+254712345678'
            })
          }
        }]
      }

      mockOpenAI.chat.completions.create.mockResolvedValue(mockResponse)

      const result = await service.generateHeroContent(restaurantProfile)

      expect(result.headline).toContain('Nyama Choma')
      expect(result.subheadline).toContain('grilled meat')
    })
  })

  describe('generateServicesContent', () => {
    test('should generate detailed services content', async () => {
      const mockResponse = {
        choices: [{
          message: {
            content: JSON.stringify({
              title: 'Our Professional Services',
              services: [
                {
                  name: 'Hair Styling',
                  description: 'Expert hair styling for all occasions',
                  price: 'From KSh 1,500',
                  icon: 'scissors'
                },
                {
                  name: 'Manicures',
                  description: 'Professional nail care and design',
                  price: 'From KSh 800',
                  icon: 'hand'
                }
              ]
            })
          }
        }]
      }

      mockOpenAI.chat.completions.create.mockResolvedValue(mockResponse)

      const result = await service.generateServicesContent(mockBusinessProfile)

      expect(result.title).toBeTruthy()
      expect(result.services).toHaveLength(2)
      expect(result.services[0].name).toBe('Hair Styling')
      expect(result.services[0].price).toContain('KSh')
    })

    test('should include all business services', async () => {
      const mockResponse = {
        choices: [{
          message: {
            content: JSON.stringify({
              title: 'Our Services',
              services: mockBusinessProfile.services.map(service => ({
                name: service,
                description: `Professional ${service.toLowerCase()} service`,
                price: 'Contact for pricing',
                icon: 'star'
              }))
            })
          }
        }]
      }

      mockOpenAI.chat.completions.create.mockResolvedValue(mockResponse)

      const result = await service.generateServicesContent(mockBusinessProfile)

      expect(result.services).toHaveLength(mockBusinessProfile.services.length)
      mockBusinessProfile.services.forEach(service => {
        expect(result.services.some(s => s.name === service)).toBe(true)
      })
    })
  })

  describe('generateAboutContent', () => {
    test('should generate compelling about content', async () => {
      const mockResponse = {
        choices: [{
          message: {
            content: JSON.stringify({
              title: 'About Mama Njeri\'s Salon',
              content: 'Located in the heart of Westlands, Mama Njeri\'s Salon has been serving the community with professional beauty services. Our experienced team is dedicated to helping you look and feel your best.',
              mission: 'To provide exceptional beauty services that enhance our clients\' natural beauty and confidence.',
              values: ['Quality Service', 'Customer Satisfaction', 'Professional Excellence']
            })
          }
        }]
      }

      mockOpenAI.chat.completions.create.mockResolvedValue(mockResponse)

      const result = await service.generateAboutContent(mockBusinessProfile)

      expect(result.title).toContain('Mama Njeri')
      expect(result.content).toContain('Westlands')
      expect(result.mission).toBeTruthy()
      expect(result.values).toHaveLength(3)
    })
  })

  describe('generateTestimonialsContent', () => {
    test('should generate realistic testimonials', async () => {
      const mockResponse = {
        choices: [{
          message: {
            content: JSON.stringify({
              title: 'What Our Clients Say',
              testimonials: [
                {
                  name: 'Grace Wanjiku',
                  location: 'Westlands',
                  content: 'Amazing service! The staff is professional and the results are always perfect.',
                  rating: 5,
                  service: 'Hair Styling'
                },
                {
                  name: 'Mary Akinyi',
                  location: 'Nairobi',
                  content: 'Best salon in Westlands. I always leave feeling beautiful and confident.',
                  rating: 5,
                  service: 'Manicures'
                }
              ]
            })
          }
        }]
      }

      mockOpenAI.chat.completions.create.mockResolvedValue(mockResponse)

      const result = await service.generateTestimonialsContent(mockBusinessProfile)

      expect(result.title).toBeTruthy()
      expect(result.testimonials).toHaveLength(2)
      expect(result.testimonials[0].rating).toBe(5)
      expect(result.testimonials[0].name).toContain('Grace')
    })

    test('should include diverse Kenyan names', async () => {
      const mockResponse = {
        choices: [{
          message: {
            content: JSON.stringify({
              title: 'Customer Reviews',
              testimonials: [
                { name: 'Grace Wanjiku', location: 'Westlands', content: 'Great service!', rating: 5, service: 'Hair Styling' },
                { name: 'Mary Akinyi', location: 'Nairobi', content: 'Excellent work!', rating: 5, service: 'Manicures' },
                { name: 'Faith Njeri', location: 'CBD', content: 'Highly recommend!', rating: 5, service: 'Pedicures' }
              ]
            })
          }
        }]
      }

      mockOpenAI.chat.completions.create.mockResolvedValue(mockResponse)

      const result = await service.generateTestimonialsContent(mockBusinessProfile)

      const names = result.testimonials.map(t => t.name)
      expect(names).toContain('Grace Wanjiku')
      expect(names).toContain('Mary Akinyi')
      expect(names).toContain('Faith Njeri')
    })
  })

  describe('generateContactContent', () => {
    test('should generate comprehensive contact content', async () => {
      const mockResponse = {
        choices: [{
          message: {
            content: JSON.stringify({
              title: 'Get In Touch',
              subtitle: 'Ready to transform your look? Contact us today!',
              contactMethods: [
                { type: 'phone', label: 'Call Us', value: '+254712345678', icon: 'phone' },
                { type: 'email', label: 'Email Us', value: '<EMAIL>', icon: 'mail' },
                { type: 'address', label: 'Visit Us', value: 'Westlands Shopping Centre, Nairobi', icon: 'map-pin' }
              ],
              operatingHours: {
                title: 'Operating Hours',
                hours: '9:00 AM - 6:00 PM',
                days: 'Monday - Saturday'
              }
            })
          }
        }]
      }

      mockOpenAI.chat.completions.create.mockResolvedValue(mockResponse)

      const result = await service.generateContactContent(mockBusinessProfile)

      expect(result.title).toBeTruthy()
      expect(result.contactMethods).toHaveLength(3)
      expect(result.contactMethods[0].value).toBe('+254712345678')
      expect(result.operatingHours.hours).toBe('9:00 AM - 6:00 PM')
    })
  })

  describe('generateCompleteContent', () => {
    test('should generate all content sections', async () => {
      // Mock all the individual content generation methods
      const mockResponses = {
        hero: {
          headline: 'Test Headline',
          subheadline: 'Test Subheadline',
          ctaText: 'Contact Us',
          ctaUrl: 'tel:+254712345678'
        },
        services: {
          title: 'Our Services',
          services: [{ name: 'Test Service', description: 'Test Description', price: 'KSh 1000', icon: 'star' }]
        },
        about: {
          title: 'About Us',
          content: 'Test content',
          mission: 'Test mission',
          values: ['Value 1']
        },
        testimonials: {
          title: 'Testimonials',
          testimonials: [{ name: 'Test User', location: 'Nairobi', content: 'Great!', rating: 5, service: 'Test' }]
        },
        contact: {
          title: 'Contact',
          subtitle: 'Get in touch',
          contactMethods: [{ type: 'phone', label: 'Phone', value: '+254712345678', icon: 'phone' }],
          operatingHours: { title: 'Hours', hours: '9-5', days: 'Mon-Fri' }
        }
      }

      mockOpenAI.chat.completions.create
        .mockResolvedValueOnce({ choices: [{ message: { content: JSON.stringify(mockResponses.hero) } }] })
        .mockResolvedValueOnce({ choices: [{ message: { content: JSON.stringify(mockResponses.services) } }] })
        .mockResolvedValueOnce({ choices: [{ message: { content: JSON.stringify(mockResponses.about) } }] })
        .mockResolvedValueOnce({ choices: [{ message: { content: JSON.stringify(mockResponses.testimonials) } }] })
        .mockResolvedValueOnce({ choices: [{ message: { content: JSON.stringify(mockResponses.contact) } }] })

      const result = await service.generateCompleteContent(mockBusinessProfile)

      expect(result.hero).toEqual(mockResponses.hero)
      expect(result.services).toEqual(mockResponses.services)
      expect(result.about).toEqual(mockResponses.about)
      expect(result.testimonials).toEqual(mockResponses.testimonials)
      expect(result.contact).toEqual(mockResponses.contact)
    })
  })

  describe('Error Handling', () => {
    test('should handle OpenAI API errors gracefully', async () => {
      mockOpenAI.chat.completions.create.mockRejectedValue(new Error('API Error'))

      await expect(service.generateHeroContent(mockBusinessProfile)).rejects.toThrow('API Error')
    })

    test('should handle invalid JSON responses', async () => {
      const mockResponse = {
        choices: [{
          message: {
            content: 'Invalid JSON content'
          }
        }]
      }

      mockOpenAI.chat.completions.create.mockResolvedValue(mockResponse)

      await expect(service.generateHeroContent(mockBusinessProfile)).rejects.toThrow()
    })

    test('should handle empty responses', async () => {
      const mockResponse = {
        choices: []
      }

      mockOpenAI.chat.completions.create.mockResolvedValue(mockResponse)

      await expect(service.generateHeroContent(mockBusinessProfile)).rejects.toThrow()
    })
  })

  describe('Content Quality', () => {
    test('should generate culturally appropriate content', async () => {
      const mockResponse = {
        choices: [{
          message: {
            content: JSON.stringify({
              headline: 'Karibu to Mama Njeri\'s Salon',
              subheadline: 'Professional beauty services in Westlands',
              ctaText: 'Piga Simu Sasa',
              ctaUrl: 'tel:+254712345678'
            })
          }
        }]
      }

      mockOpenAI.chat.completions.create.mockResolvedValue(mockResponse)

      const result = await service.generateHeroContent(mockBusinessProfile)

      expect(result.headline).toContain('Karibu')
      expect(result.ctaText).toContain('Piga Simu')
    })

    test('should include appropriate pricing in KSh', async () => {
      const mockResponse = {
        choices: [{
          message: {
            content: JSON.stringify({
              title: 'Our Services',
              services: [
                { name: 'Hair Cut', description: 'Professional hair cutting', price: 'KSh 1,500', icon: 'scissors' }
              ]
            })
          }
        }]
      }

      mockOpenAI.chat.completions.create.mockResolvedValue(mockResponse)

      const result = await service.generateServicesContent(mockBusinessProfile)

      expect(result.services[0].price).toContain('KSh')
    })
  })
})
