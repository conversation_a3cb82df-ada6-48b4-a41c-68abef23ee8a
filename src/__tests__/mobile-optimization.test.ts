/**
 * Unit Tests for Mobile Optimization Service
 * Tests mobile-specific features and PWA functionality
 */

import { mobileOptimization } from '../lib/mobile-optimization'

// Mock DOM APIs
Object.defineProperty(window, 'innerWidth', {
  writable: true,
  configurable: true,
  value: 375, // iPhone width
})

Object.defineProperty(window, 'innerHeight', {
  writable: true,
  configurable: true,
  value: 667, // iPhone height
})

Object.defineProperty(window, 'devicePixelRatio', {
  writable: true,
  configurable: true,
  value: 2,
})

Object.defineProperty(navigator, 'maxTouchPoints', {
  writable: true,
  configurable: true,
  value: 5,
})

Object.defineProperty(navigator, 'onLine', {
  writable: true,
  configurable: true,
  value: true,
})

// Mock IntersectionObserver
global.IntersectionObserver = jest.fn().mockImplementation((callback) => ({
  observe: jest.fn(),
  unobserve: jest.fn(),
  disconnect: jest.fn(),
}))

// Mock service worker
Object.defineProperty(navigator, 'serviceWorker', {
  writable: true,
  configurable: true,
  value: {
    register: jest.fn().mockResolvedValue({}),
    ready: Promise.resolve({}),
  },
})

describe('Mobile Optimization Service', () => {
  beforeEach(() => {
    // Reset DOM
    document.head.innerHTML = ''
    document.body.innerHTML = ''
    document.body.className = ''
    
    // Reset window properties
    window.innerWidth = 375
    window.innerHeight = 667
    
    // Clear all event listeners
    jest.clearAllMocks()
  })

  describe('Device Detection', () => {
    test('should detect touch device correctly', () => {
      // Simulate touch device
      Object.defineProperty(window, 'ontouchstart', {
        writable: true,
        configurable: true,
        value: {},
      })

      mobileOptimization.init()

      expect(document.body.classList.contains('touch-device')).toBe(true)
    })

    test('should detect non-touch device correctly', () => {
      // Remove touch support
      delete (window as any).ontouchstart
      Object.defineProperty(navigator, 'maxTouchPoints', {
        writable: true,
        configurable: true,
        value: 0,
      })

      mobileOptimization.init()

      expect(document.body.classList.contains('no-touch')).toBe(true)
    })

    test('should get correct device information', () => {
      const deviceInfo = mobileOptimization.getDeviceInfo()

      expect(deviceInfo.isMobile).toBe(true) // 375px width
      expect(deviceInfo.isTablet).toBe(false)
      expect(deviceInfo.isDesktop).toBe(false)
      expect(deviceInfo.orientation).toBe('portrait') // height > width
      expect(deviceInfo.screenSize.width).toBe(375)
      expect(deviceInfo.screenSize.height).toBe(667)
      expect(deviceInfo.pixelRatio).toBe(2)
    })

    test('should detect tablet correctly', () => {
      window.innerWidth = 800
      window.innerHeight = 600

      const deviceInfo = mobileOptimization.getDeviceInfo()

      expect(deviceInfo.isMobile).toBe(false)
      expect(deviceInfo.isTablet).toBe(true)
      expect(deviceInfo.isDesktop).toBe(false)
      expect(deviceInfo.orientation).toBe('landscape')
    })

    test('should detect desktop correctly', () => {
      window.innerWidth = 1200
      window.innerHeight = 800

      const deviceInfo = mobileOptimization.getDeviceInfo()

      expect(deviceInfo.isMobile).toBe(false)
      expect(deviceInfo.isTablet).toBe(false)
      expect(deviceInfo.isDesktop).toBe(true)
    })
  })

  describe('Touch Optimization', () => {
    test('should add touch optimization styles', () => {
      mobileOptimization.init({ touchOptimization: true })

      const styles = document.head.querySelector('style')
      expect(styles).toBeTruthy()
      expect(styles?.textContent).toContain('touch-action: manipulation')
      expect(styles?.textContent).toContain('min-height: 44px')
    })

    test('should register touch gestures', () => {
      const mockElement = document.createElement('div')
      const mockCallback = jest.fn()

      mobileOptimization.registerGesture('test-tap', {
        type: 'tap',
        element: mockElement,
        callback: mockCallback
      })

      // Simulate touch events
      const touchStart = new TouchEvent('touchstart', {
        touches: [{ clientX: 100, clientY: 100 } as Touch]
      })
      const touchEnd = new TouchEvent('touchend', {
        changedTouches: [{ clientX: 105, clientY: 105 } as Touch]
      })

      mockElement.dispatchEvent(touchStart)
      setTimeout(() => {
        mockElement.dispatchEvent(touchEnd)
        expect(mockCallback).toHaveBeenCalled()
      }, 100)
    })

    test('should handle swipe gestures', () => {
      const mockElement = document.createElement('div')
      const mockCallback = jest.fn()

      mobileOptimization.registerGesture('test-swipe', {
        type: 'swipe',
        direction: 'left',
        element: mockElement,
        callback: mockCallback
      })

      // Simulate swipe left
      const touchStart = new TouchEvent('touchstart', {
        touches: [{ clientX: 200, clientY: 100 } as Touch]
      })
      const touchEnd = new TouchEvent('touchend', {
        changedTouches: [{ clientX: 100, clientY: 100 } as Touch]
      })

      mockElement.dispatchEvent(touchStart)
      setTimeout(() => {
        mockElement.dispatchEvent(touchEnd)
        expect(mockCallback).toHaveBeenCalled()
      }, 200)
    })
  })

  describe('Viewport Optimization', () => {
    test('should set viewport meta tag', () => {
      mobileOptimization.init({ viewportOptimization: true })

      const viewport = document.head.querySelector('meta[name="viewport"]') as HTMLMetaElement
      expect(viewport).toBeTruthy()
      expect(viewport.content).toContain('width=device-width')
      expect(viewport.content).toContain('initial-scale=1.0')
      expect(viewport.content).toContain('viewport-fit=cover')
    })

    test('should add safe area CSS variables', () => {
      mobileOptimization.init({ viewportOptimization: true })

      const styles = document.head.querySelector('style')
      expect(styles?.textContent).toContain('--safe-area-inset-top')
      expect(styles?.textContent).toContain('env(safe-area-inset-top')
    })

    test('should fix viewport height', () => {
      mobileOptimization.init()

      // Check if CSS custom property is set
      const vh = window.innerHeight * 0.01
      expect(document.documentElement.style.getPropertyValue('--vh')).toBe(`${vh}px`)
    })
  })

  describe('Orientation Handling', () => {
    test('should handle orientation changes', () => {
      const mockCallback = jest.fn()
      mobileOptimization.onOrientationChange(mockCallback)

      // Simulate orientation change
      window.innerWidth = 667
      window.innerHeight = 375
      window.dispatchEvent(new Event('orientationchange'))

      setTimeout(() => {
        expect(mockCallback).toHaveBeenCalledWith('landscape')
        expect(document.body.getAttribute('data-orientation')).toBe('landscape')
      }, 100)
    })

    test('should detect portrait orientation', () => {
      window.innerWidth = 375
      window.innerHeight = 667

      const deviceInfo = mobileOptimization.getDeviceInfo()
      expect(deviceInfo.orientation).toBe('portrait')
    })

    test('should detect landscape orientation', () => {
      window.innerWidth = 667
      window.innerHeight = 375

      const deviceInfo = mobileOptimization.getDeviceInfo()
      expect(deviceInfo.orientation).toBe('landscape')
    })
  })

  describe('Performance Optimization', () => {
    test('should optimize animations for mobile', () => {
      mobileOptimization.init({ performanceOptimization: true })

      const styles = document.head.querySelector('style')
      expect(styles?.textContent).toContain('@media (max-width: 768px)')
      expect(styles?.textContent).toContain('animation-duration: 0.3s')
    })

    test('should respect reduced motion preference', () => {
      mobileOptimization.init({ performanceOptimization: true })

      const styles = document.head.querySelector('style')
      expect(styles?.textContent).toContain('@media (prefers-reduced-motion: reduce)')
      expect(styles?.textContent).toContain('animation-duration: 0.01ms')
    })

    test('should setup intersection observer', () => {
      mobileOptimization.init({ performanceOptimization: true })

      expect(IntersectionObserver).toHaveBeenCalled()
    })

    test('should optimize images for mobile', () => {
      // Add test images to DOM
      document.body.innerHTML = `
        <img src="test1.jpg" alt="Test 1">
        <img src="test2.jpg" alt="Test 2" loading="eager">
      `

      mobileOptimization.optimizeImagesForMobile()

      const images = document.querySelectorAll('img')
      images.forEach(img => {
        expect(img.getAttribute('loading')).toBe('lazy')
        expect(img.getAttribute('decoding')).toBe('async')
        expect(img.getAttribute('sizes')).toContain('100vw')
      })
    })
  })

  describe('Accessibility Enhancements', () => {
    test('should add skip links', () => {
      mobileOptimization.init({ accessibilityEnhancements: true })

      const skipLink = document.body.querySelector('a[href="#main-content"]')
      expect(skipLink).toBeTruthy()
      expect(skipLink?.textContent).toBe('Skip to main content')
    })

    test('should improve focus management', () => {
      const mockScrollIntoView = jest.fn()
      Element.prototype.scrollIntoView = mockScrollIntoView

      mobileOptimization.init({ accessibilityEnhancements: true })

      const input = document.createElement('input')
      document.body.appendChild(input)

      // Simulate focus on touch device
      Object.defineProperty(window, 'ontouchstart', { value: {} })
      input.dispatchEvent(new FocusEvent('focusin'))

      expect(mockScrollIntoView).toHaveBeenCalledWith({
        behavior: 'smooth',
        block: 'center'
      })
    })
  })

  describe('PWA Features', () => {
    test('should setup PWA features', () => {
      const mockAddEventListener = jest.spyOn(window, 'addEventListener')
      
      mobileOptimization.init({ offlineSupport: true })

      expect(mockAddEventListener).toHaveBeenCalledWith('beforeinstallprompt', expect.any(Function))
      expect(mockAddEventListener).toHaveBeenCalledWith('appinstalled', expect.any(Function))
    })

    test('should handle install prompt', () => {
      mobileOptimization.setupPWAFeatures()

      const mockEvent = {
        preventDefault: jest.fn(),
        prompt: jest.fn().mockResolvedValue({ outcome: 'accepted' }),
        userChoice: Promise.resolve({ outcome: 'accepted' })
      }

      // Simulate beforeinstallprompt event
      window.dispatchEvent(new CustomEvent('beforeinstallprompt', { detail: mockEvent }))

      expect(mockEvent.preventDefault).toHaveBeenCalled()
    })
  })

  describe('Configuration Options', () => {
    test('should respect configuration options', () => {
      const config = {
        touchOptimization: false,
        gestureSupport: false,
        orientationHandling: false,
        viewportOptimization: false,
        performanceOptimization: false,
        accessibilityEnhancements: false,
        offlineSupport: false
      }

      mobileOptimization.init(config)

      // Should not add touch optimization styles when disabled
      const styles = document.head.querySelectorAll('style')
      const hasTouchStyles = Array.from(styles).some(style => 
        style.textContent?.includes('touch-action: manipulation')
      )
      expect(hasTouchStyles).toBe(false)
    })

    test('should use default configuration when none provided', () => {
      const consoleSpy = jest.spyOn(console, 'log').mockImplementation()
      
      mobileOptimization.init()

      expect(consoleSpy).toHaveBeenCalledWith(
        'Mobile optimization initialized with config:',
        expect.objectContaining({
          touchOptimization: true,
          gestureSupport: true,
          orientationHandling: true,
          viewportOptimization: true,
          performanceOptimization: true,
          accessibilityEnhancements: true,
          offlineSupport: true
        })
      )

      consoleSpy.mockRestore()
    })
  })

  describe('Error Handling', () => {
    test('should handle missing IntersectionObserver gracefully', () => {
      // Remove IntersectionObserver
      delete (global as any).IntersectionObserver

      expect(() => {
        mobileOptimization.init({ performanceOptimization: true })
      }).not.toThrow()
    })

    test('should handle orientation change callback errors', () => {
      const errorCallback = jest.fn().mockImplementation(() => {
        throw new Error('Callback error')
      })
      const consoleErrorSpy = jest.spyOn(console, 'error').mockImplementation()

      mobileOptimization.onOrientationChange(errorCallback)

      // Trigger orientation change
      window.dispatchEvent(new Event('orientationchange'))

      expect(consoleErrorSpy).toHaveBeenCalledWith(
        'Orientation change callback error:',
        expect.any(Error)
      )

      consoleErrorSpy.mockRestore()
    })
  })
})
