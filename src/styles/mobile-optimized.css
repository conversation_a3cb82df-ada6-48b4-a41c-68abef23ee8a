/* Mobile-First CSS Framework for PagesLab */

/* CSS Custom Properties for Mobile */
:root {
  /* Viewport units that work on mobile - fallback value to prevent hydration mismatch */
  --vh: 1vh;
  --vw: 1vw;
  
  /* Safe area insets for notched devices */
  --safe-area-inset-top: env(safe-area-inset-top, 0px);
  --safe-area-inset-right: env(safe-area-inset-right, 0px);
  --safe-area-inset-bottom: env(safe-area-inset-bottom, 0px);
  --safe-area-inset-left: env(safe-area-inset-left, 0px);
  
  /* Touch target sizes */
  --touch-target-min: 44px;
  --touch-target-comfortable: 48px;
  
  /* Mobile spacing scale */
  --space-xs: 0.25rem;
  --space-sm: 0.5rem;
  --space-md: 1rem;
  --space-lg: 1.5rem;
  --space-xl: 2rem;
  --space-2xl: 3rem;
  
  /* Mobile typography scale */
  --text-xs: 0.75rem;
  --text-sm: 0.875rem;
  --text-base: 1rem;
  --text-lg: 1.125rem;
  --text-xl: 1.25rem;
  --text-2xl: 1.5rem;
  --text-3xl: 1.875rem;
  --text-4xl: 2.25rem;
  
  /* Mobile-optimized line heights */
  --leading-tight: 1.25;
  --leading-normal: 1.5;
  --leading-relaxed: 1.75;
  
  /* Mobile breakpoints */
  --bp-sm: 640px;
  --bp-md: 768px;
  --bp-lg: 1024px;
  --bp-xl: 1280px;
}

/* Base mobile styles */
* {
  box-sizing: border-box;
}

html {
  /* Fix for mobile viewport height issues */
  height: 100%;
  height: calc(var(--vh, 1vh) * 100);
  
  /* Smooth scrolling */
  scroll-behavior: smooth;
  
  /* Prevent horizontal scroll */
  overflow-x: hidden;
  
  /* Better text rendering */
  -webkit-text-size-adjust: 100%;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

body {
  margin: 0;
  padding: 0;
  min-height: 100%;
  min-height: calc(var(--vh, 1vh) * 100);
  overflow-x: hidden;
  
  /* Better touch scrolling on iOS */
  -webkit-overflow-scrolling: touch;
}

/* Touch device optimizations */
.touch-device {
  /* Remove tap highlights */
  -webkit-tap-highlight-color: transparent;
  
  /* Improve touch responsiveness */
  touch-action: manipulation;
}

/* Touch targets */
button,
.btn,
[role="button"],
input[type="checkbox"],
input[type="radio"],
select {
  min-height: var(--touch-target-min);
  min-width: var(--touch-target-min);
  touch-action: manipulation;
}

/* Comfortable touch targets for primary actions */
.btn-primary,
.btn-cta {
  min-height: var(--touch-target-comfortable);
  padding: var(--space-md) var(--space-lg);
}

/* Mobile-first responsive containers */
.container {
  width: 100%;
  max-width: 100%;
  margin: 0 auto;
  padding: 0 var(--space-md);
}

@media (min-width: 640px) {
  .container {
    max-width: 640px;
    padding: 0 var(--space-lg);
  }
}

@media (min-width: 768px) {
  .container {
    max-width: 768px;
    padding: 0 var(--space-xl);
  }
}

@media (min-width: 1024px) {
  .container {
    max-width: 1024px;
  }
}

@media (min-width: 1280px) {
  .container {
    max-width: 1280px;
  }
}

/* Mobile-optimized grid system */
.grid {
  display: grid;
  gap: var(--space-md);
}

.grid-1 { grid-template-columns: 1fr; }
.grid-2 { grid-template-columns: 1fr; }
.grid-3 { grid-template-columns: 1fr; }
.grid-4 { grid-template-columns: 1fr; }

@media (min-width: 640px) {
  .grid-2 { grid-template-columns: repeat(2, 1fr); }
  .grid-4 { grid-template-columns: repeat(2, 1fr); }
}

@media (min-width: 768px) {
  .grid-3 { grid-template-columns: repeat(2, 1fr); }
}

@media (min-width: 1024px) {
  .grid-3 { grid-template-columns: repeat(3, 1fr); }
  .grid-4 { grid-template-columns: repeat(4, 1fr); }
}

/* Mobile typography */
.text-responsive {
  font-size: var(--text-base);
  line-height: var(--leading-normal);
}

@media (min-width: 768px) {
  .text-responsive {
    font-size: var(--text-lg);
  }
}

@media (min-width: 1024px) {
  .text-responsive {
    font-size: var(--text-xl);
  }
}

/* Heading responsive scales */
.heading-1 {
  font-size: var(--text-2xl);
  line-height: var(--leading-tight);
  font-weight: 700;
}

@media (min-width: 768px) {
  .heading-1 {
    font-size: var(--text-3xl);
  }
}

@media (min-width: 1024px) {
  .heading-1 {
    font-size: var(--text-4xl);
  }
}

.heading-2 {
  font-size: var(--text-xl);
  line-height: var(--leading-tight);
  font-weight: 600;
}

@media (min-width: 768px) {
  .heading-2 {
    font-size: var(--text-2xl);
  }
}

@media (min-width: 1024px) {
  .heading-2 {
    font-size: var(--text-3xl);
  }
}

/* Mobile navigation */
.mobile-nav {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 50;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
  padding: var(--safe-area-inset-top) var(--space-md) var(--space-md);
}

.mobile-menu {
  position: fixed;
  top: 0;
  left: -100%;
  width: 80%;
  max-width: 320px;
  height: 100%;
  background: white;
  z-index: 60;
  transition: left 0.3s ease;
  padding: var(--safe-area-inset-top) var(--space-md) var(--space-md);
  overflow-y: auto;
}

.mobile-menu.open {
  left: 0;
}

.mobile-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 55;
  opacity: 0;
  visibility: hidden;
  transition: opacity 0.3s ease, visibility 0.3s ease;
}

.mobile-overlay.open {
  opacity: 1;
  visibility: visible;
}

/* Mobile forms */
.form-mobile {
  display: flex;
  flex-direction: column;
  gap: var(--space-md);
}

.form-mobile input,
.form-mobile textarea,
.form-mobile select {
  width: 100%;
  min-height: var(--touch-target-min);
  padding: var(--space-md);
  border: 2px solid #e5e7eb;
  border-radius: 8px;
  font-size: var(--text-base);
  
  /* Prevent zoom on iOS */
  font-size: 16px;
}

.form-mobile input:focus,
.form-mobile textarea:focus,
.form-mobile select:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* Mobile cards */
.card-mobile {
  background: white;
  border-radius: 12px;
  padding: var(--space-lg);
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  margin-bottom: var(--space-md);
}

/* Mobile buttons */
.btn-mobile {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--space-sm);
  width: 100%;
  min-height: var(--touch-target-comfortable);
  padding: var(--space-md) var(--space-lg);
  border: none;
  border-radius: 8px;
  font-size: var(--text-base);
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  touch-action: manipulation;
}

.btn-mobile:active {
  transform: scale(0.98);
}

/* Safe area utilities */
.safe-top {
  padding-top: var(--safe-area-inset-top);
}

.safe-bottom {
  padding-bottom: var(--safe-area-inset-bottom);
}

.safe-left {
  padding-left: var(--safe-area-inset-left);
}

.safe-right {
  padding-right: var(--safe-area-inset-right);
}

.safe-all {
  padding-top: var(--safe-area-inset-top);
  padding-right: var(--safe-area-inset-right);
  padding-bottom: var(--safe-area-inset-bottom);
  padding-left: var(--safe-area-inset-left);
}

/* Mobile-specific utilities */
.mobile-only {
  display: block;
}

.desktop-only {
  display: none;
}

@media (min-width: 768px) {
  .mobile-only {
    display: none;
  }
  
  .desktop-only {
    display: block;
  }
}

/* Scroll optimization */
.scroll-smooth {
  scroll-behavior: smooth;
  -webkit-overflow-scrolling: touch;
}

/* Image optimization for mobile */
.img-mobile {
  width: 100%;
  height: auto;
  object-fit: cover;
  border-radius: 8px;
}

.img-mobile[loading="lazy"] {
  opacity: 0;
  transition: opacity 0.3s ease;
}

.img-mobile[loading="lazy"].loaded {
  opacity: 1;
}

/* Accessibility improvements for mobile */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .card-mobile {
    border: 2px solid;
  }
  
  .btn-mobile {
    border: 2px solid;
  }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .card-mobile {
    background: #1f2937;
    color: white;
  }
  
  .mobile-nav {
    background: rgba(31, 41, 55, 0.95);
    color: white;
  }
}

/* Print styles for mobile */
@media print {
  .mobile-nav,
  .mobile-menu,
  .mobile-overlay,
  .btn-mobile {
    display: none !important;
  }
  
  .container {
    max-width: none;
    padding: 0;
  }
}
